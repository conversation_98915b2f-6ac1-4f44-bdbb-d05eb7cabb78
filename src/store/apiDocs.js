import api from '@/api'
import faq from '@/api/faq'
import { getItems, filterList, handleRespParams } from '@/utils/initMenu'
const detailVer = window.localStorage.getItem('detailVer') || 'new'
export default {
  namespaced: true,
  state: {
    menuCode: '',
    keyWord: '',
    currentApiId: '',
    subMenuApiId: '',
    detailLoading: false,
    detailVer,
    menuList: [],
    itemsList: [],
    apiOverviewlist: [],
    menuMap: {},
    scenesMsg: '',
    invokingMsg: '',
    filterMenuList: [],
    definition: {},
    common: {},
    resHeader: {}, // 应该放在头里面的响应参数，要是下载接口
    errcode: [],
    callbackList: [],
    historyList: [],
    apiFaqList: [],
    spiApiMapData: {}, // spi api 关联数据
    apiBasicData: {}, // api 基本信息
    showSpiApiData: [], // spiapi 关联需要展示的数据
    apifoxMap: {},
    operationId: ''
  },
  mutations: {
    setShowComponents(state, payload) {
      state.showComponents = payload
    },
    setMenuActive(state, payload) {
      state.menuCode = payload
    },
    setDefinition(state, payload) {
      state.definition = payload
    },
    setKeyWord(state, payload) {
      state.keyWord = payload
    },
    setSubMenuActive(state, payload) {
      if(!payload) return
      // 匹配 页面编码
      let payloadArr = payload.split('/')
      let currentPage = payloadArr[payloadArr.length - 1]
      if (!payload) {
        state.subMenuApiId = ''
        return
      }
      this.commit('apiDocs/setHistoryList', [])
      let item = {}
      // 优化链匹配 忽略 /post__、  /get__  、/options__
      let ignorePageNo = `/${currentPage}`
      ignorePageNo = ignorePageNo.replace(/(\/post__|\/get__|\/options__)/, function($0, $1) {
        return {
          '/post__' : '',
          '/get__' : '',
          '/options__' : '',
        }[$1]
      })
      // 没有文档编码时 默认第一个
      item = state.itemsList.find(menu => menu.location.includes(ignorePageNo))
      if(payloadArr.length > 1) {
        let val1 = state.itemsList.find(menu => menu.location === payload)
        let val2 = state.itemsList.find(menu => new RegExp(`${payloadArr[0]}.*?${ignorePageNo}$`).test(menu.location))
        if (val1) {
          item = val1
        } else if(val2) {
          item = val2
        }
      }
      if (!item) {
        state.subMenuApiId = ''
        state.currentApiId = ''
        state.operationId = ''
        return
      }
      state.subMenuApiId = item.location
      state.currentApiId = item.apiId
      state.operationId = item.operationId
      this.commit('apiDocs/setMenuActive', item.openKeys)
      this.dispatch('apiDocs/getApiExtend', {
        docNo: item.docNo,
        pageNo: item.pageNo
      })
      this.dispatch('apiDocs/init', {
        apiId: item.apiId,
        spiCount: item.spiCount
      })
      document.title = `易宝开放平台-${item.title}`
      // historyCount
      if (item.historyCount > 1) {
        // 当前版本是历史版本，根据父apiId拿历史
        if (item.latestRef) {
          this.dispatch('apiDocs/gethistoryList', item.latestRef)
          return
        }
        this.dispatch('apiDocs/gethistoryList', item.apiId)
      }
    },
    setMenuList(state, payload) {
      state.menuList = payload
    },
    setShowSpiApiData(state, payload) {
      state.showSpiApiData = payload
    },
    setItemsList(state, payload) {
      state.itemsList = payload
    },
    setFilterMenuList(state, payload) {
      state.filterMenuList = payload
    },
    setFilterMenuItems(state, payload) {
      const items = state.filterMenuList.find(
        item => item.code === state.menuCode
      ).items
      items.push(...payload)
    },
    setHistoryList(state, payload) {
      state.historyList = payload
    },
    setDetailVer(state, payload) {
      state.detailVer = payload
      window.localStorage.setItem('detailVer', payload)
    },
  },
  actions: {
    init({ dispatch, state, rootState }, { apiId, spiCount }) {
      if (!rootState.loading) state.detailLoading = true
      const list = [
        // dispatch('getSpiApiMap', apiId),
        dispatch('getDefinition', apiId),
        dispatch('getErrcode', apiId),
        dispatch('getApiFaqList', apiId),
        dispatch('getApifoxMap'),
      ]
      if (spiCount > 0) {
        list.push(dispatch('getSpi', apiId))
      } else {
        state.callbackList = []
      }
      Promise.all(list).then(() => {
        state.detailLoading = false
      })
    },
    getMenu({ commit }, location) {
      commit('setLoading', true, { root: true })
      commit('setDefinition', {})
      api
        .getMenuTree()
        .then(res => {
          const list = res
          // 过滤空菜单
          const newList = filterList(list)
          commit('setMenuList', newList)
          commit('setFilterMenuList', newList)
          commit('setItemsList', getItems(newList))
          commit('setSubMenuActive', location)
        })
        .finally(() => {
          commit('setLoading', false, { root: true })
        })
    },
    getConent({ commit }, location) {
      commit('setDefinition', {})
      commit('setSubMenuActive', location)
    },
    gethistoryList({ commit }, apiId) {
      api
        .getHistory({
          apiId
        })
        .then(res => {
          commit('setHistoryList', res)
        })
    },
    getErrcode({ state }, apiId) {
      api
        .getErrcode({
          apiId
        })
        .then(res => {
          state.errcode = res
          state.pageNo = 1
        })
    },
    getDefinition({ state, dispatch }, apiId) {
      api
        .getDefinition({
          apiId
        })
        .then(res => {
          state.definition = res
          const { resHeader, resBody } = handleRespParams(res.respParams)
          state.definition.respParams = resBody
          state.resHeader = resHeader
          dispatch('getCommon', res.commonRef)
        })
    },
    // 获取spi api 关联数据
    getSpiApiMap({ state, commit }, { apiId, spi} ) {
      api.getSpiApiMap({
        apiId: apiId
      })
      .then(res => {
        state.spiApiMapData = res
        api.getApibasic()
          .then(resData => {
            state.apiBasicData = resData
            let showSpiApiData = []
            if(state.spiApiMapData[spi]) {
              for (let key in resData) {
                state.spiApiMapData[spi].forEach((item) => {
                  if(key === item) {
                    showSpiApiData.push(resData[key])
                  }
                })
              }
            }
            commit('setShowSpiApiData', showSpiApiData)
          })
        })
    },
    getSpi({ state, dispatch }, apiId) {
      state.callbackList = []
      api
        .getSpi({
          apiId
        })
        .then(res => {
          state.callbackList = res
          if (res && res[0]) {
            dispatch('getSpiApiMap', {
              apiId: apiId,
              spi: res[0].name
            })
          }
        })
    },
    getApiExtend({ state }, params) {
      api
        .getApiExtend({
          docNo: params.docNo,
          pageNo: params.pageNo
        })
        .then(res => {
          let newPageNo = `/${params.pageNo}`
          // 匹配到 /post__、  /get__ 这两个 如果失败了，改为/options__ 再发一次请求
          if (newPageNo.includes('/post__') || newPageNo.includes('/get__')) {
            if (res.length == 0) {
              this.reGetApiExtend({ state }, params)
            }
          }
          const invoking = res.find(r => r.id === 'invoking')
          const scenes = res.find(r => r.id === 'scenes')
          state.scenesMsg = scenes ? scenes.data : ''
          state.invokingMsg = invoking
            ? `<div class="markdown-con">${invoking.data}</div>`
            : ''
        })
    },
    reGetApiExtend({ state }, params) {
      if (params.pageNo.includes('/post__')) {
        params.pageNo = params.pageNo.replace('/post__', '/options__')
      }
      if (params.pageNo.includes('/get__')) {
        params.pageNo = params.pageNo.replace('/get__', '/options__')
      }
      api.getApiExtend({
        docNo: params.docNo,
        pageNo: params.pageNo
      })
        .then(res => {
          const invoking = res.find(r => r.id === 'invoking')
          const scenes = res.find(r => r.id === 'scenes')
          state.scenesMsg = scenes ? scenes.data : ''
          state.invokingMsg = invoking
            ? `<div class="markdown-con">${invoking.data}</div>`
            : ''
      })
    },
    getCommon({ state }, refName) {
      api
        .getCommon({
          refName
        })
        .then(res => {
          state.common = res
        })
    },
    getApiFaqList({ state }, apiId) {
      state.apiFaqList = []
      faq.getApiFaqList({
          apiId
        })
        .then(res => {
          state.apiFaqList = res
        })
    },
    getApifoxMap({ state }) {
      state.apifoxMap = {}
      api.getApifoxMap()
        .then(res => {
          state.apifoxMap = res
        })
    }
  },
}
