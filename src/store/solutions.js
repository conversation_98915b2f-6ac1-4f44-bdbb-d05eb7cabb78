import api from '@/api'
import solutions from '@/api/solutions'
import faq from '@/api/faq'
import utils from '@/utils'
import { handleRespParams } from '@/utils/initMenu'
export default {
  namespaced: true,
  state: {
    menuList: [],
    currentLocation: '',
    upCurrentLocation: [],
    pageFaqList: [],
    contentType: 'MD', //JSON、MD、LINK, 区分渲染逻辑
    templateId: '', //区分渲染模板,API列表页、SPI列表页
    docInfo: {},
    loading: '',
    detailLoading: false,
    content: '',
    docNo: '',
    html: '',
    menuCode: '',
    keyWord: '',
    currentApiId: '',
    subMenuApiId: '',
    itemsList: [],
    apiOverviewlist: [],
    menuMap: {},
    scenesMsg: '',
    invokingMsg: '',
    filterMenuList: [],
    definition: {},
    common: {},
    resHeader: {}, // 应该放在头里面的响应参数，要是下载接口
    errcode: [],
    callbackList: [],
    historyList: [],
    apiFaqList: []
  },
  mutations: {
    setContentType(state, payload) {
      state.contentType = payload
    },
    setTemplateId(state, payload) {
      state.templateId = payload
    },
    setMenuList(state, payload) {
      state.menuList = payload
    },
    setUpCurrentLocation(state, payload) {
      state.upCurrentLocation = payload
    },
    setDocNo(state, payload) {
      state.docNo = payload
    },
    setCurrentLocation(state, payload) {
      state.currentLocation = payload
    },
    setDocInfo(state, payload) {
      state.docInfo = payload
    },
    setHtml(state, payload) {
      state.html = payload
    },
    setDetailLoading(state, payload) {
      state.detailLoading = payload
    },
    setContent(state, payload) {
      state.content = payload
    },
    setPageFaqList(state, payload) {
      state.pageFaqList = payload
    },
    setDefinition(state, payload) {
      state.definition = payload
    },
    setKeyWord(state, payload) {
      state.keyWord = payload
    },
    setSubMenuActive(state, payload) {
      // 匹配 页面编码
      let payloadArr = location.pathname.split('/')
      let currentPage = payloadArr[payloadArr.length - 1]
      let ignorePageNo = `/${currentPage}`
      ignorePageNo = utils.ignoreUrl(ignorePageNo)
      if (!payload) {
        state.subMenuApiId = ''
        return
      }
      // 点击闪烁问题
      this.commit('solutions/setHistoryList', [])
      // const reg = new RegExp(`${currentPage}$`)
      // 跳转链接url 匹配正则
      // const item = state.itemsList.find(menu => reg.test(menu.location))
      const item = state.itemsList.find(menu => {
        // return menu.location === payload
        menu.ignoreUrl = utils.ignoreUrl(`/${menu.uri}`)
        return menu.ignoreUrl === ignorePageNo
        // return menu.uri === currentPage
      })
      if (!item) {
        state.subMenuApiId = ''
        state.currentApiId = ''
        return
      }
      state.subMenuApiId = payload
      state.currentApiId = item.apiId
      this.commit('solutions/setMenuActive', item.openKeys)
      this.dispatch('apiDocs/getApiExtend', {
        docNo: state.docNo,
        pageNo: item.uri
      }, { root: true })
      this.dispatch('solutions/init', {
        apiId: item.apiId,
        spiCount: item.spiCount
      })
      // historyCount
      if (item.historyCount > 1) {
        // 当前版本是历史版本，根据父apiId拿历史
        if (item.latestRef) {
          this.dispatch('solutions/gethistoryList', item.latestRef)
          return
        }
        this.dispatch('solutions/gethistoryList', item.apiId)
      }
    },
    setMenuActive(state, payload) {
      state.menuCode = payload
    },
    setItemsList(state, payload) {
      state.itemsList = payload
    },
    setHistoryList(state, payload) {
      state.historyList = payload
    }

  },
  actions: {
    init({ dispatch, state, rootState }, { apiId }) {
      if (!rootState.loading) state.detailLoading = true
      const list = [
        dispatch('apiDocs/getDefinition', apiId, { root: true }),
        dispatch('apiDocs/getErrcode', apiId, { root: true }),
        dispatch('apiDocs/getApiFaqList', apiId, { root: true })
      ]
      list.push(dispatch('apiDocs/getSpi', apiId, { root: true }))
      Promise.all(list).then(() => {
        state.detailLoading = false
      })
    },
    async getMenu({ commit, dispatch }, { docNo, location }) {
      try {
        commit('setLoading', true, { root: true })
        commit('setDocNo', docNo)
        const res = await solutions.getInfo(docNo)
        const { docVersion, excludePages = [] } = res.data
        commit('setDocInfo', res.data)
        let menu = await solutions.getMenu({ docNo, docVersion })
        const faqList = await faq.getDocFaqList({ docNo })
        const apiCategories = await solutions.getApiCategories({ docNo, docVersion })
        if(faqList.length > 0) {
          utils.addMenuFaq(menu, docNo)
        }
        if(apiCategories.length > 0) {
          utils.formateApiMenu(apiCategories)
        }
        menu = utils.filterProductMenuList(menu, excludePages)

        const apiObjIndex = menu.findIndex(item => item.appendChildren && item.templateId === 'SOLUTION_APIS')
        let _apiCategories = []

        if (apiCategories.length && apiObjIndex >= 0) {
          if (apiCategories.length === 1) {
            _apiCategories = apiCategories[0].items
          } else {
            _apiCategories = apiCategories.map(item => {
              return {
                ...item,
                title: item.name,
                location: 'apis/' + item.code,
                children: item.items
              }
            })
          }

          menu[apiObjIndex] = {
            ...menu[apiObjIndex],
            children: _apiCategories
          }
        }
        commit('setMenuList', menu)
        commit('setLoading', false, { root: true })
        dispatch('getContent', location)
        let newList = []
        if (apiCategories.length === 1) {
          newList = _apiCategories
        } else {
          _apiCategories.forEach(child => {
            newList.push(...child.children)
          })
        }
        commit('setItemsList', newList)
        commit('setSubMenuActive', location)
      } catch (error) {
        commit('setMenuList', [])
        commit('setLoading', false, { root: true })
      }
    },
    getContent({ commit, dispatch, state }, paramsUri) {
      let item = utils.findProductItem(state.menuList, paramsUri)
      if (!item) {
         item = state.menuList[0]
      }
      const { contentUri, contentType, templateId, location, pageId, title } = item
      document.title = `易宝开放平台-${title}`
      commit('setContentType', contentType)
      commit('setTemplateId', templateId)
      commit('setCurrentLocation', location ? location : '')
      commit('setDefinition', {})
      commit('setSubMenuActive', location)
      if (contentType === 'JSON') {
        return dispatch('getJson', contentUri)
      } else {
        return dispatch('getHtml', { url: contentUri, pageId } )
      }
    },
    getApiExtend({ state }, params) {
      api
        .getApiExtend({
          docNo: params.docNo,
          pageNo: params.pageNo
        })
        .then(res => {
          let newPageNo = `/${params.pageNo}`
          // 匹配到 /post__、  /get__ 这两个 如果失败了，改为/options__ 再发一次请求
          if (newPageNo.includes('/post__') || newPageNo.includes('/get__')) {
            if (res.length == 0) {
              this.reGetApiExtend({ state }, params)
            }
          }
          state.scenesMsg = res[0] ? res[0].data : ''
          state.invokingMsg = res[1]
            ? `<div class="markdown-con">${res[1].data}</div>`
            : ''
        })
    },
    reGetApiExtend({ state }, params) {
      if (params.pageNo.includes('/post__')) {
        params.pageNo = params.pageNo.replace('/post__', '/options__')
      }
      if (params.pageNo.includes('/get__')) {
        params.pageNo = params.pageNo.replace('/get__', '/options__')
      }
      api.getApiExtend({
        docNo: params.docNo,
        pageNo: params.pageNo
      })
      .then(res => {
        state.scenesMsg = res[0] ? res[0].data : ''
        state.invokingMsg = res[1]
          ? `<div class="markdown-con">${res[1].data}</div>`
          : ''
      })
    },
    gethistoryList({ commit }, apiId) {
      api
        .getHistory({
          apiId
        })
        .then(res => {
          commit('setHistoryList', res)
        })
    },
    getHtml({ commit }, { url, pageId}) {
      if (!url) return
      faq.getPageFaqList({ pageId })
        .then(res => {
          commit('setPageFaqList', res)
        })
      return solutions.getHtml(url).then(res => {
        commit('setHtml', res)
      })
    },
    getJson({ commit }, url) {
      if (!url) return
      return solutions.getJson(url).then(res => {
        commit('setContent', res)
      })
    },
    getCommon({ state }, refName) {
      api
        .getCommon({
          refName
        })
        .then(res => {
          state.common = res
        })
    },
    getErrcode({ state }, apiId) {
      api
        .getErrcode({
          apiId
        })
        .then(res => {
          state.errcode = res
          state.pageNo = 1
        })
    },
    getSpi({ state }, apiId) {
      state.callbackList = []
      api
        .getSpi({
          apiId
        })
        .then(res => {
          state.callbackList = res || []
        })
    },
    getDefinition({ state, dispatch }, apiId) {
      api
        .getDefinition({
          apiId
        })
        .then(res => {
          state.definition = res
          const { resHeader, resBody } = handleRespParams(res.respParams)
          state.definition.respParams = resBody
          state.resHeader = resHeader
          dispatch('getCommon', res.commonRef)
        })
    },
    getApiFaqList({ state }, apiId) {
      state.apiFaqList = []
      faq.getApiFaqList({
        apiId
      })
        .then(res => {
          state.apiFaqList = res
        })
    }
  }
}
