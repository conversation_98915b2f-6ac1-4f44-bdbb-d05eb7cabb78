import apiSearch from '@/api/search'
export default {
  namespaced: true,
  state: {
    categoryList: [],
    currentCode: 'ALL',
    loading: false,
    searchParamsMap: {},
    searchWord: '',
    searchTipWord: '',
    initParams: {
      wd: '',
      _pageNo: 1,
      _pageSize: 20,
      subCategory: '',
      category: ''
    },
    result: {
      pageNo: 0,
      totalNum: 0,
      pageItems: [],
      mobilePageItems: [],
      words: [],
      subCategories: []
    }
  },
  mutations: {
    setPageNo(state, payload) {
      state.searchParamsMap[state.currentCode]._pageNo = payload
    },
    setCurrentCode(state, payload) {
      state.currentCode = payload
    },
    setSearchTipWord(state, payload) {
      state.searchTipWord = payload
    },
    setLoading(state, payload) {
      state.loading = payload
    },
    setSubCategories(state, payload) {
      state.searchParamsMap[state.currentCode]._pageNo = 1
      state.searchParamsMap[state.currentCode].subCategory = payload
    },
    setCategoryList(state, payload) {
      state.categoryList = payload
    },
    setResult(state, payload) {
      const { mobilePageItems, pageNo } = state.result
      state.result = payload
      if (pageNo > 1) {
        state.result.mobilePageItems = mobilePageItems.concat(payload.pageItems)
      } else {
        state.result.mobilePageItems = payload.pageItems
      }
    },
    setSearchWord(state, payload) {
      state.searchWord = payload
    },
    setSearchParamsMap(state, payload) {
      const obj = {}
      payload.forEach(item => {
        obj[item.code] = JSON.parse(JSON.stringify(state.initParams))
        obj[item.code].category = item.code
        obj[item.code].codeName = item.name
      })
      state.searchParamsMap = obj
    }
  },
  actions: {
    getCategory({ commit }) {
      return apiSearch.getCategory().then(res => {
        if (res.data) {
          commit('setCategoryList', res.data.result)
          commit('setSearchParamsMap', res.data.result)
        } else {
          commit('setCategoryList', [])
        }
      })
    },
    async getResult({ commit, state, dispatch }) {
      if (!state.searchWord) return
      try {
        if (JSON.stringify(state.searchParamsMap) === '{}') {
          await dispatch('getCategory')
        }
        const params = state.searchParamsMap[state.currentCode]
        let wd = state.searchWord
        if (wd.length > 40) {
          commit('setSearchTipWord', wd.slice(40, 43))
          wd = wd.slice(0, 40)
        } else {
          commit('setSearchTipWord', '')
        }
        if (params._pageNo === 1) {
          params.wd = wd
        }
        commit('setLoading', true)
        apiSearch
          .getResult(params)
          .then(res => {
            if (res.status === 'success') {
              commit('setResult', res.data.page)
            } else {
              commit('setResult', {})
            }
          })
          .finally(() => {
            commit('setLoading', false)
          })
      } catch (error) {
        console.log(error)
      }
    }
  }
}
