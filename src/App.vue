<template>
	<a-config-provider :locale="locale">
		<div
			id="app"
			class="global-wrap"
		>
			<PageHeader />
			<a-spin :spinning="loading">
				<div slot="indicator">
					<img
						v-if="isMobile"
						src="@/assets/images/loading_32.gif"
						class="img-mobile-loading"
						alt
					/>
					<img
						v-else
						src="@/assets/images/loading_64.gif"
						class="img-pc-loading"
						alt
					/>
				</div>
				<div
					class="global-content"
					:class="{
						'global-content-mobile': isMobile,
						'global-content-fix': enableFixed,
					}"
					id="global-content-id"
				>
					<router-view />
				</div>
			</a-spin>
			<ProductDialog />
		</div>
	</a-config-provider>
</template>
<script>
import zhCN from 'ant-design-vue/lib/locale-provider/zh_CN'
import enUS from 'ant-design-vue/lib/locale-provider/en_US'
import PageHeader from '@/components/PageHeader'
import ProductDialog from '@/components/ProductDialog'
import { BackTop } from 'ant-design-vue'
import { Modal } from 'ant-design-vue'
import Api from '@/api'
export default {
	components: {
		PageHeader,
		ProductDialog,
		[BackTop.name]: BackTop,
	},
	data() {
		return {
			locale: zhCN,
		}
	},
	computed: {
		loading() {
			return this.$store.state.loading
		},
		enableFixed() {
			return !!this.$route.meta.enableFixed
		},
		showToTopBtn() {
			return this.$store.state.showToTopBtn
		},
		scrollBarWidth() {
			return this.$store.state.scrollBarWidth
		},
		customUserId() {
			return this.$store.state.customUserId
		},
		fixIconPosition() {
			if (this.isMobile) return '10px'
			return 32 + this.scrollBarWidth + 'px'
		},
		isMobile() {
			return this.$store.state.isMobile
		},
		visible() {
			return this.$store.state.visible
		},
	},
	watch: {
		'$store.state.langType'() {
			const langType = window.localStorage.getItem('langType')
			if (langType && langType === 'en_US') {
				this.locale = enUS
				this.$i18n.locale = 'en'
			} else {
				this.locale = zhCN
				this.$i18n.locale = 'cn'
			}
			this.$store.dispatch('search/getCategory')
		},
		$route(newValue, oldValue) {
			if (newValue.path !== oldValue.path) {
				this.setTitle()
        this.addPoint()
        document.documentElement.scrollTop = 0
			}
		},
	},
	created() {
		this.toggleMobile()
		const that = this
		window.addEventListener('resize', () => {
			that.toggleMobile()
		})
		this.setLang()
		this.$store.dispatch('search/getCategory')
		this.$store.commit('setCustomUserId')
		window.localStorage.wmUserInfo = JSON.stringify({
			userId: window.localStorage.getItem('customUserId'),
			userTag: 'tag',
			projectVersion: '1.0.1',
			env: 'pro',
		})
	},
	async mounted() {
		this.scrollWidth()
		this.onScroll()
		this.setTitle()
		this.addPoint()
		this.addListener()
		this.detectBrowser()
	},
  methods: {
    detectBrowser() {
			const userAgent = navigator.userAgent
			const that = this
			if (/firefox/i.test(userAgent)) {
				document.body.classList.add('firefox')
			} else if (/chrome/i.test(userAgent)) {
				document.body.classList.add('chrome')
			} else if (/safari/i.test(userAgent)) {
				document.body.classList.add('safari')
      }
      window.addEventListener('beforeprint', () =>  {
        that.$store.commit('setPrint', true)
      })
      window.addEventListener('afterprint', () =>  {
        that.$store.commit('setPrint', false)
      })
		},
		addListener() {
			const that = this
			window.goToUrl = function (url, target) {
				if (/open\.yeepay\.com/.test(url)) {
					that.handlerUrl(url, target)
					return
				}
				Api.checkLink(encodeURIComponent(url)).then((res) => {
					switch (res.data.type) {
						case 'DIRECT':
							if (that.isIOS()) {
								window.location.href = res.data.targetLink
							} else {
								window.open(res.data.targetLink)
							}
							break
						case 'ALERT':
							Modal.confirm({
								title: '确认继续访问？',
								closable: true,
								icon: 'info-circle',
								content: (h) => {
									return h('div', [
										h('p', '继续访问将跳转到外部网站，其安全性未知。'),
										h(
											'a',
											{
												style: {
													cursor: 'default',
													overflow: 'hidden',
													wordBreak: 'break-all',
													textOverflow: 'ellipsis',
													borderRadius: '5px',
													height: '40px',
													width: '100%',
													padding: '0 10px',
													lineHeight: '40px',
													whiteSpace: 'nowrap',
													display: 'inline-block',
													background: 'rgb(239,239,239)',
												},
											},
											url
										),
									])
								},
								okText: '继续访问',
								cancelText: '取消',
								onOk: () => {
									window.open(res.data.targetLink)
								},
							})
							break
						case 'FORBID':
							window.location.href = res.data.targetLink
							break
						default:
							break
					}
				})
			}
		},
		handlerUrl(targetUrl) {
			const url = targetUrl.replace('https://open.yeepay.com/', '').replace(/#.*/, '')
			if (this.visible || !/docs(\/v2)?(\/open)?\/platform(-doc)?/.test(url) || this.isMobile) {
				window.open(targetUrl)
				return
			}
			let splitCode = 'platform'
			if (/open\//.test(url)) {
				splitCode = 'open'
			}
			const docNoAndLocation = url.replace('/index.html', '').split(splitCode)[1]
			let docNo = docNoAndLocation.split('/')[1]
			let location = docNoAndLocation.replace(`/${docNo}/`, '')
			this.$store.dispatch('getDialogContent', { docNo, location, splitCode })
			this.$store.commit('setVisible', true)
		},
		isIOS() {
			return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream
		},
		setTitle() {
			const { title } = this.$route.meta
			if (!title) return
			document.title = title
		},
		addPoint() {
			try {
				const params = {
					userId: this.customUserId,
					pagePath: window.location.pathname,
					pageName: window.document.title,
					terminal: this.isMobile ? 'h5' : 'pc',
				}
				window.webfunnyEvent && window.webfunnyEvent[1].trackEvent(params)
			} catch (err) {
				console.log(err)
			}
		},
		setLang() {
			const langType = window.localStorage.getItem('langType')
			if (langType && langType === 'en_US') {
				this.locale = enUS
				this.$i18n.locale = 'en'
				this.$store.commit('setLangType', 'en')
			} else {
				this.locale = zhCN
				this.$i18n.locale = 'cn'
				this.$store.commit('setLangType', 'cn')
			}
		},
		onScroll() {
      document.addEventListener('scroll', () => {
        const htmlDom = document.documentElement
        const top = htmlDom.scrollTop
        const left = htmlDom.scrollLeft
        this.$store.commit('setScrollTop', top)
        this.$store.commit('setScrollLeft', left)
        if (top < 64) {
          this.$store.commit('setTop', 64 - top)
        }
        if (top > 64) {
          this.$store.commit('setTop', 0)
        }
        this.$store.commit('showToTop', top > 468)
      })
		},
		showAdvice() {
			window.open(
				'https://www.wjx.cn/vj/YeLx1Ec.aspx',
				'_blank',
				'width=1000,height=600,left=300,top=150'
			)
		},
		showService() {
			const url = window.location.origin + '/service'
			window.open(url, '_blank', 'width=800,height=560,left=300,top=150')
		},
		scrollWidth() {
			const scrollContainer = document.createElement('div')
			const scrollContent = document.createElement('div')
			scrollContainer.setAttribute(
				'style',
				'position:fixed;left:-100000px;z-index:-1;width:50px;height:50px;overflow:scroll;'
			)
			scrollContent.setAttribute('style', 'height:100px;')
			scrollContainer.appendChild(scrollContent)
			document.body.appendChild(scrollContainer)
			const scrollBarWidth = scrollContainer.offsetWidth - scrollContent.offsetWidth
			this.$store.commit('setScrollBarWidth', scrollBarWidth)
		},
		toggleMobile() {
			const width = document.documentElement.clientWidth
			this.$store.commit('setClientWidth', width)
			if (width > 960) {
				if (this.$route.path === '/mhome') {
					this.$router.replace('/home')
				}
				this.$store.commit('setIsMobile', false)
				this.$store.commit('setShowComponents', 'PcLayout')
			} else {
				if (this.$route.path === '/home') {
					this.$router.replace('/mhome')
				}
				this.$store.commit('setIsMobile', true)
				this.$store.commit('setShowComponents', 'MobileLayout')
			}
			this.$store.commit('setIs1440', width > 1440)
		},
	},
}
</script>
<style lang="less">
#app {
	font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei',
		SimSun, sans-serif;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	.global-content {
		/* width: 100vw; */
	}
	.global-content-mobile {
		height: 100vh;
	}
	.global-content-fix {
		height: 100vh;
	}
}
@media print {
	#pc-left-menu {
		display: none !important;
	}
  .code-wrap code {
    white-space: pre-wrap !important;
    word-break: break-all !important;
  }
  .params-item-hidden {
    display: block !important;
  }
  .icon-caidan_zhankai:before {
    content: "\e7c0" !important;
  }
  .children-obj {
    display: none !important;
  }
  .children-obj-show {
    display: block !important;
  }
  .paramsList {
    display: block !important;
  }
  .paramsBody-wrap .header .operate, .paramsHeader-wrap .header .operate {
    display: none !important;
  }
	.code-col-wrap {
		position: static !important;
		top: 0 !important;
		width: 100vw !important;
	}
	.print-col-wrap {
		width: 100vw !important;
	}
  .safari {
    .code-col-wrap {
      width: 80vw !important;
    }
    .print-col-wrap {
      width: 80vw !important;
    }
    .hljs-comment, .params-exam {
      color: rgba(0, 0, 0, .65) !important;
    }
  }
	.trigger-wrap {
		display: none !important;
	}
	.header-wrap {
		display: none !important;
	}
	.ant-layout {
		margin-left: 0 !important;
	}
	.product-wrap {
		width: 100% !important;
	}
	.product-wrap .ant-col-lg-20 {
		width: 100% !important;
	}
	.api-content {
		padding-left: 0 !important;
		.left {
			width: 100% !important;
		}
		.right {
			display: none !important;
		}
	}
}
</style>
