<template>
<div>
  <div 
    v-if="detailVer === 'new'" 
    class="backOld" 
    :class="is1440 ? '' : 'backOld-fix'"
    :style="{right: is1440 ? '0' : `${scrollBarWidth}px` }"
  >
    <div class="back-btn" @click="goBack('old')">返回旧版</div>
  </div>
  <img v-else class="goNewVer" src="../../assets/goNewVer.gif" alt="" @click="goBack('new')">
</div>
</template>

<script>
export default {
  computed: {
    is1440() {
      return this.$store.state.is1440
    },
    detailVer() {
      return this.$store.state.apiDocs.detailVer
    },
    scrollBarWidth() {
      return this.$store.state.scrollBarWidth
    },
  },
  methods: {
    goBack(ver) {
      this.$upLog(118, {
        target_version: ver === 'new' ? '新版' : '旧版'
      })
      this.$store.commit('apiDocs/setDetailVer', ver)
    },
  },
}
</script>

<style lang="less" scoped>
.backOld {
  margin-bottom: 14px;

  .back-btn {
    width: 72px;
    height: 30px;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid rgba(66, 66, 66, 0.16);
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.65);
    line-height: 30px;
    text-align: center;
    cursor: pointer;
  }
}

.backOld-fix {
  position: fixed;
  top: 110px;
  right: 0;

  .back-btn {
    width: 44px;
    height: 44px;
    line-height: 14px;
    padding: 8px 8px;
    box-shadow: 0px 4px 8px -2px rgba(0, 0, 0, 0.08), 0px 8px 32px -8px rgba(25, 15, 15, 0.07),
      0px 16px 64px -16px rgba(0, 0, 0, 0.05);
    border-radius: 6px 0px 0px 6px;
    border: none;
  }
}

.goNewVer {
  width: 100px;
  cursor: pointer;
}
</style>
