<template>
<div class="help">
  <div class="title">文档对您</div>
  <div class="help-wrap">
    <div class="help-left" :class="{ active: help }" @click="helpClick">
      <svg t="1640936113452" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7194" width="16" height="16" xmlns:xlink="http://www.w3.org/1999/xlink">
        <path d="M64 483.072v388.928c0 37.248 30.144 67.392 67.392 67.392H192V416.32l-60.608-0.64C94.08 415.68 64 445.824 64 483.072zM857.28 344.96l-267.776 1.664c12.544-44.224 18.944-83.584 18.944-118.208 0-78.592-68.864-155.52-137.6-145.472-60.608 8.768-67.264 61.184-67.264 126.784v59.264c0 76.096-63.808 140.864-137.856 148.032L256 416.896v522.432h527.552c49.344 0 91.712-35.136 100.928-83.648l73.728-388.928a102.72 102.72 0 0 0-100.928-121.792z" p-id="7195" />
      </svg>
      <div class="help-text">有帮助</div>
    </div>
    <div class="help-right" :class="{ active: bad }" @click="badClick" id="helpBadId">
      <svg t="1640936323942" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7381" width="16" height="16" xmlns:xlink="http://www.w3.org/1999/xlink">
        <path d="M64 540.992V152c0-37.184 30.144-67.328 67.392-67.328H192v523.008l-60.608 0.64A67.328 67.328 0 0 1 64 540.992z m793.28 138.048l-267.776-1.728c12.544 44.288 18.944 83.584 18.944 118.208 0 78.592-68.864 155.52-137.6 145.536-60.608-8.832-67.264-61.184-67.264-126.848v-59.264c0-76.032-63.808-140.864-137.856-147.968L256 607.04V84.672h527.552c49.344 0 91.712 35.072 100.928 83.584l73.728 388.928a102.72 102.72 0 0 1-100.928 121.856z" p-id="7382" />
      </svg>
      <div class="help-text">无帮助</div>
    </div>
  </div>
  <div class="feedBack" v-if="sucess">
    <img src="@/assets/feedbackSuccess.png" alt="">
    <div class="text">提交成功！感谢你的建议～</div>
  </div>
  <div class="no-form" id="helpBadDia">
    <div class="title">
      <div class="text">您对当前页面的整体感受是否满意？</div>
      <a-icon class="icon" type="close" @click="closeBadDia" />
    </div>
    <a-checkbox-group @change="onChange">
      <a-checkbox :value="item.label" v-for="item in options" :key="item.label">{{item.label}}</a-checkbox>
    </a-checkbox-group>
    <div class="desc">请针对您所遇到的问题给出具体的反馈</div>
    <a-textarea style="height: 74px;" placeholder="请输入反馈内容" allow-clear @change="onChange" />
    <a-button type="primary" class="btn" @click="submit">提 交</a-button>
  </div>
</div>
</template>

<script>
export default {
  data() {
    return {
      help: false,
      bad: false,
      options: [
        {
          label: '描述不清晰',
          value: '',
        },
        {
          label: '代码/图片缺失',
          value: '',
        },
        {
          label: '步骤不完整',
          value: '',
        },
        {
          label: '内容更新不及时',
          value: '',
        },
        {
          label: '参数有误',
          value: '',
        },
        {
          label: '其他及优化建议',
          value: '',
        },
      ],
      sucess: false
    }
  },
  methods: {
    helpClick() {
      this.help = !this.help
      this.sucess = true
    },
    badClick() {
      this.bad = !this.bad
      this.showDia()
    },
    onChange() {
      this.sucess = true
    },
    submit() {

    },
    showDia() {
      const badDom = document.querySelector('#helpBadId')
      const { left } = badDom.getBoundingClientRect()
      const diaDom = document.querySelector('#helpBadDia')
      diaDom.style.left = left - 316 + 'px'
    },
    closeBadDia() {
      const diaDom = document.querySelector('#helpBadDia')
      diaDom.style.left =  -316 + 'px'
    }
  }
}
</script>

<style lang="less" scoped>
.help {
  position: fixed;
  bottom: 72px;
  right: 76px;
  .title {
    display: none;
  }
  .help-wrap {
    .help-left, .help-right {
      width: 40px;
      height: 40px;
      background: #FFFFFF;
      box-shadow: 0px 4px 8px -2px rgba(0,0,0,0.08), 0px 8px 32px -8px rgba(25,15,15,0.07), 0px 16px 64px -16px rgba(0,0,0,0.05);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      svg {
        fill: #aeaeae;
        margin-right: 4px;
      }

      &:hover {
        svg {
          fill: #52bf63;
        }
      }
      .help-text {
        display: none;
      }
    }
    .help-left {
      border-bottom: 1px solid #F4F4F4;
    }
  }
  &:hover {
    width: 106px;
    height: 125px;
    background: #FFFFFF;
    box-shadow: 0px 4px 8px -2px rgba(0,0,0,0.08), 0px 8px 32px -8px rgba(25,15,15,0.07), 0px 16px 64px -16px rgba(0,0,0,0.05);
    border-radius: 8px;
    padding-top: 12px;
    .title {
      display: block;
      height: 20px;
      font-weight: 500;
      font-size: 14px;
      color: rgba(0,0,0,0.85);
      line-height: 20px;
      margin-bottom: 8px;
      text-align: center;
    }
    .help-wrap {
      .help-left, .help-right {
        display: flex;
        align-items: center;
        width: 100%;
        .help-text {
          display: block;
          height: 20px;
          font-weight: 400;
          font-size: 14px;
          color: rgba(0,0,0,0.65);
          line-height: 20px;
          margin-left: 4px;
        }
      }
    }
  }
  .no-form {
    position: fixed;
    left: -298px;
    bottom: 72px;
    width: 298px;
    z-index: 99;
    background: radial-gradient( 0% 161% at 3% 4%, #E5FFFC 0%, rgba(255,255,255,0.55) 100%), #FFFFFF;
    box-shadow: 0px 4px 8px -2px rgba(0,0,0,0.08), 0px 8px 32px -8px rgba(25,15,15,0.07), 0px 16px 64px -16px rgba(0,0,0,0.05);
    border-radius: 8px;
    padding: 20px 24px;
    .title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      .text {
        flex: 1;
        font-weight: 500;
        font-size: 14px;
        color: rgba(0,0,0,0.85);
        line-height: 20px;
        margin-right: 10px;
      }
      .icon {
        font-size: 14px;
      }
    }
    .desc {
      font-weight: 400;
      font-size: 12px;
      color: rgba(0,0,0,0.85);
      line-height: 17px;
      margin: 24px 0 8px 0;
    }
    .btn {
      margin-top: 16px; 
      height: 32px;
      width: 100%;
    }
    /deep/ .ant-checkbox + span {
      height: 22px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: rgba(0,0,0,0.85);
      line-height: 22px;
      text-align: left;
      padding-right: 0;
    }
    /deep/ .ant-checkbox-wrapper:nth-child(even) {
      width: 122px;
      margin-left: 0;
      margin-bottom: 8px;
    }
    /deep/ .ant-checkbox-wrapper:nth-child(odd) {
      width: 110px;
      margin-left: 0;
    }
  }
  /deep/ textarea {
    height: 74px;
  }
  .feedBack {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    margin-bottom: 12px;
    width: 176px;
    height: 125px;
    background: radial-gradient( 0% 161% at 3% 4%, #E5FFFC 0%, rgba(255,255,255,0.55) 100%), #FFFFFF;
    box-shadow: 0px 9px 28px 8px rgba(0,0,0,0.05), 0px 6px 16px 0px rgba(0,0,0,0.08), 0px 3px 6px -4px rgba(0,0,0,0.12);
    border-radius: 8px;
    position: fixed;
    bottom: 72px;
    right: 16px;
    img {
      width: 52px;
      height: 52px;
      margin-bottom: 9px;
    }
    .text {
      height: 14px;
      font-weight: 400;
      font-size: 12px;
      color: rgba(0,0,0,0.88);
      line-height: 14px;
    }
  }
}
</style>
