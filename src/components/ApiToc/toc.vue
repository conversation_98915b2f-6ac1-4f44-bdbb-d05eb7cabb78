<template>
  <div class="anchor-wrap" :class="{ lg: !is1440 }">
    <div class="anchor-content" id="anchor-id-wrap" ref="anchor-id-wrap">
      <div class="title">本文内容</div>
      <a-anchor :targetOffset="80" @click="handleClick" @change="onAnchorChange" :wrapperStyle="{}" :affix="false">
        <a-anchor-link href="#anchor1" title="接口说明" />
        <a-anchor-link href="#anchor2" :title="$t('instructions')" v-if="apiInvokingMsg" />
        <a-anchor-link href="#anchor3" :title="$t('repParams')" :class="{
          'api-anchor-wrap': currentAnchor !== '#anchor3' && currentAnchor.indexOf('#anchor3') !== -1,
        }">
          <a-anchor-link href="#anchor3-1" :title="$t('reqHeader')" />
          <a-anchor-link href="#anchor3-2" v-if="definition.reqParams && Object.keys(definition.reqParams).length > 0"
            :title="definition.method.toUpperCase().includes('GET') ? $t('reqParameters') : $t('reqBody')" />
        </a-anchor-link>
        <a-anchor-link href="#anchor4" :title="$t('respParams')" :class="{
          'api-anchor-wrap': currentAnchor !== '#anchor4' && currentAnchor.indexOf('#anchor4') !== -1,
        }">
          <a-anchor-link href="#anchor4-1" :title="$t('resHeader')" />
          <a-anchor-link href="#anchor4-2" v-if="definition.respParams && Object.keys(definition.respParams).length > 0"
            :title="$t('resBody')" />
        </a-anchor-link>
        <a-anchor-link href="#anchor6" :title="$t('errorCode')" />
        <a-anchor-link href="#anchor7" v-if="callbackList.length > 0" :title="$t('callback')" :class="{
          'api-anchor-wrap': currentAnchor !== '#anchor7' && currentAnchor.indexOf('#anchor7') !== -1,
        }">
          <a-anchor-link 
            v-for="callback in callbackList"
            :key="callback.title"
            :href="`#${callback.title}`" 
            :title="callback.title" 
          />
          <a-anchor-link v-if="showSpiApiData && showSpiApiData.length > 0" href="#anchor10"
            :title="$t('apiSearchOrder')" />
        </a-anchor-link>
        <a-anchor-link href="#anchor8" v-if="apiFaqList.length > 0" :title="$t('menu.questionTitle')">
          <a-anchor-link v-for="faq in apiFaqList" :key="faq.id" :href="`#${faq.title}faq`" :title="faq.title" />
        </a-anchor-link>
      </a-anchor>
    </div>
    <div class="small-anchor" id="menuId" :class="{ active: active }" @click.stop="showMenu">
      <span class="icon iconfont icon-mulu" style="font-size: 14px"></span>
      目录
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  props: ['currentAnchor', 'reqParamsValue', 'respParamsValue', 'isShowBaowen', 'showSpiApiData'],
  data() {
    return {
      active: false,
    }
  },
  computed: {
    ...mapState('apiDocs', [
      'subMenuApiId',
      'currentApiId',
      'definition',
      'pageNo',
      'common',
      'resHeader',
      'scenesMsg',
      'apiInvokingMsg',
      'menuList',
      'itemsList',
      'historyList',
      'detailLoading',
      'callbackList',
      'apiFaqList',
    ]),
    is1440() {
      return this.$store.state.is1440
    },
  },
  mounted() {
    window.addEventListener('click', this.onClick)
  },
  methods: {
    onClick(e) {
      if (!this.active) return
      const selectWrapDom = this.$refs['anchor-id-wrap']
      const { left, right, top, bottom } = selectWrapDom.getBoundingClientRect()
      const { clientX, clientY } = e
      if ((clientX < left || clientX > right) || (clientY < top || clientY > bottom)) {
        this.showMenu()
      }
    },
    handleClick() { },
    onAnchorChange(val) {
      const currentTitle = val.replace('#', '')
      if (this.callbackList.find(c => c.title === currentTitle)) {
        this.$emit('onCallbackClick', currentTitle)
      }
    },
    showMenu() {
      const menuDom = document.querySelector('#menuId')
      const anchorDom = document.querySelector('#anchor-id-wrap')
      if (this.active) {
        this.active = false
        anchorDom.style.left = -216 + 'px'
        return
      }
      this.active = true
      const { left } = menuDom.getBoundingClientRect()
      anchorDom.style.left = left - 216 + 'px'
    },
  },
}
</script>
<style scoped lang="less">
.lg {
  #anchor-id-wrap {
    width: 200px;
    height: 300px;
    background: #ffffff;
    box-shadow: 0px 4px 8px -2px rgba(0, 0, 0, 0.08), 0px 8px 32px -8px rgba(25, 15, 15, 0.07),
      0px 16px 64px -16px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    overflow-y: auto;
    position: fixed;
    left: -200px;
    bottom: 32px;
    .title {
      display: none;
    }

    padding: 16px 0 0 8px;
  }

  .small-anchor {
    width: 40px;
    height: 40px;
    background: #ffffff;
    box-shadow: 0px 4px 8px -2px rgba(0, 0, 0, 0.08), 0px 8px 32px -8px rgba(25, 15, 15, 0.07),
      0px 16px 64px -16px rgba(0, 0, 0, 0.05);
    border-radius: 6px;
    display: flex !important;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 10px;
    cursor: pointer;
    position: fixed;
    bottom: 32px;
    right: 1.1%;
    z-index: 997;
    &:hover {
      color: #52bf63;
    }
  }

  .active {
    color: #52bf63;
  }
}

.anchor-wrap {
  font-family: PingFangSC, PingFang SC;
  width: 196px;
  padding-right: 12px;
  .title {
    height: 20px;
    font-weight: 500;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    line-height: 20px;
    margin-bottom: 15px;
  }
  .small-anchor  {
    display: none;
  }
}
</style>
