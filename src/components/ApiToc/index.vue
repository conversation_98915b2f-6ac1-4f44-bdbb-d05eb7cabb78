<template>
<div class="toc-fix">
  <ToggleVer />
  <low-code-renderer v-if="schema.components && !detailLoading" :schema="schema" />
  <Toc v-bind="$props" v-on="$listeners" />
</div>
</template>

<script>
import { fetchSchema } from '@/utils/lowcode'
import Toc from './toc.vue'
import ToggleVer from './toggleVer.vue'
export default {
  props: [
    'currentAnchor',
    'reqParamsValue',
    'respParamsValue',
    'isShowBaowen',
    'showSpiApiData',
  ],
  components: {
    // Help,
    // Help2,
    Toc,
    ToggleVer
  },
  data() {
    return {
      schema: {}
    }
  },
  computed: {
    is1440() {
      return this.$store.state.is1440
    },
    detailLoading() {
      return this.$store.state.apiDocs.detailLoading
    },
  },
  async mounted() {
    this.schema = await fetchSchema('feed_back_V2')
  },
}
</script>
<style lang="less">
div[data-id='checkbox_1'] {
  .ant-checkbox-wrapper + .ant-checkbox-wrapper {
    margin-left: 0;
  }
  .ant-checkbox-wrapper {
    width: 130px;
  }
}

</style>
<style lang="less" scoped>
.toc-fix {
  position: sticky;
  top: 80px;
  z-index: 997;
}
</style>
