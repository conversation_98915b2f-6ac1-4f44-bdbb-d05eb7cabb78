<template>
  <RecycleScroller
    style="max-height: 4000px"
    :items="list"
    :item-size="90"
    key-field="apiId"
    :infinite-scroll-disabled="busy"
    :infinite-scroll-distance="10"
  >
    <a-menu-item
      @click="onMenuClick(item.apiId)"
      class="ant-menu-item"
      :class="{ 'ant-menu-item-selected': item.apiId === subMenuApiId }"
      slot-scope="{ item, index }"
      :id="`menu-${menuIndex}-subMenu-${index}`"
      :key="item.apiId"
      v-if="!item.latestRef || item.show || item.latestRef === item.apiId"
    >
      <div class="pc-menu">
        <div class="api-path">
          <template v-for="method in item.method.split(',')">
            <div :class="method" :key="method">
              <span class="font-size-10px">{{ method }}</span>
            </div>
          </template>
          {{ item.path }}
        </div>
        <div class="api-name">{{ item.title }}</div>
      </div>
    </a-menu-item>
  </RecycleScroller>
</template>
<script>
import { RecycleScroller } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
export default {
  props: ['list', 'code', 'menuIndex'],
  components: {
    RecycleScroller
  },
  computed: {
    subMenuApiId() {
      return this.$store.state.subMenuApiId
    }
  },
  data() {
    return {
      data: [],
      busy: false
    }
  },
  methods: {
    onMenuClick(apiId) {
      this.$store.commit('setSubMenuActive', apiId)
      this.$store.commit('setMenuActive', this.code)
    }
  }
}
</script>
<style>
.api-path {
  word-break: break-all;
  white-space: pre-line;
  font-size: 12px;
  font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
    sans-serif;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.65);
  line-height: 17px;
  margin-bottom: 5px;
}
.api-name {
  word-break: break-all;
  height: 17px;
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
    sans-serif;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.45);
  line-height: 17px;
}
</style>
