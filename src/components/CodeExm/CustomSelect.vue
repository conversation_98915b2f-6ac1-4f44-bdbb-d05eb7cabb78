<template>
  <div class="custom-select-wrap">
    <div class="content" @click.left="showDia">
      <div class="label">{{ label }}</div>
      <span class="icon iconfont icon-zhankai_black"></span>
    </div>
    <ul class="select-wrap" v-if="showSub" ref="select-wrap-id">
      <li class="select-item" v-for="item in options" :key="item.value" @click.stop="change(item.value)">
        <a-icon type="check" class="check-icon" v-if="item.value === value" />{{ item.label }}
      </li>
    </ul>
  </div>
</template>
<script>
export default {
  props: ['options', 'value'],
  data() {
    return {
      showSub: false
    }
  },
  computed: {
    label() {
      if (this.options.find(o => o.value === this.value)) {
        return this.options.find(o => o.value === this.value).label
      }
      return ''
    }
  },
  mounted() {
    window.addEventListener('click', this.onClick)
  },
  methods: {
    onClick(e) {
      if (!this.showSub) return
      const selectWrapDom = this.$refs['select-wrap-id']
      if(!selectWrapDom)return
      const { left, right, top, bottom } = selectWrapDom.getBoundingClientRect()
      const { clientX, clientY } = e
      if ((clientX < left || clientX > right) || (clientY < top || clientY > bottom)) {
        this.showSub = false
      }
    },
    change(val) {
      this.showSub = false
      this.$emit('input', val)
      this.$emit('change', val)
    },
    showDia() {
      this.showSub = true
    }
  }
}
</script>
<style lang="less" scoped>
.custom-select-wrap {
  width: 88px;
  background: rgba(0,0,0,0.04);
  border-radius: 4px;
  border: 1px solid rgba(0,0,0,0.08);
  padding: 0 10px 0 8px;
  position: relative;
  cursor: pointer;
  display: inline-block;
  .content {
    height: 24px;
    display: flex;
    align-items: center;
  }
  .label {
    height: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: rgba(0,0,0,0.85);
    line-height: 16px;
    margin-right: 8px;
    flex: 1;
  }
  .icon {
    color: rgba(0,0,0,0.85);
    font-size: 10px;
  }
  .select-wrap {
    position: absolute;
    left: 0;
    top: -50%;
    width: 84px;
    z-index: 99;
    background: #F3F4F5;
    box-shadow: 0px 4px 6px 0px rgba(0,0,0,0.24);
    border-radius: 8px;
    border: 1px solid rgba(255,255,255,0.08);
    backdrop-filter: blur(2px);
    padding: 4px;
    .select-item {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 12px;
      color: rgba(0,0,0,0.65);
      line-height: 22px;
      padding-left: 16px;
      cursor: pointer;
      margin-bottom: 6px;
      position: relative;
      &:last-child {
        margin-bottom: 0;
      }
      &:hover {
        background: #52BF63;
        border-radius: 4px;
        color: #FFFFFF;
      }
      .check-icon {
        position: absolute;
        left: 2px;
        top: 5px;
      }
    }
  }
}
</style>