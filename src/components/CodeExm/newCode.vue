<template>
	<div class="newcode-wrap">
		<div class="header">
			<div class="title">{{ $t("codeExm") }}</div>
			<div style="flex: 1;display: flex;margin-right: 4px;">
				<CustomSelect
					v-model="value"
					:options="langOptions"
					style="margin-right: 8px"
					@change="changeLang"
				/>
				<CustomSelect
          v-if="showLangType"
					v-model="langType"
					:options="langTypeOptions"
				/>
			</div>
      <a-popover content="调试" overlayClassName="custom-popover" v-if="!isMobile">
        <span class="icon iconfont icon-tiaoshi_line" style="font-size: 13px;margin-top: 1px;" @click="debug"></span>
      </a-popover>
      <a-popover content="复制" overlayClassName="custom-popover">
        <a-icon class="icon" type="copy" @click="copy" />
      </a-popover>
      <a-popover content="查看SDK文档" overlayClassName="custom-popover">
        <span class="icon iconfont icon-wendang" style="font-size: 14px; margin-top: 2px;" @click="goDoc"></span>
      </a-popover>
		</div>
		<div class="content">
			<Code
				v-if="definition.sampleCodes"
				:key="code"
				:code="code"
				:lang="value.includes('JAVA') ? 'JAVA' : value"
				:hiddenCpBtn="true"
			/>
		</div>
	</div>
</template>

<script>
import { goApifoxUrl } from '@/utils'
import Code from '../Code/index'
import CustomSelect from './CustomSelect'
export default {
	components: {
		Code,
		CustomSelect,
	},
	data() {
		return {
			value: 'JAVA',
			langType: 'business', // basic
      langTypeOptions: [
        {
          label: '业务SDK',
          value: 'business'
        },
        {
          label: '基础SDK',
          value: 'basic'
        }
      ],
      codeLink: {
        JAVA_COMMON: {
          label: '查看JAVA-业务SDK下载及使用说明',
          link: 'https://open.yeepay.com/docs/open/platform-doc/sdk_guide-sm/java-sdk-guide-sm#2-%E5%BC%95%E5%85%A5-%E4%B8%9A%E5%8A%A1SDK-'
        },
        JAVA: {
          label: '查看JAVA-基础SDK下载及使用说明',
          link: 'https://open.yeepay.com/docs/open/platform-doc/sdk_guide-sm/java-sdk-guide-sm#1-%E5%BC%95%E5%85%A5-%E5%9F%BA%E7%A1%80SDK-'
        },
        GO: {
          label: '下载GO-SDK',
          link: 'https://open.yeepay.com/docs/platform/developTools/sdk'
        },
        CSHARP: {
          label: '下载C#-SDK',
          link: 'https://open.yeepay.com/docs/platform/developTools/sdk'
        },
        PHP: {
          label: '查看PHP-SDK下载及使用说明',
          link: 'https://open.yeepay.com/docs/platform/sdk_guide/php-sdk-guide'
        },
        PYTHON: {
          label: '下载PYTHON-SDK',
          link: 'https://open.yeepay.com/docs/platform/developTools/sdk'
        },
      }
		}
  },
  watch: {
    langType(newVal) {
      window.localStorage.setItem('code_lang_type', newVal)
    },
  },
	computed: {
		definition() {
			return this.$store.state.apiDocs.definition && this.$store.state.apiDocs.definition.apiId
				? this.$store.state.apiDocs.definition
				: this.$store.state.solutions.definition
		},
		currentCodeLink() {
			return this.codeLink[this.value]
		},
		showLangType() {
			return this.definition.sampleCodes.JAVA && this.definition.sampleCodes.JAVA_COMMON && this.value.includes('JAVA')
		},
    code() {
			return this.langType === 'basic'
				? this.definition.sampleCodes['JAVA_COMMON']
				: this.definition.sampleCodes[this.value]
		},
    langOptions() {
			return Object.keys(this.definition.sampleCodes)
        .filter((item) => {
          if (item == 'JAVA_COMMON') {
            return !this.definition.sampleCodes.JAVA
          }
          return true
        })
				.map((item) => {
					let label = item
					if (item === 'CSHARP') {
						label = 'C#'
					}
					if (item === 'JAVA_COMMON') {
						label = 'JAVA'
					}
					return {
						label,
						value: item,
					}
				})
    },
    isMobile() {
      return this.$store.state.isMobile
    },
  },
  mounted() {
    const catchValue = window.localStorage.getItem('code_lang')
    if (!catchValue || !this.langOptions.find(l => l.value === catchValue)) {
      this.value = this.langOptions[0].value
    } else {
      this.value = catchValue
    }
    this.langType =  window.localStorage.getItem('code_lang_type') || 'business'
  },
	methods: {
    copy() {
      this.$upLog(120, {
        feature_id: '复制'
      })
			var oInput = document.createElement('textarea') //创建一个隐藏input
			oInput.value = this.code
			document.body.appendChild(oInput)
			oInput.select() // 选择对象
			document.execCommand('Copy') // 执行浏览器复制命令
			oInput.className = 'oInput'
			oInput.style.display = 'none'
			this.$message.success('复制成功')
		},
		// 切换语言类型
    changeLang(row) {
      this.$upLog(120, {
        feature_id: '切换语言类型'
      })
      this.langType = row.includes('JAVA') ? 'business' : ''
      window.localStorage.setItem('code_lang', row)
		},
    debug() {
      this.$upLog(120, {
        feature_id: '调试'
      })
      goApifoxUrl(this.$store.state.apiDocs)
    },
    goDoc() {
      this.$upLog(120, {
        feature_id: '查看SDK文档'
      })
      const url = this.codeLink[this.value].link
      window.open(url)
    }
	},
}
</script>
<style>
.custom-popover {
  .ant-popover-inner-content {
    padding: 5px 10px;
    font-size: 14px;
    color: rgba(0,0,0,0.65);
  }
  .ant-popover-content, .ant-popover-inner{
    border-radius: 6px;
  }
}
</style>
<style lang="less" scoped>
.newcode-wrap {
	background:  #FAFAFA;
	border-radius: 8px;
	font-family: PingFangSC, PingFang SC;
	max-width: 100%;
  border: 1px solid #EBEBEB;
	.header {
		height: 40px;
		background: #FAFAFA;
		border-radius: 8px 8px 0px 0px;
		display: flex;
		align-items: center;
		padding-left: 16px;
    border-bottom: 1px solid #EBEBEB;
		.title {
			font-weight: 500;
			font-size: 14px;
			color: rgba(0,0,0,0.85);
			line-height: 20px;
			margin-right: 16px;
		}
		.icon {
			color: rgba(0,0,0,0.65);
			margin-right: 16px;
			cursor: pointer;
		}

		.select-custom {
			background: rgba(0,0,0,0.04);
			color: #fff;

			/deep/ .ant-select-selection {
				background: rgba(0,0,0,0.04);
				border: none;

				.ant-select-arrow {
					color: #fff;
				}
			}
		}
	}

	/deep/ .hljs,
	/deep/ .line-numbers-rows {
		background: #F3F4F5;
		color: rgba(0, 0, 0, 0.85);
	}
}
</style>
