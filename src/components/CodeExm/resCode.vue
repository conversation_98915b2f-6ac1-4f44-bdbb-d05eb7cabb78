<template>
  <div class="newcode-wrap">
    <div class="header">
      <div class="title">{{ title }}</div>
      <div style="flex: 1;">
      </div>
      <a-icon class="icon" type="copy" @click="copy" />
    </div>
    <div class="content">
      <Code 
        v-if="definition.sampleCodes"
        :code="code"
        lang="JSON"
        :key="renderKey"
        :hiddenCpBtn="true"
      />
    </div>
  </div>
</template>
<script>
import Code from '../Code/index'
export default {
  props: ['code', 'title'],
  components: {
    Code
  },
  data() {
    return {
      value: 'JAVA', 
      langType: 'business', // basic
      renderKey: 1,
    }
  },
  computed: {
    definition() {
      return (this.$store.state.apiDocs.definition && this.$store.state.apiDocs.definition.apiId) ? this.$store.state.apiDocs.definition : this.$store.state.solutions.definition
    },
  },
  methods: {
    copy() {
      var oInput = document.createElement('textarea') //创建一个隐藏input
      oInput.value = this.code
      document.body.appendChild(oInput)
      oInput.select() // 选择对象
      document.execCommand('Copy') // 执行浏览器复制命令
      oInput.className = 'oInput'
      oInput.style.display = 'none'
      this.$message.success('复制成功')
    },
    // 切换语言类型
    changeLang(row) {
      this.langType = row.includes('JAVA') ? 'business' : ''
      this.value = row
      this.renderKey += 1
    },
    changeLangType(type) {
      this.langType = type
      this.renderKey += 1
    }
  }
}
</script>
<style lang="less" scoped>
.newcode-wrap {
  background: #F3F4F5;
  border-radius: 8px;
  font-family: PingFangSC, PingFang SC;
  border: 1px solid rgba(0,0,0,0.08);
  .header {
    border-radius: 8px 8px 0 0;
    height: 40px;
    background: #FAFAFA;
    box-shadow: 0px 1px 0px 0px rgba(0,0,0,0.08);
    margin-bottom: 1px;
    display: flex;
    align-items: center;
    padding-left: 16px;
    .title {
      height: 20px;
      font-weight: 500;
      font-size: 14px;
      color: rgba(0,0,0,0.85);
      line-height: 20px;
      margin-right: 16px;
    }
    .icon {
      width: 14px;
      height: 14px;
      color: rgba(0,0,0,0.65);
      font-size: 14px;
      margin-right: 16px;
      cursor: pointer;
    }
    .select-custom {
      background: #F3F4F5;
      color: rgba(0,0,0,0.85);
      /deep/ .ant-select-selection {
        background: #F3F4F5;
        border: none;
        .ant-select-arrow {
          color: rgba(0,0,0,0.85);
        }
      }
    }
  }
  /deep/ .hljs, /deep/ .line-numbers-rows {
    background: #F3F4F5;
    color: rgba(0,0,0,0.85);
  }
}
</style>