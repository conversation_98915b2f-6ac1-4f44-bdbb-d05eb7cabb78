<template>
	<div class="code-wrap">
		<div class="select-header">
			<div class="lang">
				<div class="label">{{$t('codeLang')}}：</div>
				<a-radio-group v-model="value">
					<template v-for="item in Object.keys(definition.sampleCodes)">
						<a-radio-button v-if="item == 'JAVA_COMMON' && !definition.sampleCodes.JAVA" :value="item" @click="changeLang(item)" :key="item">JAVA</a-radio-button>
						<a-radio-button v-if="item !== 'JAVA_COMMON' && item !== 'GO'" :value="item" @click="changeLang(item)" :key="item">{{ item === "CSHARP" ? "C#" : item }}</a-radio-button>
					</template>
				</a-radio-group>
			</div>
      <div class="code-link">
        <a v-if="langType === 'business'" :href="codeLink.JAVA_COMMON.link" target="_blank">{{codeLink.JAVA_COMMON.label}}</a>
        <a v-else :href="currentCodeLink.link" target="_blank">{{currentCodeLink.label}}</a>
      </div>
		</div>
    <div v-if="definition.sampleCodes.JAVA && definition.sampleCodes.JAVA_COMMON && value.includes('JAVA')" class="java-type">
      <p class="type-txt">{{$t('condeLangType')}}：</p>
      <p class="sdk-txt">
        <span @click="changeLangType('business')" :class="langType === 'business' ? 'actived' : ''">业务SDK</span>
        <span @click="changeLangType('basic')" :class="langType === 'basic' ? 'actived' : ''">基础SDK</span>
      </p>
    </div>
		<Code
			v-if="definition.sampleCodes"
			:code="langType === 'basic' ? definition.sampleCodes['JAVA_COMMON']: definition.sampleCodes[value]"
			:lang="value.includes('JAVA') ? 'JAVA' : value"
			:key="renderKey"
		/>
	</div>
</template>

<script>
import Code from '@/components/Code'
export default {
  components: {
    Code
  },
  data() {
    return {
      value: 'JAVA', 
      langType: 'business', // basic
      renderKey: 1,
      codeLink: {
        JAVA_COMMON: {
          label: '查看JAVA-业务SDK下载及使用说明',
          link: 'https://open.yeepay.com/docs/open/platform-doc/sdk_guide-sm/java-sdk-guide-sm#2-%E5%BC%95%E5%85%A5-%E4%B8%9A%E5%8A%A1SDK-'
        },
        JAVA: {
          label: '查看JAVA-基础SDK下载及使用说明',
          link: 'https://open.yeepay.com/docs/open/platform-doc/sdk_guide-sm/java-sdk-guide-sm#1-%E5%BC%95%E5%85%A5-%E5%9F%BA%E7%A1%80SDK-'
        },
        GO: {
          label: '下载GO-SDK',
          link: 'https://open.yeepay.com/docs/platform/developTools/sdk'
        },
        CSHARP: {
          label: '下载C#-SDK',
          link: 'https://open.yeepay.com/docs/platform/developTools/sdk'
        },
        PHP: {
          label: '查看PHP-SDK下载及使用说明',
          link: 'https://open.yeepay.com/docs/platform/sdk_guide/php-sdk-guide'
        },
        PYTHON: {
          label: '下载PYTHON-SDK',
          link: 'https://open.yeepay.com/docs/platform/developTools/sdk'
        },
      }
    }
  },
  computed: {
    definition() {
      return (this.$store.state.apiDocs.definition && this.$store.state.apiDocs.definition.apiId) ? this.$store.state.apiDocs.definition : this.$store.state.solutions.definition
    },
    currentCodeLink() {
      return this.codeLink[this.value]
    },
  },
  watch: {
    'definition.sampleCodes': {
      handler(newValue) {
        this.value = newValue.JAVA ? 'JAVA' : 'JAVA_COMMON'
        this.langType = newValue.JAVA ? 'business' : 'basic'
      },
      immediate: true
    }
  },
  methods: {
    copy() {
      var Url2 = this.$refs[this.value].querySelector('code').innerText
      var oInput = document.createElement('textarea') //创建一个隐藏input
      oInput.value = Url2 //赋值
      document.body.appendChild(oInput)
      oInput.select() // 选择对象
      document.execCommand('Copy') // 执行浏览器复制命令
      oInput.className = 'oInput'
      oInput.style.display = 'none'
      this.$message.success('复制成功')
    },
    // 切换语言类型
    changeLang(row) {
      this.langType = row.includes('JAVA') ? 'business' : ''
      this.value = row
      this.renderKey += 1
    },
    changeLangType(type) {
      this.langType = type
      this.renderKey += 1
    }
  }
}
</script>
<style lang="less" scoped>
	.code-wrap {
    .select-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap;
      margin-bottom: 0;
      .lang {
        flex-shrink: 0;
        margin-bottom: 12px;
        > .label {
          font-weight: 600;
        }
      }
      .code-link {
        margin-bottom: 12px;
      }
    }
		.java-type {
      font-weight: 600;
      margin-bottom: 16px;
      display: flex;
      align-items: baseline;
      .type-txt {
        
      }
      .sdk-txt {
        font-size: 13px;
        cursor: pointer;
        font-weight: 500;
        > span {
          padding: 10px 0;
        }
        > span:nth-child(1) {
          margin-right: 10px;
        }
        .actived {
          border-bottom: 2px solid #52BF63;
          color: #52BF63;
        }
      }
    }
    .code-content {
			position: relative;
			cursor: pointer;
			.copy-btn {
				position: absolute;
				right: 20px;
				top: 5px;
				z-index: 100;
				display: none;
			}
			&:hover {
				.copy-btn {
					display: block;
				}
			}
			.pre {
				height: 460px;
				overflow-y: auto;
			}
		}
	}
</style>
