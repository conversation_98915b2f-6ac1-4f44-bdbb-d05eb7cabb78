<template>
	<div class="faq-wrap" style="padding-top: 24px;">
		<div class="header">
			<h1 class="header-title">{{$t('menu.questionTitle')}}</h1>
		</div>
    <template v-for="item in faqList">
      <FaqItem :item="item" :key="item.id" />
    </template>
	</div>
</template>

<script>
import FaqItem from '@/components/FaqItem'
export default {
  props: ['faqList'],
  components: {
    FaqItem
  },
}
</script>

<style lang="less">
	.faq-wrap {
		.header {
			border-bottom: 1px solid rgba(232, 232, 232, 1);
			padding-bottom: 12px;
			margin-bottom: 24px;
			.header-title {
				max-width: 100%;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
				vertical-align: bottom;
				padding-right: 4px;
				margin-bottom: 0 !important;
			}
		}
	}
</style>
