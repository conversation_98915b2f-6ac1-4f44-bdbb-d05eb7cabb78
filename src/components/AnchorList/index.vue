<template>
<div class="toc-fix">
  <low-code-renderer v-if="schema.components" :schema="schema" />
  <a-anchor
    v-if="anchorList.length > 1"
    @click="handleClick"
    @change="onAnchorChange"
    :targetOffset="80" 
    :wrapperStyle="{}" 
    :affix="false"
  >
    <template v-for="item in anchorList">
      <a-anchor-link
        v-if="item.children && item.children.length < 1"
        :href="item.id"
        :key="item.id"
        :title="item.title"
      />
      <a-anchor-link
        v-else
        :href="item.id"
        :key="item.id"
        :title="item.title"
        :class="{
          'api-anchor-wrap': item.children.find(
            item => item.id === currentAnchor
          )
        }"
      >
        <a-anchor-link
          v-for="item2 in item.children"
          :href="item2.id"
          :key="item2.id"
          :title="item2.title"
        />
      </a-anchor-link>
    </template>
  </a-anchor>
</div>
</template>

<script>
import { fetchSchema } from '@/utils/lowcode'
export default {
  props: ['anchorList'],
  data() {
    return {
      currentAnchor: '',
      schema: {}
    }
  },
  computed: {
    scrollTop() {
      return this.$store.state.scrollTop
    },
  },
  async mounted() {
    this.schema = await fetchSchema('feed_back_V2')
  },
  methods: {
    onAnchorChange(currentAnchor) {
      this.currentAnchor = currentAnchor
    },
    handleClick(e) {
      e.preventDefault()
    },
  }
}
</script>
<style lang="less" scoped>
.toc-fix {
  position: sticky;
  top: 80px;
  z-index: 997;
  padding-top: 20px;
}
</style>
