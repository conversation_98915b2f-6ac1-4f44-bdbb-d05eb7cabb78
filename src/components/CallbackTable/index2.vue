<template>
  <div>
    <div v-if="description" class="text" style="padding: 8px;">
      <p v-html="description" class="callback-desc"></p>
    </div>
    <a-row :gutter="40">
      <a-col :sm="24" :md="24" :lg="12" class="print-col-wrap">
        <ParamsBody 
          v-if="result.reqParams && result.reqParams[radioValue]" 
          :title="$t('tongzhicanshu')"
          :list="result.reqParams[radioValue]"
          :apiGroup="result.apiGroup"
          :type="radioValue"
        />
      </a-col>
      <a-col :sm="24" :md="24" :lg="12" class="code-col-wrap" v-if="result.reqExamples && result.reqExamples[radioValue]">
        <ResCode :title="$t('shilibaowen')" :code="result.reqExamples[radioValue]"  />
      </a-col>
    </a-row>
  </div>
</template>

<script>
import ResCode from '@/components/CodeExm/resCode'
import ParamsBody from './paramsBody'
import api from '@/api'
export default {
  props: ['spiName', 'columns', 'currentApiId'],
  components: {
    ResCode,
    ParamsBody,
  },
  data() {
    return {
      result: {},
      radioValue: 'application/json',
      apiBasicData: {}
    }
  },
  watch: {
    'result.reqParams': function (newValue) {
      this.radioValue = Object.keys(newValue)[0]
    },
    radioValue: function (newValue) {
      this.$emit(
        'getBaowen',
        this.result.reqExamples && this.result.reqExamples[newValue]
      )
    },
  },
  computed: {
    description() {
      const { description, title } = this.result
      if(!description || !description.trim()) return ''
      if (description.trim() === title.trim()) {
        return ''
      }
      return description
    }
  },
  created() {
    this.getCallbackSpis()
    this.getSpiApiDatas()
  },
  methods: {
    getCallbackSpis() {
      api
        .getCallbackSpis({
          spiName: this.spiName,
        })
        .then((res) => {
          this.result = res
          this.$emit(
            'getBaowen',
            this.result.reqExamples && this.result.reqExamples[this.radioValue]
          )
        })
    },
    // 获取了spi 再去获取查单api 相关关系
    getSpiApiDatas() {
      this.$store.dispatch('apiDocs/getSpiApiMap', {
        apiId: this.currentApiId, 
        spi: this.spiName
      })
    },
  },
}
</script>
<style lang="less">
.callback-desc {
  ul { 
    li {
      list-style:disc inside;
    }
  }  
  ol { 
    li {
      list-style:decimal inside;
    }
  }  
}
</style>
