<template>
  <div>
    <div v-if="result.description" class="text" style="margin-bottom: 12px">
      <p v-html="result.description" class="callback-desc"></p>
    </div>
    <h4 style="margin-top: 0" id="anchor7-1">
      {{ $t('tongzhicanshu') }}
    </h4>
    <div class="select-header">
      <div class="lang" v-if="result.reqParams">
        <div class="label">格式：</div>
        <a-radio-group
          v-model="radioValue"
          v-if="result.reqParams && result.reqParams.length > 1"
        >
          <template v-for="item in Object.keys(result.reqParams)">
            <a-radio-button :value="item" :key="item">
              {{ item }}
            </a-radio-button>
          </template>
        </a-radio-group>
        <div class="text" v-else>
          {{ Object.keys(result.reqParams)[0] }}
        </div>
      </div>
    </div>
    <PageTable
      v-if="result.reqParams && result.reqParams[radioValue]"
      :columns="columns"
      :apiGroup="result.apiGroup"
      :list="result.reqParams[radioValue]"
    />
    <h4
      v-if="result.reqExamples && result.reqExamples[radioValue]"
      id="anchor7-2"
    >
      {{ $t('shilibaowen') }}
    </h4>
    <Code
      v-if="result.reqExamples && result.reqExamples[radioValue]"
      :code="result.reqExamples[radioValue]"
      :key="result.title"
    />
  </div>
</template>

<script>
import Code from '@/components/Code'
import PageTable from '@/components/PageTable'
import api from '@/api'
export default {
  props: ['spiName', 'columns', 'currentApiId'],
  components: {
    Code,
    PageTable,
  },
  data() {
    return {
      result: {},
      radioValue: 'application/json',
      apiBasicData: {}
    }
  },
  watch: {
    'result.reqParams': function (newValue) {
      this.radioValue = Object.keys(newValue)[0]
    },
    radioValue: function (newValue) {
      this.$emit(
        'getBaowen',
        this.result.reqExamples && this.result.reqExamples[newValue]
      )
    },
  },
  created() {
    this.getCallbackSpis()
    this.getSpiApiDatas()
  },
  methods: {
    getCallbackSpis() {
      api
        .getCallbackSpis({
          spiName: this.spiName,
        })
        .then((res) => {
          this.result = res
          this.$emit(
            'getBaowen',
            this.result.reqExamples && this.result.reqExamples[this.radioValue]
          )
        })
    },
    // 获取了spi 再去获取查单api 相关关系
    getSpiApiDatas() {
      this.$store.dispatch('apiDocs/getSpiApiMap', {
        apiId: this.currentApiId, 
        spi: this.spiName
      })
    },
  },
}
</script>
<style lang="less">
.callback-desc {
  ul { 
    li {
      list-style:disc inside;
    }
  }  
  ol { 
    li {
      list-style:decimal inside;
    }
  }  
}
</style>
