<template>
  <div class="paramsBody-wrap">
    <div class="header">
      <div class="title">{{ title }}</div>
    </div>
    <template v-if="sortList.length > 0">
      <Item  v-for="item in sortList" :data="item" :key="item.name" :apiGroup="apiGroup" />
    </template>
  </div>
</template>
<script>
import Mixin from '../Params/mixin'
export default {
  mixins: [Mixin],
  props: ['type'],
  computed: {
    sortList() {
      const requiredList = this.newList.filter(l => l.required)
      const noRequiredList = this.newList.filter(l => !l.required)
      return requiredList.concat(noRequiredList)
    }
  }
}
</script>
<style lang="less" scoped>
.paramsBody-wrap {
  font-family: PingFangSC, PingFang SC;
  .header {
    height: 56px;
    background: #FFFFFF;
    box-shadow: 0px 1px 0px 0px rgba(0,0,0,0.06);
    display: flex;
    align-items: center;
    cursor: pointer;
    .title {
      height: 24px;
      font-weight: 500;
      font-size: 18px;
      color: rgba(0,0,0,0.65);
      line-height: 24px;
      margin-right: 8px;
    }
    .desc {
      height: 20px;
      font-weight: 400;
      font-size: 14px;
      color: rgba(0,0,0,0.45);
      line-height: 20px;
      flex: 1;
    }
    .operate {
      height: 20px;
      font-weight: 400;
      font-size: 14px;
      color: rgba(0,0,0,0.65);
      line-height: 20px;
    }
  }
  .fold-item {
    display: flex;
    justify-items: flex-start;
    .icon {
      cursor: pointer;
      width: 16px;
      height: 16px;
      line-height: 16px;
      text-align: center;
      font-size: 13px;
      margin-right: 8px;
      margin-top: 25px;
    }
  }
}
</style>