<template>
  <div class="header-wrap">
    <div
      class="header"
      v-if="!isMobile"
      :class="{
        fixed: scrollTop > 0 && enableFixed,
        enableFixedTrue: enableFixed,
        enableFixedFalse: !enableFixed,
      }"

    >
      <div
        class="header-content"
        :style="{
          width: maxWidth,
        }"
      >
        <a class="logo" href="/home">
          <div class="img"></div>
        </a>
        <div class="header-menu">
          <ul class="menu-list">
            <li
              class="menu-item"
              :class="{
                active: getTitle() === item.title,
              }"
              :hasChildren="
                item.children && item.children.length > 0 ? '1' : '0'
              "
              v-for="item in menuList"
              :key="item.title"
            >
              <template v-if="item.contentUri">
                <a
                  :href="item.contentUri"
                  :class="{ active: getTitle() === item.title }"
                  >{{ $t(item.title) }}</a
                >
              </template>
              <template v-else>
                {{ $t(item.title) }}
              </template>
              <template v-if="item.children && item.children.length > 0">
                <div class="sub-menu-wrap sub-menu-inline">
                  <ul
                    class="sub-menu-list"
                    v-for="subMenu in item.children"
                    :key="subMenu.title"
                    :style="{ width: getWidth(subMenu.title) }"
                  >
                    <li class="sub-menu-title">{{ $t(subMenu.title) }}</li>
                    <template
                      v-if="subMenu.children && subMenu.children.length > 0"
                    >
                      <div class="sub-menu-item-wrap">
                        <li
                          class="sub-menu-item"
                          :style="
                            subMenu.title === 'menu.hyfa'
                              ? 'margin-right: 24px'
                              : ''
                          "
                          v-for="subMenu2 in subMenu.children"
                          :key="subMenu2.title"
                        >
                          <a
                            :class="{
                              active: subActive(subMenu2.contentUri),
                            }"
                            :href="subMenu2.contentUri"
                            :target="subMenu2.target || '_self'"
                            >{{ $t(subMenu2.title) }}</a
                          >
                        </li>
                      </div>
                    </template>
                  </ul>
                </div>
              </template>
            </li>
          </ul>
        </div>
        <div class="header-lang">
          <div class="search-wrap">
            <SearchInput :fixed="enableFixed && !(scrollTop > 0)" />
          </div>
          <div class="login-btn-wrap">
            <a
              class="login-btn"
              :href="bzUrl"
              target="_blank"
            >
              {{ $t('loginDeveloper') }}
            </a>
            <div class="sub-login-wrap">
              <ul class="sub-login-list">
                <li class="sub-login-item" style="margin-bottom: 15px">
                  <a
                    :href="bzUrl"
                    target="_blank"
                  >
                    {{ $t('biaozhunLogin') }}
                  </a>
                </li>
                <li class="sub-login-item">
                  <a :href="kjUrl" target="_blank">{{ $t('kuajingLogin') }}</a>
                </li>
              </ul>
            </div>
          </div>
          <a-tooltip placement="bottom">
            <template slot="title">
              <span>{{ $t('langTitle') }}</span>
            </template>
            <div class="change-lang" @click="changeLang">
              <svg
                t="1616640934958"
                v-if="langType === 'en'"
                class="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="1517"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                width="24"
                height="24"
              >
                <path
                  d="M896 0a128 128 0 0 1 128 128v768a128 128 0 0 1-128 128H128a128 128 0 0 1-128-128V128a128 128 0 0 1 128-128h768z m0 42.688H128c-44.352 0-80.832 33.792-84.928 77.12L42.688 128v768c0 44.352 33.792 80.832 77.12 84.928l8.192 0.384h768c44.352 0 80.832-33.792 84.928-77.12l0.384-8.192V128c0-44.352-33.792-80.832-77.12-84.928L896 42.688z"
                  p-id="1518"
                />
                <path
                  d="M525.568 810.56V606.912h171.456v37.632h43.008V363.776H525.568v-107.52h-44.16v107.52H267.52v280.768h43.008v-37.632h170.88v203.648h44.16z m-44.16-245.504h-170.88V405.568h170.88V565.12z m215.616 0H525.568V405.568h171.456V565.12z"
                  p-id="1519"
                />
              </svg>
              <svg
                t="1616640952893"
                v-else
                class="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="1648"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                width="24"
                height="24"
              >
                <path
                  d="M896 0a128 128 0 0 1 128 128v768a128 128 0 0 1-128 128H128a128 128 0 0 1-128-128V128a128 128 0 0 1 128-128h768z m0 42.688H128c-44.352 0-80.832 33.792-84.928 77.12L42.688 128v768c0 44.352 33.792 80.832 77.12 84.928l8.192 0.384h768c44.352 0 80.832-33.792 84.928-77.12l0.384-8.192V128c0-44.352-33.792-80.832-77.12-84.928L896 42.688z"
                  p-id="1649"
                />
                <path
                  d="M526.08 725.312v-42.368H262.656V526.4h238.912v-42.368H262.656V341.248h253.248v-42.432H213.76v426.496H526.08z m109.888 0V538.944c1.792-28.032 10.752-50.176 27.52-66.88 15.488-15.552 34.048-23.296 55.552-23.296 53.76 0 81.216 29.888 81.216 90.24v186.304h47.808V535.36c0-84.8-38.848-127.232-115.328-127.232-20.288 0-38.784 4.8-55.552 14.336-16.704 8.96-30.464 22.144-41.216 39.424v-45.376h-47.744v308.8h47.744z"
                  p-id="1650"
                />
              </svg>
            </div>
          </a-tooltip>
        </div>
      </div>
    </div>
    <div v-if="isMobile" class="mobile">
      <div
        class="mobile-nav"
        :class="{ 'mobile-nav-scroll': (scrollTop > 0 || mobileLeftShow) || !enableFixed }"
        v-if="isMobile && (enableFixed || useMobile)"
      >
        <a class="logo" href="/mhome">
          <div class="img"></div>
        </a>
        <div class="right-icon" @click="showMenu">
          <a-icon class="icon" type="menu" v-if="!mobileLeftShow" />
          <a-icon class="icon" type="close" v-else />
        </div>
      </div>
      <div class="mobile-left-menu" v-if="mobileLeftShow">
        <MobileSearchInput needSubMenu />
        <div class="submenu">
          <a-menu style="min-height: calc(100vh - 300px)" mode="inline">
            <template v-for="item in menuList">
              <template v-if="item.children && item.children.length > 0">
                <a-sub-menu :key="item.title">
                  <span slot="title">
                    {{ $t(item.title) }}
                  </span>
                  <a-menu-item key="3" style="height: auto; padding: 0">
                    <div
                      class="mobile-menu-level2"
                      v-for="item2 in item.children"
                      :key="item2.title"
                    >
                      <div class="title">
                        {{ $t(item2.title) }}
                      </div>
                      <ul class="mobile-menu-level2-list">
                        <li
                          class="mobile-menu-level2-item"
                          v-for="item3 in item2.children"
                          :key="item3.title"
                        >
                          <a
                            :href="item3.contentUri"
                            :class="{
                              active: subActive(item3.contentUri),
                            }"
                            :target="item3.target || '_self' "
                          >
                            {{ $t(item3.title) }}
                          </a>
                        </li>
                      </ul>
                    </div>
                  </a-menu-item>
                </a-sub-menu>
              </template>
              <template v-else>
                <a-menu-item :key="item.title">
                  <a class="arrow-right" style="color: rgba(0, 0, 0, 0.85)" :href="item.contentUri">
                    {{ $t(item.title) }}
                  </a>
                </a-menu-item>
              </template>
            </template>
          </a-menu>
          <div class="mobile-footer">
            <a
              class="btn"
              href="https://mp.yeepay.com/auth/signin?redirectUrl=https://mp.yeepay.com/yop-developer-center/cas?redirectUrl=https://mp.yeepay.com/mp-developer-center/index.html"
              target="_blank"
              >{{ $t('loginDeveloperM') }}</a
            >
            <a class="btn2" :href="kjUrl" target="_blank">{{
              $t('kuajingLogin')
            }}</a>
            <div class="change-lang" @click="changeLang">
              {{ langType === 'en' ? '中文' : 'English' }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import header from '@/mixins/header'
import SearchInput from '@/components/SearchInput'
import MobileSearchInput from '@/components/SearchInput/mobileSearchInput'
export default {
  components: {
    SearchInput,
    MobileSearchInput,
  },
  mixins: [header],
  data() {
    return {
      subMenu: false,
      enableFixed: !!this.$route.meta.enableFixed,
      useMobile: !!this.$route.meta.useMobile,
      maxWidth: this.$route.meta.maxWidth ? this.$route.meta.maxWidth : '100%',
      menuList: [
        {
          title: 'menu.home',
          contentUri: '/home',
        },
        {
          title: 'menu.product',
          contentUri: '/productCenter',
        },
        {
          title: 'menu.solution',
          contentUri: '/solution',
          children: [
          {
              title: 'menu.hyfa',
              hasContent: false,
              children: [
                {
                  title: 'menu.retail',
                  contentUri: '/solution/detail?solution=ls',
                },
                {
                  title: 'menu.catering',
                  contentUri: '/solution/detail?solution=cy',
                },
                {
                  title: 'menu.consumptionFinancial',
                  contentUri: '/solution/detail?solution=xf',
                },
                {
                  title: 'menu.airTravel',
                  contentUri: '/solution/detail?solution=hl',
                },
                {
                  title: 'menu.administrative',
                  contentUri: '/solution/detail?solution=zw',
                },
                {
                  title: 'menu.energy',
                  contentUri: '/solution/detail?solution=ny',
                },
                {
                  title: 'menu.cosmeticMedicine',
                  contentUri: '/solution/detail?solution=ym',
                },
                {
                  title: 'menu.nighttimeEconomy',
                  contentUri: '/solution/detail?solution=yjj',
                },
              ],
            },
            {
              title: 'menu.tyfa',
              hasContent: false,
              children: [
                {
                  title: 'menu.bzshsfk',
                  contentUri: '/docs/products/bzshsfk/index.html',
                },
                {
                  title: 'menu.ptssfk',
                  contentUri: '/docs/products/ptssfk/index.html',
                },
                {
                  title: 'menu.fwssfk',
                  contentUri: '/docs/products/fwssfk/index.html',
                },
                {
                  title: 'menu.dlssfk',
                  contentUri: '/docs/products/dlssfk/index.html',
                },
              ],
            },
          ],
        },
        {
          title: 'menu.developer',
          hasContent: false,
          children: [
            {
              title: 'menu.guideTitle',
              hasContent: false,
              children: [
                {
                  title: 'menu.userGuide',
                  contentUri: '/docs/open/platform-doc/user-guide-sm',
                },
                {
                  title: 'menu.certIntro',
                  contentUri: '/docs/open/platform-doc/sign_sm/cert_intro_sm',
                },
                {
                  title: 'menu.sdkGuide',
                  contentUri: '/docs/open/platform-doc/sdk_guide-sm/sdk-guide-sm',
                },
                {
                  title: 'menu.notifySummary',
                  contentUri: '/docs/open/platform-doc/notifys/notify-summary-sm',
                },
              ],
            },
            {
              title: 'menu.apiDocsTitle',
              hasContent: false,
              children: [
                {
                  title: 'menu.apiAll',
                  contentUri: '/docs',
                },
                {
                  title: 'menu.apiOverview',
                  contentUri: '/docs-v2',
                },
              ],
            },
            {
              title: 'menu.developTools',
              hasContent: false,
              children: [
                {
                  title: 'menu.sdkTools',
                  contentUri: '/docs/open/platform-doc/developTools-sm/keyTools-sm',
                },
                {
                  title: 'menu.platformSdk',
                  contentUri: '/docs/open/platform-doc/developTools-sm/platform-sdk-sm',
                },
                {
                  title: 'menu.yopIsvGateway',
                  contentUri: '/docs/open/platform-doc/notifys/yop-isv-gateways-sm',
                },
                {
                  title: 'menu.yizhanshi',
                  contentUri: '/docs/open/platform-doc/developTools-sm/developer_tools-vscode-sm',
                },
                {
                  title: 'accessDiagnosis',
                  contentUri: window.location.hostname === 'open.yeepay.com' ? 'https://mp.yeepay.com/auth/signin?redirectUrl=https://mp.yeepay.com/yop-developer-center/cas?redirectUrl=https://mp.yeepay.com/mp-developer-center/index.html#/dev-services/access-diagnosis' : 'https://qamp.yeepay.com/auth/signin?redirectUrl=https://qamp.yeepay.com/yop-developer-center/cas?redirectUrl=https://qamp.yeepay.com/mp-developer-center/index.html#/dev-services/access-diagnosis'
                },
              ],
            },
            {
              title: 'menu.deployTool',
              hasContent: false,
              children: [
                {
                  title: 'menu.rongqiyun',
                  contentUri: 'https://marketplace.huaweicloud.com/contents/aa540296-a953-488f-8f01-0fd942edfabd',
                  target: '_blank'
                },
              ],
            },
          ],
        },
      ],
    }
  },
  computed: {
    kjUrl() {
      const hostname = window.location.hostname
      if (hostname === 'open.yeepay.com') {
        return 'https://mp.yeepay.com/mp-developer-center/index.html#/login'
      }
      return 'https://qamp.yeepay.com/mp-developer-center/index.html#/login'
    },
    bzUrl() {
      const hostname = window.location.hostname
      if (hostname === 'open.yeepay.com') {
        return 'https://mp.yeepay.com/auth/signin?redirectUrl=https://mp.yeepay.com/yop-developer-center/cas?redirectUrl=https://mp.yeepay.com/mp-developer-center/index.html'
      }
      return 'https://qamp.yeepay.com/auth/signin?redirectUrl=https://qamp.yeepay.com/yop-developer-center/cas?redirectUrl=https://qamp.yeepay.com/mp-developer-center/index.html'
    },
    scrollBarWidth() {
      return this.$store.state.scrollBarWidth
    },
    lock() {
      return this.$store.state.lock
    },
    mobileLeftShow() {
      return this.$store.state.mobileLeftShow
    },
    langType() {
      return this.$store.state.langType
    },
    isMobile() {
      return this.$store.state.isMobile
    },
    scrollTop() {
      return this.$store.state.scrollTop
    },
  },
  watch: {
    $route(newValue) {
      this.enableFixed = !!newValue.meta.enableFixed
      this.useMobile = !!newValue.meta.useMobile
      this.maxWidth = newValue.meta.maxWidth ? newValue.meta.maxWidth : 'none'
    },
    mobileLeftShow: {
      handler(newValue) {
        this.$store.dispatch('toggleLock', newValue)
      },
      immediate: true,
    },
    subMenu(newValue) {
      this.$store.dispatch('toggleLock', newValue)
    },
  },
  methods: {
    getTitle() {
      const path = this.$route.path
      if (
        path.indexOf('/platform') !== -1 ||
        path === '/docs' ||
        path.indexOf('/docs/apis') !== -1 ||
        path === '/docs-v2' ||
        path.indexOf('/docs-v2/apis') !== -1
      ) {
        return 'menu.developer'
      }
      if (/\/products\/(ptssfk|bzshsfk|fwssfk|dlssfk)/g.test(path)) {
        return 'menu.solution'
      }
      if (/\/solution/g.test(path)) {
        if (/\/solutions/g.test(path)) {
          return ''
        }
        return 'menu.solution'
      }
      if (/\/(products|productCenter)/g.test(path)) {
        return 'menu.product'
      }
      return 'menu.home'
    },
    subActive(url) {
      const path = this.$route.path
      if (path === '/docs' && url === '/docs') {
        return true
      }
      if (/\/products/g.test(path)) {
        const name = path.split('products/')[1]
        if (url.indexOf(name) !== -1) {
          return true
        }
      }
      if (/\/platform/g.test(path)) {
        const name = path.split('platform/')[1]
        if (url.indexOf(name) !== -1) {
          return true
        }
      }
      if (/\/solution/g.test(path)) {
        if (url === this.$route.fullPath) {
          return true
        }
      }
      return false
    },
    getWidth(title) {
      if (this.langType === 'cn') {
        switch (title) {
          case 'menu.tyfa':
            return '160px'
          case 'menu.hyfa':
            return '260px'
          case 'menu.zfcp':
            return '110px'
          case 'menu.developTools':
            return '110px'
          default:
            return '96px'
        }
      } else {
        switch (title) {
          case 'menu.tyfa':
            return '180px'
          case 'menu.hyfa':
            return '260px'
          case 'menu.zfcp':
          case 'menu.zjgl':
            return '180px'
          default:
            return '110px'
        }
      }
    },
    showSubMenu() {
      this.subMenu = !this.subMenu
      this.$store.commit('setMobileLeftShow', false)
    },
    showMenu() {
      this.subMenu = false
      this.$store.commit('toggleMobileLeftShow')
    },
  },
}
</script>
<style lang="less" scoped>
.header-wrap {
  position: sticky;
  top: 0;
  z-index: 10000;
}
.header {
  height: 64px;
  line-height: 64px;
  margin-bottom: 1px;
  position: relative;
  right: 0;
  box-shadow: 0px 1px 4px 0px rgba(0, 21, 41, 0.12);
  .header-content {
    margin: 0 auto;
  }
  a {
    transition: none;
  }
  .logo {
    display: inline-block;
    line-height: 64px;
    cursor: pointer;
    margin-right: 53px;
    margin-left: 16px;
    .img {
      display: inline-block;
      vertical-align: middle;
      height: 32px;
      width: 201px;
      background: url('https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>')
        no-repeat center;
      background-size: contain;
    }
  }
  .header-menu {
    margin-left: 20px;
    display: inline-block;
    vertical-align: top;
    .menu-list {
      .menu-item {
        display: inline-block;
        line-height: 64px;
        padding: 0 28px;
        &:first-child {
          padding: 0;
        }
        &:nth-child(2) {
          padding-left: 56px;
        }
        &:last-child {
          margin-right: 0;
        }
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serifSC;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.95);
        cursor: pointer;
        position: relative;
        z-index: 998;
        a {
          color: rgba(255, 255, 255, 0.95);
          &:hover {
            color: #52bf63;
          }
        }
        &:hover {
          color: #52bf63;
          .sub-menu-wrap {
            display: block;
          }
          &[hasChildren='1']::before {
            content: '';
            position: absolute;
            z-index: 1;
            top: 53px;
            left: 50%;
            margin-left: -6px;
            border: 6px solid;
            border-color: transparent transparent #fff transparent;
          }
        }
        .sub-menu-wrap {
          display: none;
          line-height: 22px;
          position: absolute;
          padding: 40px;
          left: 62px;
          top: 64px;
          transform: translateX(-50%);
          border-radius: 10px;
          background: #fff;
          box-shadow: 0px -2px 10px 0px rgba(0, 0, 0, 0.13);
          white-space: nowrap;
          .sub-menu-list {
            width: 96px;
            &:last-child {
              margin-right: 0;
            }
            margin-right: 16px;
            display: inline-block;
            vertical-align: top;
            .sub-menu-title {
              height: 20px;
              font-size: 14px;
              font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei',
                SimSun, sans-serifSC;
              font-weight: 500;
              color: #000000;
              line-height: 20px;
              margin-bottom: 16px;
            }
            .sub-menu-item-wrap {
              white-space: pre-wrap;
              .sub-menu-item {
                display: inline-block;
                min-width: 50px;
                height: 17px;
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei',
                  SimSun, sans-serif;
                font-weight: 400;
                color: rgba(0, 0, 0, 0.65);
                line-height: 17px;
                margin-bottom: 8px;
                a {
                  color: rgba(0, 0, 0, 0.65);
                  &:hover {
                    color: #52bf63;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  .header-lang {
    float: right;
    margin-right: 22px;
    height: 64px;
    .search-wrap {
      display: inline-block;
      vertical-align: middle;
      height: 64px;
      margin-right: 12px;
    }
    .login-btn-wrap {
      display: inline-block;
      vertical-align: middle;
      position: relative;
      &:hover {
        .sub-login-wrap {
          display: block;
        }
      }
      .sub-login-wrap {
        display: none;
        position: absolute;
        top: 30px;
        left: 30px;
        transform: translateX(-50%);
        z-index: 1000;
        padding-top: 10px;
        .sub-login-list {
          background: #ffffff;
          box-shadow: 0px -2px 10px 0px rgba(0, 0, 0, 0.13);
          padding: 16px;
          border-radius: 5px;
          .sub-login-item {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei',
              SimSun, sans-serif;
            font-weight: 400;
            white-space: nowrap;
            a {
              color: #000000;
            }
            line-height: 20px;
            &:hover {
              a {
                color: #52bf63;
              }
            }
          }
        }
      }
    }
    .login-btn {
      display: block;
      border-radius: 2px;
      height: 32px;
      width: 60px;
      text-align: center;
      background: #52bf63;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 400;
      color: #ffffff;
      line-height: 32px;
      margin-right: 16px;
      cursor: pointer;
      &:hover {
        background: #78cc82;
      }
    }
    .change-lang {
      width: 24px;
      height: 24px;
      line-height: 24px;
      display: inline-block;
      vertical-align: middle;
      cursor: pointer;
      font-size: 14px;
      fill: rgba(255, 255, 255, 0.65);
      &:hover {
        fill: rgba(255, 255, 255, 0.45);
      }
    }
  }
}
.enableFixedTrue {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 99;
  background-color: transparent;
  box-shadow: none;
}
.enableFixedFalse {
  background-color: #fff;
  // box-shadow: 0px 0px 1px 0px #d8d8d8;
  .logo {
    .img {
      display: inline-block;
      vertical-align: middle;
      height: 32px;
      width: 201px;
      background: url('https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>')
        no-repeat center;
      background-size: contain;
    }
  }
  .header-menu {
    .menu-list {
      .menu-item {
        a {
          color: rgba(0, 0, 0, 0.65);
        }
        color: rgba(0, 0, 0, 0.65);
      }
    }
  }
  .header-lang {
    .change-lang {
      fill: rgba(0, 0, 0, 0.65);
      &:hover {
        fill: rgba(0, 0, 0, 0.45);
      }
    }
  }
}
.fixed {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0px 1px 2px 0px rgba(0, 21, 41, 0.12);
  .logo {
    display: inline-block;
    line-height: 64px;
    cursor: pointer;
    margin-right: 59px;
    .img {
      display: inline-block;
      vertical-align: middle;
      height: 32px;
      width: 201px;
      background: url('https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>')
        no-repeat center;
      background-size: contain;
    }
  }
  .header-menu {
    .menu-list {
      .menu-item {
        color: rgba(0, 0, 0, 0.65);
        a {
          color: rgba(0, 0, 0, 0.65);
        }
      }
    }
  }
  .header-lang {
    .change-lang {
      fill: rgba(0, 0, 0, 0.65);
      &:hover {
        fill: rgba(0, 0, 0, 0.45);
      }
    }
  }
}
.mobile-nav {
  background-color: transparent !important;
  box-shadow: 0px 1px 4px 0px rgba(0, 21, 41, 0.12) !important;
  .logo {
    position: absolute;
    top: 9px;
    left: 15px;
    .img {
      height: 32px;
      width: 201px;
      background: url('https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>')
        no-repeat center;
      background-size: contain;
    }
  }
  .right-icon {
    position: absolute;
    top: 13px;
    right: 15px;
    .icon {
      color: #fff;
    }
  }
}
.mobile-nav-scroll {
  background-color: #fff !important;
  .logo {
    .img {
      height: 32px;
      width: 201px;
      background: url('https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>')
        no-repeat center;
      background-size: contain;
    }
  }
  .right-icon {
    position: absolute;
    top: 13px;
    right: 15px;
    .icon {
      color: #000;
    }
  }
}
.mobile-menu-level2 {
  .title {
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
      sans-serif;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    line-height: 22px;
    padding: 20px 0 9px;
    border-bottom: 1px solid #efefef;
    margin-bottom: 20px;
  }
  .mobile-menu-level2-list {
    display: flex;
    flex-wrap: wrap;
    .mobile-menu-level2-item {
      min-width: 84px;
      height: 20px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
      line-height: 20px;
      margin: 0 26px 20px 0;
      a {
        color: rgba(0, 0, 0, 0.65);
      }
    }
  }
}
.mobile-left-menu {
  .arrow-right {
    position: relative;
    &::after, &::before {
      position: absolute;
      width: 6px;
      height: 1px;
      background: #fff;
      background: rgba(0, 0, 0, 0.65) \9;
      background-image: linear-gradient(to right, rgba(0, 0, 0, 0.65), rgba(0, 0, 0, 0.65));
      background-image: none \9;
      border-radius: 2px;
      transition: background 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), top 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      content: '';
      top: 20px;
      right: 2px;
      left: auto;
    }
    &::before {
      transform: rotate(-225deg) translateX(2px);
    }
    &::after {
      transform: rotate(225deg) translateX(2px);
    }
  }
  /deep/.ant-menu {
    padding: 0;
    .ant-menu-sub {
      padding: 0;
    }
    .ant-menu-sub {
      background-color: #fbfbfb;
    }
  }
  /deep/.ant-menu-item {
    padding: 0 20px !important;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
      sans-serif;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    line-height: 40px;
    &::after {
      display: none !important;
    }
  }
  /deep/ .ant-menu:not(.ant-menu-horizontal) {
    .ant-menu-item-selected {
      background-color: #fff;
    }
  }
  /deep/.ant-menu-submenu-title {
    padding: 0 20px !important;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
      sans-serif;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    line-height: 40px;
    padding: 14 0;
    .ant-menu-submenu-arrow {
      right: 20px !important;
    }
  }
}
.mobile-footer {
  margin-top: 20px;
  font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
    sans-serifSC;
  .btn {
    width: 335px;
    height: 50px;
    background: #52bf63;
    border-radius: 4px;
    font-size: 17px;
    font-weight: 500;
    color: #ffffff;
    line-height: 50px;
    margin: 0 auto 20px;
    text-align: center;
    display: block;
  }
  .btn2 {
    width: 335px;
    height: 50px;
    background: #fff;
    border-radius: 4px;
    font-size: 17px;
    font-weight: 500;
    color: #666666;
    line-height: 50px;
    margin: 0 auto 20px;
    text-align: center;
    display: block;
    border: 1px solid rgba(0, 0, 0, 0.15);
  }
  .change-lang {
    height: 20px;
    font-size: 14px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.65);
    line-height: 20px;
    text-align: center;
  }
}
.active {
  color: #52bf63 !important;
}
</style>
