<template>
  <div class="error-coder-table-wrap">
    <a-table
      :pagination="false"
      :columns="errorCodeColumns"
      :data-source="print ? errcodeList : displayData"
      row-key="subErrorCode"
      id="table-errorCode"
      style="overflow-y: auto;"
    >
      <div
        slot="filterDropdown"
        slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters }"
        style="padding: 8px; text-align: right"
      >
        <a-textarea
          v-ant-ref="c => (searchInput = c)"
          :placeholder="$t('pleaseInput')"
          :value="selectedKeys[0]"
          :auto-size="{ minRows: 3, maxRows: 5 }"
          style="width: 218px; margin-bottom: 12px; display: block;"
          @change="e => setSelectedKeys(e.target.value ? [e.target.value] : [])"
          @pressEnter="() => handleSearch(selectedKeys, confirm)"
        />
        <a-button
          type="primary"
          size="small"
          style="width: 58px; margin-right: 8px"
          @click="() => handleSearch(selectedKeys, confirm)"
          >{{ $t('serach') }}</a-button
        >
        <a-button
          size="small"
          style="width: 58px"
          @click="() => handleReset(clearFilters)"
          >{{ $t('reset') }}</a-button
        >
      </div>
      <span slot="filterIcon" class="iconfont icon-icon_sousuo" style="font-size: 16px;color: #000;display: flex;align-items: center;justify-content: center;"></span>
      <template slot="customRender" slot-scope="text">
        <template>{{ text }}</template>
      </template>
      <template slot="solution" slot-scope="text">
        <div v-html="text"></div>
      </template>
    </a-table>
    <div
      v-if="errcodeList.length > 10 && !print"
      style="text-align: right; margin-top: 20px"
    >
      <a-pagination
        :default-current="1"
        :current="current"
        :total="errcodeList.length"
        :pageSize="10"
        @change="onPageNoChange" 
        :size="isMobile ? 'small' : 'middle'"
        :showTotal="total => `总共${total}个`"
      />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchText: '',
      current: 1,
      displayData: [],
      errcodeList: [],
      errorCodeColumns: [
        {
          title: () => this.$t('errorCodeTableColumns.title1'),
          dataIndex: 'subErrorCode',
          width: '30%',
          scopedSlots: {
            filterDropdown: 'filterDropdown',
            filterIcon: 'filterIcon',
            customRender: 'customRender'
          },
          onFilterDropdownVisibleChange: visible => {
            if (visible) {
              setTimeout(() => {
                this.searchInput.focus()
              }, 0)
            }
          }
        },
        {
          title: () => this.$t('errorCodeTableColumns.title2'),
          width: '30%',
          dataIndex: 'subErrorMsg'
        },
        {
          width: '40%',
          title: () => this.$t('errorCodeTableColumns.title3'),
          dataIndex: 'outerSolution',
          scopedSlots: { customRender: 'solution' }
        }
      ]
    }
  },
  watch: {
    '$store.state.apiDocs.errcode': {
      handler(newValue) {
        this.errcodeList = newValue
      },
      immediate: true
    },
    errcodeList: {
      handler(newValue) {
        this.displayData = newValue.slice(0, 10)
      },
      immediate: true
    }
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile
    },
    print() {
      return this.$store.state.print
    },
  },
  methods: {
    handleSearch(selectedKeys, confirm) {
      confirm()
      this.searchText = selectedKeys[0]
      this.current = 1
      this.errcodeList = this.errcodeList.filter(item => {
        return item.subErrorCode.indexOf(this.searchText) !== -1
      })
    },
    handleReset(clearFilters) {
      clearFilters()
      this.searchText = ''
      this.current = 1
      this.errcodeList = this.$store.state.apiDocs.errcode
    },
    onPageNoChange(page) {
      this.current = page
      this.displayData = this.errcodeList.slice(
        (page - 1) * 10,
        (page - 1) * 10 + 10
      )
      document.querySelector('#anchor6') && document.querySelector('#anchor6').scrollIntoView()
    }
  }
}
</script>

<style lang="less" scoped>
.error-coder-table-wrap {
  overflow-x: auto;
  overflow-y: hidden;
  /deep/ .ant-table {
    min-width: 700px;
  }
}
</style>
