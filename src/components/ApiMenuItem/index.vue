<template>
  <a-sub-menu
    :key="menuInfo.location"
    v-bind="$props"
    v-on="$listeners"
    :class="`level-${level}`"
  >
    <span slot="title">
      <span>{{ menuInfo.name }}</span>
      <span class="icon iconfont icon-caidan_zhankai custom-sub-menu-icon" style="font-size: 8px"></span>
    </span>
    <template
      v-if="
        menuInfo.children &&
          menuInfo.children.length === 1 &&
          menuInfo.children[0].code === 'others'
      "
    >
      <template v-for="menu in menuInfo.children[0].items">
        <template v-if="menu.show">
          <a-menu-item :class="`level-${level + 1}`" :key="menu.location">{{
            menu.title
          }}</a-menu-item>
        </template>
        <template v-else-if="!menu.latestRef || menu.latestRef === menu.apiId">
          <a-menu-item :class="`level-${level + 1}`" :key="menu.location">{{
            menu.title
          }}</a-menu-item>
        </template>
      </template>
    </template>
    <template v-else>
      <template v-if="menuInfo.children && menuInfo.children.length > 0">
        <ApiMenuItem
          v-for="menu in menuInfo.children"
          :level="level + 1"
          :menu-info="menu"
          :key="menu.location"
          v-bind="$props"
          v-on="$listeners"
        />
      </template>
      <template v-else>
        <template v-for="menu in menuInfo.items">
          <template v-if="menu.show">
            <a-menu-item :class="`level-${level + 1}`" :key="menu.location">{{
              menu.title
            }}</a-menu-item>
          </template>
          <template
            v-else-if="!menu.latestRef || menu.latestRef === menu.apiId"
          >
            <a-menu-item :class="`level-${level + 1}`" :key="menu.location">{{
              menu.title
            }}</a-menu-item>
          </template>
        </template>
      </template>
    </template>
  </a-sub-menu>
</template>

<script>
import { Menu } from 'ant-design-vue'
export default {
  name: 'ApiMenuItem',
  isSubMenu: true,
  props: {
    ...Menu.SubMenu.props,
    // Cannot overlap with properties within Menu.SubMenu.props
    menuInfo: {
      type: Object,
      default: () => ({})
    },
    level: {
      type: Number,
      default: 1
    }
  }
}
</script>
<style>
.custom-sub-menu-icon {
  position: absolute;
  right: 16px;
  transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}
.ant-menu-submenu-open.ant-menu-submenu-inline > .ant-menu-submenu-title .custom-sub-menu-icon {
  transform: rotate(90deg);
}
.ant-menu-submenu-arrow {
  display: none;
}
</style>
