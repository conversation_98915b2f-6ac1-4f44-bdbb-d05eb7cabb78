<template>
	<div class="faq-page-wrap" v-if="faqList.length > 0" >
		<h2 class="header-title" id="faqListAnchor">{{$t('menu.questionTitle')}}</h2>
    <template v-for="item in faqList">
      <FaqItem :item="item" :key="item.id" />
    </template>
	</div>
</template>

<script>
import FaqItem from '@/components/FaqItem'
export default {
  props: ['faqList'],
  components: {
    FaqItem
  },
}
</script>
