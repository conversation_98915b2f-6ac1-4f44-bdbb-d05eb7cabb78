<template>
<a-spin :spinning="spinning">
  <div slot="indicator">
    <img
      v-if="isMobile"
      src="@/assets/images/loading_32.gif"
      class="img-mobile-loading"
      alt
    />
    <img
      v-else
      src="@/assets/images/loading_64.gif"
      class="img-pc-loading"
      alt
    />
  </div>
  <slot></slot>
</a-spin>
</template>
<script>
export default {
  props: ['spinning'],
  computed: {
    isMobile() {
      return this.$store.state.isMobile
    },
  }    
}
</script>