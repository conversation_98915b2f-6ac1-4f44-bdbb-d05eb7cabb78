<template>
	<div class="code-wrap">
		<div class="code-content">
			<div class="copy-btn" v-if="!hiddenCpBtn">
				<a-button
					@click="copy(code)"
					style="width: 40px; height: 18px; font-size: 12px; text-align: center"
				>{{$t('copy')}}</a-button>
			</div>
			<div v-highlight="lang" v-if="code" >
				<pre>
          <code>{{code}}</code>
        </pre>
			</div>
		</div>
	</div>
</template>

<script>
export default {
  props: {
    code: {
      type: String,
    },
    lang: {
      type: String,
    },
    hiddenCpBtn: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    copy(code) {
      var oInput = document.createElement('textarea') //创建一个隐藏input
      oInput.value = code //赋值
      document.body.appendChild(oInput)
      oInput.select() // 选择对象
      document.execCommand('Copy') // 执行浏览器复制命令
      oInput.className = 'oInput'
      oInput.style.display = 'none'
      this.$message.success(this.$t('copySuccess'))
    }
  }
}
</script>
<style lang="less" scoped>
	.code-wrap {
    border-radius: 0 8px;
    overflow-x: hidden;
		.code-content {
			position: relative;
			cursor: pointer;
			.copy-btn {
				position: absolute;
				right: 20px;
				top: 5px;
				z-index: 100;
				padding: 0;
				/deep/ .ant-btn {
					padding: 0;
				}
				display: none;
			}
			&:hover {
				.copy-btn {
					display: block;
				}
			}
			.pre {
				height: 460px;
				overflow-y: auto;
			}
		}
	}
</style>
