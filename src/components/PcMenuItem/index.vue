<template>
	<a-sub-menu :key="menuInfo.location" v-bind="$props" v-on="$listeners" :class="`level-${level}`">
		<span slot="title">
			<span>{{ menuInfo.title }}</span>
		</span>
		<template v-for="(menu) in menuInfo.children">
			<template v-if="menu.children && menu.children.length > 0">
				<PcMenuItem :level="level + 1" :menu-info="menu" :key="menu.location" v-bind="$props" v-on="$listeners" />
			</template>
			<template v-else>
				<a-menu-item :class="`level-${level + 1}`"  :key="menu.location">{{ menu.title }}</a-menu-item>
			</template>
		</template>
	</a-sub-menu>
</template>

<script>
import { Menu } from 'ant-design-vue'
export default {
  name: 'PcMenuItem',
  isSubMenu: true,
   props: {
    ...Menu.SubMenu.props,
    // Cannot overlap with properties within Menu.SubMenu.props
    menuInfo: {
      type: Object,
      default: () => ({}),
    },
    level: {
      type: Number,
      default: 1,
    },
  },
}
</script>

