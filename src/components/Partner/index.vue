<template>
  <div class="partner-wrap">
    <h2>合作伙伴</h2>
    <ul class="partner-box">
      <li v-for="(item, index) in partnerList" :key="index">
        <img :src="item.partnerImage" class="img-style" />
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: ['partnerList'],
  data() {
    return {}
  },
  created() {},
  methods: {}
}
</script>
<style lang="less" scoped>
.partner-wrap {
  text-align: center;
  .partner-box {
    width: 1202px;
    display: flex;
    flex-wrap: wrap;
    margin-left: calc((100% - 1202px) / 2);
    text-align: left;
    li {
      margin: 0 18px 16px 0;
      display: inline-block;
      .img-style {
        width: 182px;
        height: 66px;
      }
    }
  }
}
</style>
