<template>
  <a-drawer
    placement="right"
    :width="width"
    :closable="true"
    :visible="visible"
    @close="onClose"
    :zIndex="998"
    wrapClassName="custom-drawer"
  >
    <a-empty
      v-if="!dialogContent"
      style="margin-top: 200px"
      :image="simpleImage"
    />
    <RenderMarkdown v-else :html="dialogContent" />
    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 1,
      }"
    >
      <a-button type="primary" @click="onClose">关闭</a-button>
    </div>
  </a-drawer>
</template>
<script>
import { Empty } from 'ant-design-vue'
import RenderMarkdown from '../RenderMarkdown'

export default {
  beforeCreate() {
    this.simpleImage = Empty.PRESENTED_IMAGE_SIMPLE
  },
  components: {
    RenderMarkdown
  },
  data() {
    return {
      width: 1000,
    }
  },
	computed: {
		dialogContent() {
			return this.$store.state.dialogContent
		},
		visible() {
			return this.$store.state.visible
    },
  },
  mounted() {
    this.width = document.documentElement.clientWidth - 277 - 48
  },
  methods: {
    onClose() {
      this.$store.commit('setVisible', false)
    },
  }
}
</script>
<style lang="less">
.custom-drawer {
  .ant-drawer-body {
    padding-top: 0px;
    padding-bottom: 60px;
  }
  .ant-drawer-close {
    top: 8px;
    right: 10px;
  }
}
</style>
