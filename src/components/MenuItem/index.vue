<template>
  <ul class="menu-tree-left">
    <template v-for="item in list">
      <li
        class="menu-tree-item"
        :style="{
          paddingLeft: `${index * 10}px`
        }"
        :class="{ active: upCurrentLocation === item.location }"
        :key="item.location"
        @click="clickLeftMenu(item)"
      >
        {{ item.title }}
      </li>
      <template
        v-if="item.children && item.children.length > 0 && maxTier > index + 1"
      >
        <MenuItem
          :list="item.children"
          :index="index + 1"
          :maxTier="maxTier"
          :key="item.location"
          @clickLeftMenu="clickLeftMenu"
        />
      </template>
    </template>
  </ul>
</template>

<script>
export default {
  name: 'MenuItem',
  props: ['list', 'index', 'upCurrentLocation', 'maxTier'],
  methods: {
    clickLeftMenu(item) {
      this.$emit('clickLeftMenu', item)
    }
  }
}
</script>

<style lang="less" scoped>
.menu-tree-left {
  overflow-y: auto;
  .menu-tree-item {
    width: 100px;
    background: #ffff;
    padding-left: 25px;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
      sans-serif;
    color: rgba(0, 0, 0, 0.65);
    line-height: 40px;
    position: relative;
    white-space: nowrap;
    overflow: hidden;
  }
  .active {
    background: #f9fafc;
    &::before {
      content: '';
      display: inline-block;
      position: absolute;
      top: 0;
      bottom: 0;
      width: 3px;
      background: #52bf63;
      left: 0;
    }
  }
}
</style>
