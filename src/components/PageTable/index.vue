<template>
  <div class="pageTable-wrap">
    <Table
      :data="tableData1"
      :columns="columns"
      row-key="id"
      :load-data="handleLoadData"
      border
    ></Table>
    <div style="margin: 10px;overflow: hidden" v-if="originList.length > 40">
      <div style="float: right;">
        <Page
          :total="total"
          :current="pageNo"
          :page-size="40"
          @on-change="changePage"
        ></Page>
      </div>
    </div>
  </div>
</template>
<script>
import api from '@/api'
import { Page, Table } from 'view-design'
export default {
  props: ['list', 'columns', 'apiGroup'],
  components: {
    Page,
    Table
  },
  data() {
    return {
      tableData1: [],
      originList: [],
      loadDataMap: {},
      pageNo: 1,
      total: 1
    }
  },
  mounted() {
    this.init()
  },
  watch: {
    list(newValue) {
      if (newValue) {
        this.init()
      }
    }
  },
  methods: {
    init() {
      if (this.list.length === 1 && this.list[0].ref) {
        this.handleLoadDataInit(this.list[0])
        return
      }
      this.originList = this.handleTableData(this.list)
      this.total = this.originList.length
      this.tableData1 = this.originList.slice(0, 40)
    },
    handleTableData(list, ref = 1) {
      if (!list) return []
      return this.addId(list, ref)
    },
    addId(list, upIndex) {
      list.forEach((item, index) => {
        item.id = `${index}-sub-${upIndex}`
        if (item.ref) {
          item._loading = false
        }
        if (!item.name) {
          item.name = '-'
        }
        if (item.children && item.children.length > 0) {
          this.addId(item.children, `${index}-sub-${upIndex + 1}`)
        }
      })
      return list
    },
    handleLoadData(item, callback) {
      const ref = item.ref
      if (this.loadDataMap[item.name]) {
        this.$message.info(
          this.$t('tableParamsTip', {
            name: item.name,
            level: this.loadDataMap[item.name]
          })
        )
        callback([])
        return
      } else {
        const list = item.id.split('-')
        this.loadDataMap[item.name] = list[list.length - 1]
      }
      api
        .getModels({
          apiGroupCode: this.apiGroup,
          modelName: ref
        })
        .then(res => {
          callback(this.handleTableData(res, item.id))
        })
    },
    handleLoadDataInit(item) {
      const ref = item.ref
      api
        .getModels({
          apiGroupCode: this.apiGroup,
          modelName: ref
        })
        .then(res => {
          this.originList = this.handleTableData(res)
          this.total = this.originList.length
          this.tableData1 = this.originList.slice(0, 40)
        })
    },
    changePage(page) {
      this.pageNo = page
      this.tableData1 = this.originList.slice(
        (page - 1) * 40,
        (page - 1) * 40 + 40
      )
    }
  }
}
</script>
<style lang="less" scoped>
// .pageTable-wrap {
// 	max-width: 997px;
// }
</style>
