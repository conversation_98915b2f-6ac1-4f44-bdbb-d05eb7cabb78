<template>
  <div class="toggle-wrap">
    <div class="close-btn" @click="handleBtn">
      {{ title }}
      <a-icon
        type="down"
        style="color: #52BF63; fontSize: 11px "
        v-if="!close"
      />
      <a-icon type="up" style="color: #52BF63; fontSize: 11px " v-else />
    </div>
    <div class="content" v-if="close">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: ['title', 'flag'],
  data() {
    return {
      close: this.flag
    }
  },
  methods: {
    handleBtn() {
      this.close = !this.close
    }
  }
}
</script>
<style lang="less" scoped>
.toggle-wrap {
  padding-top: 16px;
  .close-btn {
    height: 20px;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
      sans-serif;
    font-weight: 500;
    color: #52bf63;
    line-height: 20px;
    cursor: pointer;
    margin-bottom: 12px;
  }
}
</style>
