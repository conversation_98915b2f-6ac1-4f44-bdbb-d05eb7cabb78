<template>
  <div class="search-item-wrap">
    <a :href="detail.pageUri" style="display: block" target="_blank">
      <div class="title" v-html="handleHtml(detail.pageTitle, '#23AC38')"></div>
      <div
        class="content"
        :style="lineClamp === 0 ? {} : contentStyle"
        v-html="handleHtml(detail.content, '#52BF63')"
      ></div>
    </a>
    <div class="footer">
      <div
        class="label"
        @click.stop="clickLabel"
        v-if="detail.docTitle"
        :class="[`label-color${getColor()}`]"
      >
        {{ detail.docTitle }}
      </div>
      <div class="date">{{ $t('update') }}{{ detail.lastModifiedDate }}</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    detail: {
      type: Object,
      default: () => {
        return {}
      }
    },
    words: {
      type: Array,
      default: () => []
    },
    lineClamp: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      contentStyle: {
        ' text-overflow': '-o-ellipsis-lastline',
        overflow: 'hidden',
        'text-overflow': 'ellipsis',
        display: '-webkit-box',
        '-webkit-box-orient': 'vertical',
        '-webkit-line-clamp': this.lineClamp,
        'line-clamp': this.lineClamp
      }
    }
  },
  methods: {
    clickLabel() {},
    getColor() {
      return Math.ceil(Math.random() * 4)
    },
    handleHtml(html, color) {
      const reg = new RegExp(this.words.join('|'), 'ig')
      return html.replace(reg, word => {
        return `<span style="color:${color}">${word}</span>`
      })
    }
  }
}
</script>
<style lang="less" scoped>
.search-item-wrap {
  .title {
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    line-height: 20px;
    margin-bottom: 10px;
    word-break: break-all;
  }
  .content {
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
    line-height: 25px;
    margin-bottom: 8px;
  }
  .footer {
    height: 20px;
    .label {
      padding: 0 12px;
      height: 20px;
      border-radius: 2px;
      font-size: 12px;
      font-weight: 400;
      line-height: 20px;
      margin-right: 16px;
      display: inline-block;
      vertical-align: middle;
    }
    .label-color1 {
      background: rgba(97, 175, 254, 0.1);
      color: #61affe;
    }
    .label-color2 {
      background: rgba(253, 187, 23, 0.1);
      color: #fdbb17;
    }
    .label-color3 {
      background: rgba(82, 191, 99, 0.1);
      color: #52bf63;
    }
    .label-color4 {
      background: rgba(244, 98, 102, 0.1);
      color: #f46266;
    }
    .date {
      height: 17px;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #888888;
      line-height: 17px;
      display: inline-block;
      vertical-align: middle;
    }
  }
}
</style>
