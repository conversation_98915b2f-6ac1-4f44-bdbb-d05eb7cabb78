<template>
  <div class="Industry-wrap">
    <h2 style="margin-bottom: 40px">适用场景</h2>
    <ul class="img-list">
      <li
        class="img-item"
        v-for="(item, index) in sceneList"
        :class="{ 'img-item-active': currentActive === index }"
        :key="index"
        :style="{
          background: `${item.sceneImageOpen} no-repeat center`,
          width:
            currentActive === index ? getWidth.itemWidth : getWidth.closeWidth
        }"
      >
        <div class="open-text" v-if="currentActive === index">
          <div class="text-box">
            <p>
              {{ item.closeText
              }}<span
                style="display: block; line-height: 8px;margin-bottom: 12px;"
                >-</span
              >
            </p>
            <p v-for="(a, b) in item.openText" :key="b">{{ a }}</p>
            <a
              v-if="item.product"
              class="product-style"
              :href="item.productUrl"
              :target="item.target"
              >{{ item.product }}</a
            >
          </div>
        </div>
        <div
          class="item-mask"
          :style="{
            background: `${item.sceneImageClose} no-repeat center`
          }"
          @mouseover="mouseover(index)"
        >
          <div class="close-text" :style="{ width: getWidth.closeWidth }">
            {{ item.closeText
            }}<span style="display: block;color: rgba(255, 255, 255, 0.4);"
              >-</span
            >
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: ['sceneList'],
  data() {
    return {
      currentActive: 0,
      timer: null,
      note: {
        background: '',
        backgroundSize: '100% 100%'
      },
      list: [],
      widthMap: {
        2: {
          itemWidth: '1040px',
          closeWidth: '240px'
        },
        3: {
          itemWidth: '960px',
          closeWidth: '160px'
        },
        4: {
          itemWidth: '800px',
          closeWidth: '160px'
        },
        5: {
          itemWidth: '800px',
          closeWidth: '120px'
        }
      }
    }
  },
  computed: {
    getWidth() {
      return this.widthMap[this.sceneList.length]
    }
  },
  methods: {
    mouseover(index) {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      this.timer = setTimeout(() => {
        this.currentActive = index
        this.timer = null
      }, 400)
    }
  }
}
</script>

<style lang="less" scoped>
.Industry-wrap {
  text-align: center;
  .close-text {
    padding: 0 10px;
  }
  .img-list {
    display: inline-block;
    position: relative;
    height: 400px;
    .img-item {
      float: left;
      width: 120px;
      position: relative;
      overflow: hidden;
      height: 400px;
      cursor: pointer;
      background-size: 100% 100%;
      transition: width 0.4s cubic-bezier(0.77, 0, 0.175, 1);
      .item-mask {
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
        background-size: 100% 100%;
      }
      .close-text {
        position: absolute;
        bottom: 50px;
        z-index: 2;
        color: #fff;
        font-weight: 500;
        font-size: 17px;
        width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: center;
        margin: 0 auto;
      }
    }
    .img-item-active {
      width: 800px;
      background-size: 100% 100%;
      height: 400px;
      position: relative;
      .item-mask {
        display: none;
      }
      .open-text {
        width: 520px;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 0px 10px 10px 0px;
        position: absolute;
        left: 0;
        bottom: 32px;
        padding: 32px 24px;
        .text-box {
          text-align: left;
          p {
            color: rgba(255, 255, 255, 0.65);
            margin-bottom: 6px;
          }
          p:first-child {
            color: #fff;
            font-size: 20px;
            font-weight: 500;
            span {
              color: rgba(255, 255, 255, 0.4);
            }
          }
          .product-style {
            color: #fff;
            font-size: 16px;
            margin-top: 14px;
            font-weight: 500;
          }
        }
      }
    }
  }
}
</style>
