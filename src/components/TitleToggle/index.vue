<template>
  <div class="toggle-wrap">
    <div class="close-wrap">
      <div @click="handleBtn" class="left-wrap">
        <div class="close-icon">
          <a-icon type="caret-right" v-if="!close" />
          <a-icon type="caret-down" v-else />
        </div>
        <h3 :id="anchor" class="title">{{ title }}</h3>
      </div>
      <Tooltip
        v-if="tip"
        transfer
        max-width="370"
        :content="tip"
        placement="top"
      >
        <a-icon type="question-circle" style="cursor: pointer;" />
      </Tooltip>
    </div>
    <div class="content" v-if="close">
      <slot></slot>
    </div>
  </div>
</template>

<script>
import { Tooltip } from 'view-design'
export default {
  props: ['title', 'flag', 'tip', 'anchor'],
  components: {
    Tooltip
  },
  data() {
    return {
      close: this.flag
    }
  },
  methods: {
    handleBtn() {
      this.close = !this.close
    }
  }
}
</script>
<style lang="less" scoped>
.toggle-wrap {
  margin-top: 24px;
  .close-btn {
    height: 20px;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
      sans-serif;
    font-weight: 500;
    color: #52bf63;
    line-height: 20px;
    cursor: pointer;
    margin-bottom: 12px;
  }
  .close-wrap {
    margin-bottom: 12px;
    position: relative;
    .left-wrap {
      display: inline-block;
      cursor: pointer;
      margin-right: 10px;
    }
    .close-icon {
      position: absolute;
      left: -14px;
      top: 4px;
    }
    .title {
      margin: 0;
    }
  }
}
</style>
