import api from '@/api'
import { mapState } from 'vuex'
import Item from './item.vue'
export default {
  props: ['title', 'list', 'apiGroup', 'type'],
  components: {
    Item
  },
  data() {
    return {
      newList: []
    }
  },
  computed: {
    ...mapState('apiDocs', [
      'definition',
    ]),
  },
  watch: {
    list: {
      handler() {
        this.init()
      },
      immediate: true
    }
  },
  methods: {
    init() {
      if(!this.list) return
      if (this.list.length === 1 && this.list[0].ref) {
        this.handleLoadDataInit(this.list[0])
      }
      this.newList = this.list
    },
    handleLoadDataInit(item) {
      const ref = item.ref
      api
        .getModels({
          apiGroupCode: this.apiGroup,
          modelName: ref
        })
        .then(res => {
          this.newList = res
        })
    },
  }
}