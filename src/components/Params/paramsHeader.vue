<template>
  <div class="paramsHeader-wrap">
    <div class="header">
      <div class="title">{{ title }}</div>
      <div class="desc">若使用SDK对接无需关注。</div>
      <div class="operate" @click="handle">{{ !show ? $t('展开') : $t('收起') }}</div>
    </div>
    <div class="paramsList" :style="{ display: show ? 'block' : 'none' }">
      <Item  v-for="item in newList" :data="item" :key="item.name" :apiGroup="apiGroup" />
    </div>
  </div>
</template>
<script>
import Mixin from './mixin'
export default {
  mixins: [Mixin],
  data() {
    return {
      show: false
    }
  },
  methods: {
    handle() {
      this.show = !this.show
    }
  }
}
</script>
<style lang="less" scoped>
.paramsHeader-wrap {
  font-family: PingFangSC, PingFang SC;
  margin-bottom: 1px;
  .header {
    height: 56px;
    background: #FFFFFF;
    box-shadow: 0px 1px 0px 0px rgba(0,0,0,0.09);
    display: flex;
    align-items: center;
    .title {
      height: 24px;
      font-weight: 500;
      font-size: 18px;
      color: rgba(0,0,0,0.65);
      line-height: 24px;
      margin-right: 8px;
    }
    .desc {
      height: 20px;
      font-weight: 400;
      font-size: 14px;
      color: rgba(0,0,0,0.45);
      line-height: 20px;
      flex: 1;
    }
    .operate {
      height: 20px;
      font-weight: 400;
      font-size: 14px;
      color: @primary-color;
      line-height: 20px;
      cursor: pointer;
    }
  }
}
</style>