<template>
  <div class="paramsBody-wrap">
    <div class="header">
      <div class="title">{{ title }}</div>
      <div class="desc">{{ type }}</div>
    </div>
    <template v-if="requiredList.length > 0">
      <Item  v-for="item in requiredList" :data="item" :key="item.name" :apiGroup="apiGroup" />
    </template>
    <template v-if="noRequiredList.length > 0">
      <div class="header more-header">
        <div class="title" style="flex: 1">{{ $t('更多参数') }}</div>
        <div class="operate" @click="handle">{{!showAll ? $t('展开全部') : $t('收起全部')}}</div>
      </div>
      <ul>
        <li class="fold-item" v-for="item in noRequiredList" :key="item.name">
          <div class="icon" @click="toggleShowMore(item)">
            <span v-if="!item.noContent" class="icon iconfont" :class="!item.showMore ? 'icon-caidan_zhankai' : 'icon-caidan_shouqi'" style="font-size: 12px"></span>
          </div>
          <div class="content">
            <Item :data="item" :showMore="item.showMore" :apiGroup="apiGroup"  :isMore="true" @onClick="toggleShowMore(item)" />
          </div>
        </li>
      </ul>
    </template>
  </div>
</template>
<script>
import Mixin from './mixin'
export default {
  mixins: [Mixin],
  data() {
    return {
    }
  },
  computed: {
    requiredList() {
      if (this.newList.filter(l => l.required || l.conditionRequired).length < 1) {
        return this.newList
      }
      return [
        ...this.newList.filter(l => l.required),
        ...this.newList.filter(l => l.conditionRequired)
      ]
    },
    noRequiredList() {
      if (this.newList.filter(l => l.required || l.conditionRequired).length < 1) {
        return []
      }
      return this.newList.filter(l => !l.required && !l.conditionRequired).map(l => {
        this.$set(l, 'showMore', false)
        return l
      })
    },
    showAll() {
      return  !this.noRequiredList.some(i => !i.showMore)
    }
  },
  methods: {
    handle() {
      const result =  !this.showAll
      this.noRequiredList.forEach(i => {
        i.showMore = result
      })
    },
    toggleShowMore(item) {
      item.showMore = !item.showMore
    }
  }
}
</script>
<style lang="less" scoped>
.paramsBody-wrap {
  font-family: PingFangSC, PingFang SC;
  .header {
    height: 40px;
    background: #FFFFFF;
    display: flex;
    align-items: flex-end;
    .title {
      height: 24px;
      font-weight: 500;
      font-size: 18px;
      color: rgba(0,0,0,0.65);
      line-height: 24px;
      margin-right: 8px;
    }
    .desc {
      height: 20px;
      font-weight: 400;
      font-size: 14px;
      color: rgba(0,0,0,0.45);
      line-height: 20px;
      flex: 1;
    }
    .operate {
      height: 20px;
      font-weight: 400;
      font-size: 14px;
      color: @primary-color;
      line-height: 20px;
      cursor: pointer;
    }
  }
  .more-header {
    margin-top: 8px;
    margin-bottom: 4px;
  }
  .fold-item {
    display: flex;
    justify-items: flex-start;
    cursor: pointer;
    .icon {
      width: 16px;
      height: 16px;
      line-height: 16px;
      text-align: center;
      font-size: 13px;
      margin-right: 8px;
      margin-top: 18.5px;
      color: rgba(0,0,0,0.45);
    }
    .content {
      flex: 1;
    }
  }
}
</style>