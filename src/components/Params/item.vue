<template>
	<div class="params-item">
		<div class="params-header" :class="!isMore ? 'params-header-card' : ''" @click="onHeaderClick">
			<div class="name">
        {{ data.name }}
        <div class="split" v-if="data.title"></div>
        <span class="title">{{ data.title }}</span>
      </div>
			<div class="type">
				{{ data.type }}<span v-if="show(data.maxLength)">({{ data.maxLength }})</span>
			</div>
			<div class="required" v-if="data.required">必填</div>
			<div class="required" v-if="data.conditionRequired">条件必填</div>
			<div class="encipher" v-if="data.encrypt">推荐加密</div>
		</div>
		<div :class="showMore ? 'params-item-show' : 'params-item-hidden'">
			<div class="params-content" ref="desc" v-if="desc" v-html="desc"></div>
      <div class="conditionRequired" v-if="data.conditionDesc">【必填条件】{{ data.conditionDesc }}
      </div>
			<ul class="enum-wrap" v-if="enums.length > 0">
				<li class="enum-title">枚举值</li>
				<li class="enum-item" v-for="item in enums" :key="item.label">
					<span class="tag">{{ item.label }}</span
					><span style="margin: 0 3px">:</span><span v-html="item.value"></span>
				</li>
			</ul>
			<div class="params-exam" v-if="show(data.example)">示例值：{{ data.example }}</div>
      <template v-if="data.ref || data.children.length > 0">
        <div
          class="children-obj"
          style="padding-left: 13px"
          :style="{ 'display': !childrenShow ? 'flex' : 'none' }"
          @click="showChildren"
          key="show"
        >
          <span class="icon iconfont icon-zhankai"></span>
          显示子属性
        </div>
        <div class="children-obj-show"
          :style="{ 'display': childrenShow && data.children.length > 0 ? 'block' : 'none' }"
        >
          <div class="header" key="hidden" @click="hiddenChildren">
            <span class="icon iconfont icon-shouqi"></span>
            隐藏子属性
          </div>
          <Item
            v-for="item in data.children"
            :data="item"
            :key="`${item.name}${index}`"
            :index="index + 1"
            :loadDataMap="loadDataMap"
            :apiGroup="apiGroup"
          />
        </div>
      </template>
    </div>
  </div>
</template>
<script>
import api from '@/api'
import { formateA } from '@/utils'
export default {
  name: 'Item',
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    loadDataMap: {
      type: Object,
      default: () => ({})
    },
    showMore: {
      type: Boolean,
      default: true
    },
    isMore: {
      type: Boolean,
      default: false
    },
    index: {
      type: Number,
      default: 1
    },
    apiGroup: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      loadData: false,
      childrenShow: false
    }
  },
  computed: {
    desc() {
      const { description = '' } = this.data
			const descArr = description.split(/可选项如下:<br.*?>|可选项如下:\n/)
			if (descArr[0]) {
				return descArr[0]
      }
      return ''
		},
		enums() {
			const { description = '' } = this.data
			const descArr = description.split(/可选项如下:<br.*?>|可选项如下:\n/)
      if (descArr.length < 2) return []
      return descArr[1].split(/<br.*?>|\n/).filter(d => d).map((d) => {
				const dArr = d.split(':')
				return {
					label: dArr[0],
					value: dArr[1],
				}
			})
		},
	},
	watch: {
		showMore() {
			this.fixA()
		},
	},
	mounted() {
    this.fixA()
    this.hasContent()
	},
  methods: {
    hasContent() {
      const res = !this.desc
        && this.data.conditionDesc === undefined
        && this.enums.length < 1
        && !this.show(this.data.example)
        && !(this.data.ref || this.data.children.length > 0)
      if (res) {
        this.$set(this.data, 'noContent', true)
      } else {
        if (this.data.ref) {
          this.handleLoadData(false, false)
        }
      }
    },
		show(val) {
			return val && val !== '-'
    },
    showChildren() {
			if (this.data.children.length < 1 && !this.loadData) {
				this.handleLoadData(true)
			} else {
				this.childrenShow = true
			}
		},
		hiddenChildren() {
			this.childrenShow = false
		},
		handleLoadData(show, showInfo = true) {
			const ref = this.data.ref
			const name = this.data.name
      if (this.loadDataMap[name] && this.loadDataMap[name] !== this.index) {
        if (showInfo) { 
          this.$message.info(
            this.$t('tableParamsTip', {
              name: name,
              level: this.loadDataMap[name],
            })
          )
        }
				return
			} else {
				this.loadDataMap[name] = this.index
			}
			this.loadData = true
			api
				.getModels({
					apiGroupCode: this.apiGroup,
					modelName: ref,
				})
				.then((res) => {
					this.$set(this.data, 'children', res)
					this.childrenShow = show
				})
				.finally(() => {
					this.loadData = false
				})
		},
		onHeaderClick() {
			this.$emit('onClick')
		},
		fixA() {
			this.$nextTick(() => {
				if (!this.$refs.desc) return
				formateA(this.$refs.desc)
			})
		},
	},
}
</script>
<style scoped lang="less">
.params-item {
	font-family: PingFangSC, PingFang SC;
  border-bottom: 1px solid rgba(0, 0, 0, 0.09);
	padding: 16px 0;
	.params-header {
		display: flex;
		align-items: center;
		line-height: 30px;
		.name {
			font-weight: 700;
			font-size: 15px;
			color: rgba(0, 0, 0, 0.85);
			margin-right: 8px;
			line-height: 16px;
			font-family: -apple-system, "system-ui", "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif,
				"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
      display: flex;
      align-items: center;
      .split {
        width: 1px;
        height: 14px;
        background: rgba(0,0,0,0.65);
        margin: 0 8px;
      }
      .title {
        font-size: 14px;
      }
    }
    .title {
      font-weight: 400;
    }
		.type {
			height: 20px;
			font-weight: 400;
			font-size: 14px;
			color: rgba(0, 0, 0, 0.45);
			line-height: 20px;
			margin-right: 8px;
		}
		.required {
			font-weight: 400;
			font-size: 12px;
			color: #ff4d4f;
			text-align: center;
			margin-right: 8px;
			margin-top: 1px;
		}
		.encipher {
			width: 48px;
			height: 18px;
			font-weight: 400;
			font-size: 12px;
			color: rgba(0, 0, 0, 0.45);
			line-height: 18px;
		}
	}
	.params-header-card {
		margin-bottom: 8px;
		.name {
			background: rgba(0, 0, 0, 0.04);
			border-radius: 4px;
			padding: 6px 8px;
		}
	}
	.params-content {
		font-weight: 400;
		font-size: 14px;
		color: rgba(0, 0, 0, 0.85);
		line-height: 24px;
		word-break: break-all;
		/deep/ pre {
			margin-bottom: 0;
			white-space: pre-wrap;
			word-break: break-all;
		}
	}
  .conditionRequired {
    font-weight: 400;
    font-size: 14px;
    color: rgba(0,0,0,0.85);
    line-height: 24px;
    margin:4px 0 12px;
    margin-left: -9px;
    .label {
      width: 60px;
      height: 22px;
      background: rgba(255, 77, 79, 0.12);
      border-radius: 4px;
      font-weight: 400;
      font-size: 12px;
      color: #FF4D4F;
      line-height: 22px;
      text-align: center;
      margin-right: 8px;
      display: inline-block;
    }
  }
	.enum-wrap {
		background: #ffffff;
		border-radius: 8px;
		border: 1px solid rgba(0, 0, 0, 0.08);
		margin-top: 8px;
		margin-bottom: 8px;
		.enum-title {
			height: 36px;
			border-bottom: 1px solid #e2e2e2;
			padding-left: 17px;
			line-height: 36px;
			font-weight: 500;
			font-size: 14px;
			color: rgba(0, 0, 0, 0.65);
			margin-bottom: 12px;
		}
		.enum-item {
			margin-bottom: 6px;
			padding: 0 14px 0 28px;
			position: relative;
			word-break: break-all;
			font-size: 13px;
			&::before {
				content: "";
				display: block;
				width: 4px;
				height: 4px;
				border-radius: 50%;
				background: rgba(0, 0, 0, 0.32);
				position: absolute;
				top: 10px;
				left: 16px;
			}
			&:last-child {
				margin-bottom: 12px;
			}
			.tag {
				background: rgba(0, 0, 0, 0.03);
				border-radius: 4px;
				border: 1px solid #e0e0e0;
				padding: 0 6px;
				font-weight: 400;
				font-size: 12px;
				color: rgba(0, 0, 0, 0.65);
				margin-bottom: 4px;
				display: inline-block;
			}
		}
	}
	.params-exam {
		font-weight: 400;
		font-size: 14px;
		color: #8c8c8c;
		line-height: 24px;
		word-break: break-all;
	}
	.children-obj {
		margin-top: 16px;
		width: 116px;
		height: 36px;
		background: #ffffff;
		border-radius: 20px;
		border: 1px solid #e2e2e2;
		cursor: pointer;
		display: flex;
		align-items: center;
		font-weight: 500;
		font-size: 14px;
		color: rgba(0, 0, 0, 0.65);
		cursor: pointer;
		.icon {
			font-size: 12px;
			margin-right: 4px;
		}
	}
	.children-obj-show {
		background: #ffffff;
		border-radius: 8px;
		border: 1px solid rgba(0, 0, 0, 0.08);
		cursor: pointer;
		margin-top: 16px;
		.header {
			height: 36px;
			display: flex;
			align-items: center;
			padding-left: 13px;
			font-weight: 500;
			font-size: 14px;
			color: rgba(0, 0, 0, 0.65);
			border-bottom: 1px solid #e2e2e2;
			.icon {
				font-size: 12px;
				margin-right: 4px;
			}
		}
		.params-item {
			padding-left: 16px;
			padding-right: 16px;
			&:last-child {
				box-shadow: none;
			}
		}
	}
  .params-item-show {
    display: block;
  }
  .params-item-hidden {
    display: none;
  }
}
</style>
