<template>
  <div class="api-tech-tips" v-if="visibleTips.length > 0">
    <div class="tips-header" v-if="showHeader">
      <h4 class="tips-title">
        <span class="tips-icon">⚠️</span>
        {{ $t("techTips.title") }}
      </h4>
      <div class="tips-toggle" @click="toggleCollapse">
        <span class="toggle-icon">{{ collapsed ? "▶" : "▼" }}</span>
      </div>
    </div>

    <div class="tips-content" v-show="!collapsed">
      <a-alert
        v-for="tip in visibleTips"
        :key="tip.type"
        :type="tip.alertType"
        :message="tip.title"
        :description="tip.description"
        :show-icon="true"
        :closable="tip.closable"
        class="tip-item"
        :class="`tip-${tip.type}`"
        @close="onTipClose(tip.type)"
      >
        <template v-if="tip.actions && tip.actions.length > 0" #action>
          <div class="tip-actions">
            <a
              v-for="action in tip.actions"
              :key="action.key"
              :href="action.href"
              :target="action.target || '_blank'"
              class="tip-action-link"
            >
              {{ action.text }}
            </a>
          </div>
        </template>
      </a-alert>
    </div>
  </div>
</template>

<script>
export default {
  name: "ApiTechTips",
  props: {
    // API定义对象
    apiDefinition: {
      type: Object,
      default: () => ({}),
    },
    // 是否显示标题头部
    showHeader: {
      type: Boolean,
      default: true,
    },
    // 默认是否折叠
    defaultCollapsed: {
      type: Boolean,
      default: false,
    },
    // 自定义显示的提示类型
    customTips: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      collapsed: this.defaultCollapsed,
      closedTips: JSON.parse(localStorage.getItem("closedApiTips") || "[]"),
    };
  },
  computed: {
    // 所有可能的技术提示
    allTips() {
      const tips = [];
      const { method, options = [], securityReqs = [] } = this.apiDefinition;

      // 1. 非幂等接口异常处理提示
      if (this.isNonIdempotent()) {
        tips.push({
          type: "non-idempotent",
          alertType: "error",
          title: this.$t("techTips.nonIdempotent.title"),
          description: this.$t("techTips.nonIdempotent.description"),
          closable: true,
          priority: 1,
        });
      }

      // 2. 重试机制建议
      tips.push({
        type: "retry-mechanism",
        alertType: "warning",
        title: this.$t("techTips.retryMechanism.title"),
        description: this.$t("techTips.retryMechanism.description"),
        closable: true,
        priority: 2,
      });

      // 3. 超时处理
      tips.push({
        type: "timeout-handling",
        alertType: "info",
        title: this.$t("techTips.timeoutHandling.title"),
        description: this.$t("techTips.timeoutHandling.description"),
        closable: true,
        priority: 3,
      });

      // 4. 并发控制（针对敏感操作）
      if (this.isSensitiveOperation()) {
        tips.push({
          type: "concurrency-control",
          alertType: "error",
          title: this.$t("techTips.concurrencyControl.title"),
          description: this.$t("techTips.concurrencyControl.description"),
          closable: true,
          priority: 1,
        });
      }

      // 5. 签名安全（需要签名认证的接口）
      if (securityReqs.length > 0) {
        tips.push({
          type: "signature-security",
          alertType: "warning",
          title: this.$t("techTips.signatureSecurity.title"),
          description: this.$t("techTips.signatureSecurity.description"),
          closable: true,
          priority: 2,
          actions: [
            {
              key: "security-guide",
              text: this.$t("techTips.signatureSecurity.actionText"),
              href: "https://open.yeepay.com/docs/platform/security/signature",
              target: "_blank",
            },
          ],
        });
      }

      // 6. 参数校验
      tips.push({
        type: "parameter-validation",
        alertType: "info",
        title: this.$t("techTips.parameterValidation.title"),
        description: this.$t("techTips.parameterValidation.description"),
        closable: true,
        priority: 4,
      });

      // 7. 限流频控
      tips.push({
        type: "rate-limiting",
        alertType: "info",
        title: this.$t("techTips.rateLimiting.title"),
        description: this.$t("techTips.rateLimiting.description"),
        closable: true,
        priority: 5,
      });

      // 添加自定义提示
      if (this.customTips.length > 0) {
        tips.push(...this.customTips);
      }

      // 按优先级排序
      return tips.sort((a, b) => (a.priority || 999) - (b.priority || 999));
    },

    // 过滤掉已关闭的提示
    visibleTips() {
      return this.allTips.filter((tip) => !this.closedTips.includes(tip.type));
    },
  },
  methods: {
    // 判断是否为非幂等接口
    isNonIdempotent() {
      const { method, options = [] } = this.apiDefinition;
      // POST方法且没有IDEMPOTENT标记的接口认为是非幂等的
      return (
        method &&
        method.toUpperCase().includes("POST") &&
        !options.includes("IDEMPOTENT")
      );
    },

    // 判断是否为敏感操作（涉及资金、订单等）
    isSensitiveOperation() {
      const { path = "", title = "", desc = "" } = this.apiDefinition;
      const sensitiveKeywords = [
        "pay",
        "order",
        "money",
        "fund",
        "transfer",
        "withdraw",
        "支付",
        "订单",
        "资金",
        "转账",
        "提现",
      ];
      const content = `${path} ${title} ${desc}`.toLowerCase();
      return sensitiveKeywords.some((keyword) =>
        content.includes(keyword.toLowerCase())
      );
    },

    // 切换折叠状态
    toggleCollapse() {
      this.collapsed = !this.collapsed;
    },

    // 关闭提示
    onTipClose(tipType) {
      if (!this.closedTips.includes(tipType)) {
        this.closedTips.push(tipType);
        localStorage.setItem("closedApiTips", JSON.stringify(this.closedTips));
      }
    },
  },
};
</script>

<style lang="less" scoped>
.api-tech-tips {
  margin-bottom: 16px;

  .tips-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px 4px 0 0;

    .tips-title {
      margin: 0;
      font-size: 14px;
      font-weight: 500;
      color: #495057;
      display: flex;
      align-items: center;

      .tips-icon {
        margin-right: 6px;
        font-size: 14px;
      }
    }

    .tips-toggle {
      cursor: pointer;
      padding: 4px;
      border-radius: 2px;
      transition: background-color 0.2s;

      &:hover {
        background-color: #e9ecef;
      }

      .toggle-icon {
        font-size: 12px;
        color: #6c757d;
      }
    }
  }

  .tips-content {
    border: 1px solid #e9ecef;
    border-top: none;
    border-radius: 0 0 4px 4px;
    padding: 12px;
    background: #fff;

    .tip-item {
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      &.tip-non-idempotent,
      &.tip-concurrency-control {
        border-color: #dc3545;
      }

      &.tip-retry-mechanism,
      &.tip-signature-security {
        border-color: #ffc107;
      }

      &.tip-timeout-handling,
      &.tip-parameter-validation,
      &.tip-rate-limiting {
        border-color: #17a2b8;
      }
    }

    .tip-actions {
      margin-top: 8px;

      .tip-action-link {
        color: #007bff;
        text-decoration: none;
        font-size: 12px;
        margin-right: 12px;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

// 当没有标题头部时的样式
.api-tech-tips:not(.has-header) {
  .tips-content {
    border-radius: 4px;
    border-top: 1px solid #e9ecef;
  }
}
</style>
