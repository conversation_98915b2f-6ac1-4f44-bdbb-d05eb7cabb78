<template>
	<div class="faq-item-wrap">
		<div class="title" :id="`${item.title}faq`">
			<div class="question">Q：{{ item.title }}</div>
			<div class="help-wrap">
				<div class="help-left" :class="{ active: help }" @click="helpClick">
					<Tooltip transfer content="有帮助" placement="top">
						<svg
							t="1640936113452"
							class="icon"
							viewBox="0 0 1024 1024"
							version="1.1"
							xmlns="http://www.w3.org/2000/svg"
							p-id="7194"
							width="16"
							height="16"
							xmlns:xlink="http://www.w3.org/1999/xlink"
						>
							<path
								d="M64 483.072v388.928c0 37.248 30.144 67.392 67.392 67.392H192V416.32l-60.608-0.64C94.08 415.68 64 445.824 64 483.072zM857.28 344.96l-267.776 1.664c12.544-44.224 18.944-83.584 18.944-118.208 0-78.592-68.864-155.52-137.6-145.472-60.608 8.768-67.264 61.184-67.264 126.784v59.264c0 76.096-63.808 140.864-137.856 148.032L256 416.896v522.432h527.552c49.344 0 91.712-35.136 100.928-83.648l73.728-388.928a102.72 102.72 0 0 0-100.928-121.792z"
								p-id="7195"
							/>
						</svg>
					</Tooltip>
				</div>
				<div class="help-right" :class="{ active: bad }" @click="badClick">
					<Tooltip transfer content="没帮助" placement="top">
						<svg
							t="1640936323942"
							class="icon"
							viewBox="0 0 1024 1024"
							version="1.1"
							xmlns="http://www.w3.org/2000/svg"
							p-id="7381"
							width="16"
							height="16"
							xmlns:xlink="http://www.w3.org/1999/xlink"
						>
							<path
								d="M64 540.992V152c0-37.184 30.144-67.328 67.392-67.328H192v523.008l-60.608 0.64A67.328 67.328 0 0 1 64 540.992z m793.28 138.048l-267.776-1.728c12.544 44.288 18.944 83.584 18.944 118.208 0 78.592-68.864 155.52-137.6 145.536-60.608-8.832-67.264-61.184-67.264-126.848v-59.264c0-76.032-63.808-140.864-137.856-147.968L256 607.04V84.672h527.552c49.344 0 91.712 35.072 100.928 83.584l73.728 388.928a102.72 102.72 0 0 1-100.928 121.856z"
								p-id="7382"
							/>
						</svg>
					</Tooltip>
				</div>
			</div>
		</div>
		<div class="answer-wrap">
			<div class="answer-inner">
        <div 
          class="answer" 
          ref="answer"
          :style="{
            height: !isShowAll && hasMoreText ? '78px' : 'auto'
          }"
        >
          <RenderMarkdown :html="getHtml(answer)" />
          <a v-if="!isShowAll && hasMoreText" class="show-more" href="javascript:;" @click="showAll">查看全部</a>
        </div>
      </div>
		</div>
	</div>
</template>

<script>
import api from '@/api/faq'
import RenderMarkdown from '@/components/RenderMarkdown'
import { Tooltip } from 'view-design'
export default {
  props: {
    item: {
      type: Object,
      default: () => {
        return {
          title: '',
          answer: ''
        }
      }
    }
  },
	components: {
		Tooltip,
		RenderMarkdown
	},
  data() {
    return {
			help: false,
			bad: false,
      isShowAll: false,
      maxNum: null,
			height: '',
      answer: ''
    }
  },
  computed: {
    hasMoreText() {
      return false
    },
    customUserId() {
      return this.$store.state.customUserId
    }
  },
  mounted() {
    this.answer = this.item.answer || ''
    this.$nextTick(() => {
      this.height = this.$refs.answer.offsetHeight
    })
  },
  methods: {
		getHtml(answer) {
			return `<div class="markdown-con">${answer}</div>`
		},
    showAll() {
      this.isShowAll = true
    },
		helpClick() {
			if(this.help) return
			this.help = true
      api.feedback({
        target: 'DOC_FAQ',
        type: 'HELP',
        uid: this.customUserId,
        targetId: this.item.id
      }).then(res => {
        if(res.status === 'success') {
          this.$message.success('提交成功')
        }
      })
		},
		badClick() {
			if(this.bad) return
			this.bad = true
      api.feedback({
        target: 'DOC_FAQ',
        type: 'NO_HELP',
        uid: this.customUserId,
        targetId: this.item.id
      }).then(res => {
        if(res.status === 'success') {
					window.open(
						'https://wenjuan.feishu.cn/m?t=sktLiV5l73yi-d8u6',
						'_blank',
						'width=700,height=500,left=300,top=150'
					)
        }
      })
		}
  }

}
</script>

<style lang="less" scoped>
	.faq-item-wrap {
    background: rgba(0,0,0,0.02);
    border-radius: 8px;
		margin-bottom: 16px;
    padding: 15px 16px;
    padding-bottom: 13px;
		/deep/ .vditor-reset {
			line-height: 22px !important;
      p {
        margin-bottom: 0;
        font-weight: 500;
      }
      h5 {
        margin-top: 16px;
      }
		}
		.title {
			position: relative;
      margin-bottom: 4px;
			.label {
				display: inline-block;
				font-size: 14px;
				font-weight: 500;
				color: #52bf63;
				line-height: 22px;
			}
			.question {
				display: inline-block;
				height: 20px;
				font-size: 14px;
				font-weight: 500;
				color: #52bf63;
				line-height: 22px;
        word-break: break-all;
			}
			.help-wrap {
				display: none;
				position: absolute;
				right: 0;
				top: -8px;
				width: 96px;
				height: 32px;
				line-height: 32px;
				background: #ffffff;
				border-radius: 4px;
				border: 1px solid rgba(0, 0, 0, 0.06);
				text-align: center;
				padding-top: 2px;
				.help-left {
					width: 24px;
					height: 24px;
					display: inline-block;
					margin-right: 20px;
					cursor: pointer;
					svg {
						fill: #aeaeae;
					}
					&:hover {
						svg {
							fill: #52bf63;
						}
					}
				}
				.help-right {
					width: 24px;
					height: 24px;
					display: inline-block;
					cursor: pointer;
					svg {
						fill: #aeaeae;
					}
					&:hover {
						svg {
							fill: #52bf63;
						}
					}
				}
				.active {
					svg {
						fill: #52bf63;
					}
				}
			}
		}
		&:hover {
			.title .help-wrap {
				display: block;
			}
		}
		.answer-wrap {
		}
    .answer-inner {
      position: relative;
      &::before {
        content: '';
        width: 0;
        height: 0;
        border-top: 8px solid transparent;
        border-left: 8px solid #fafafa;
        border-bottom: 8px solid transparent;
        position: absolute;
        top: -7px;
        left: 0;
      }
    }
		.answer {
			font-size: 16px;
			font-weight: 400;
			color: rgba(0, 0, 0, 0.65);
			line-height: 26px;
      word-break: break-all;
			overflow: hidden;
			position: relative;
			.show-more {
				position: absolute;
				background: #fafafa;
				line-height: 26px;
				width: 70px;
				padding: 0 4px;
				bottom: 0px;
				right: 0px;
			}
		}
	}
</style>