<template>
  <div class="search-input">
    <div class="input-wrap">
      <div class="input-icon"></div>
      <div class="close-icon" @click="clearValue" v-if="inputValue">
        <a-icon
          theme="filled"
          type="close-circle"
          class="ant-input-clear-icon"
        />
      </div>
      <input
        :placeholder="$t('serach')"
        type="search"
        ref="input"
        @input="onInput"
        @blur="onBlur"
        key="input-search-wrod"
        v-model="inputValue"
        @keyup.enter="() => onCategoryClick('ALL')"
      />
    </div>
    <ul class="sub-search" v-if="isShowSub && needSubMenu">
      <li
        class="sub-search-item"
        v-for="item in categoryList"
        :key="item.code"
        @click="onCategoryClick(item.code)"
      >
        <div class="text">{{ inputValue }}</div>
        <div class="label">
          <div class="label-text">{{ item.name }}</div>
          <a-icon type="enter" style="font-size: 9px; color: #000" />
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: {
    needSubMenu: {
      type: Boolean,
      default: false
    },
    value: {
      type: [String, Number],
      default: null
    },
    searchWord: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      isInput: false,
      inputValue: '',
      isShowSub: false
    }
  },
  watch: {
    searchWord: {
      handler(newValue) {
        if (newValue) {
          this.inputValue = this.searchWord
        }
      },
      immediate: true
    }
  },
  computed: {
    categoryList() {
      return this.$store.state.search.categoryList
    }
  },
  methods: {
    onCategoryClick(code) {
      this.$store.commit('search/setCurrentCode', code)
      this.onSearch()
    },
    onBlur() {
      setTimeout(() => {
        this.isShowSub = false
      }, 500)
    },
    onInput(e) {
      this.isShowSub = !!e.target.value
    },
    clearValue() {
      this.inputValue = ''
    },
    onSearch() {
      if (!this.inputValue) return
      this.isShowSub = false
      this.$store.commit('setMobileLeftShow', false)
      const searchWord = this.inputValue
      if (this.$route.path === '/search') {
        this.$store.commit('search/setSearchWord', searchWord)
        this.$store.commit('search/setPageNo', 1)
        this.$store.dispatch('search/getResult')
        if (this.$route.query.wd === searchWord) return
        this.$router.replace({
          path: '/search',
          query: {
            wd: searchWord
          }
        })
        return
      }
      this.$router.push({
        path: '/search',
        query: {
          wd: searchWord
        }
      })
    },
    onSearchClick() {
      this.isInput = true
      this.$nextTick(() => {
        this.$refs.input.focus()
      })
    }
  }
}
</script>
<style lang="less" scoped>
.search-input {
  height: 52px;
  background: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  position: relative;
  .sub-search {
    position: absolute;
    z-index: 99;
    top: 44px;
    left: 10px;
    right: 10px;
    background-color: #fff;
    padding: 10px 0;
    .sub-search-item {
      width: 100%;
      height: 32px;
      font-size: 14px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
      line-height: 20px;
      padding: 6px 16px;
      white-space: nowrap;
      display: flex;
      align-items: center;
      justify-content: space-between;
      &:hover {
        background: rgba(0, 0, 0, 0.06);
      }
      .text {
        flex: 1;
        margin-right: 20px;
        overflow: hidden;
        white-space: nowrap;
        word-break: break-all;
        text-overflow: ellipsis;
      }
      .label {
        float: right;
        height: 20px;
        background: rgba(0, 0, 0, 0.1);
        border-radius: 10px;
        line-height: 20px;
        padding: 0 12px;
        display: flex;
        align-items: center;
        flex-wrap: nowrap;
        .label-text {
          display: inline-block;
          max-width: 100px;
          overflow: hidden;
          white-space: nowrap;
          word-break: break-all;
          text-overflow: ellipsis;
        }
      }
    }
  }
  .icon {
    display: inline-block;
    vertical-align: middle;
    width: 30px;
    height: 30px;
    background: url('../../assets/images/search-icon.png') no-repeat center;
    background-size: 30px 30px;
    cursor: pointer;
  }
  .input-wrap {
    position: relative;
    width: 100%;
    height: 32px;
    background: #fff;
    border-radius: 2px;
    padding: 0 36px;
    .input-icon {
      position: absolute;
      left: 8px;
      top: 2px;
      width: 30px;
      height: 30px;
      background: url('../../assets/images/search-icon.png') no-repeat center;
      background-size: 30px 30px;
    }
    .close-icon {
      position: absolute;
      right: 16px;
      top: 9px;
      width: 16px;
      height: 16px;
      line-height: 16px;
    }
  }
  input {
    position: relative;
    display: block;
    width: 100%;
    outline: none;
    -webkit-appearance: none; /*去除系统默认的样式*/
    box-shadow: none;
    background: #fff;
    border: none;
    opacity: 1;
    height: 32px;
    padding: 0;
  }
}
</style>
