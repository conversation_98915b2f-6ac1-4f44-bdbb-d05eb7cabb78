<template>
  <div class="search-input">
    <div
      class="icon"
      :class="{ icon2: fixed }"
      v-if="!isInput"
      @click="onSearchClick"
    ></div>
    <div class="input-wrap" v-else>
      <div class="input-icon"></div>
      <div class="close-icon" @click="clearValue" v-if="inputValue">
        <a-icon
          theme="filled"
          type="close-circle"
          class="ant-input-clear-icon"
        />
      </div>
      <input
        type="text"
        ref="input"
        :placeholder="$t('serach')"
        key="input-search-wrod"
        v-model="inputValue"
        @input="onInput"
        @keyup.enter="onSearch"
      />
    </div>
    <ul key="sub-search" class="sub-search" v-show="isShowSub && inputValue">
      <li
        class="sub-search-item"
        v-for="item in categoryList"
        @click="onCategoryClick(item.code)"
        :key="item.code"
      >
        <div class="text">{{ inputValue }}</div>
        <div class="label">
          <div class="label-text">{{ item.name }}</div>
          <a-icon
            type="enter"
            class="enter-icon"
            style="font-size: 9px; color: #000"
          />
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: {
    fixed: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      inputValue: '',
      isInput: false,
      isShowSub: false
    }
  },
  computed: {
    categoryList() {
      return this.$store.state.search.categoryList
    }
  },
  mounted() {
    document.addEventListener('click', e => {
      const path = e.path || (e.composedPath && e.composedPath())
      let classNameString = ''
      ;[].forEach.call(path, element => {
        if (element.className) {
          classNameString += element.className
        }
      })
      if (classNameString.indexOf('search-input') === -1) {
        this.isShowSub = false
        this.isInput = false
        this.inputValue = ''
      }
    })
  },
  methods: {
    onCategoryClick(code) {
      this.$store.commit('search/setCurrentCode', code)
      this.onSearch()
    },
    onInput(e) {
      this.isShowSub = !!e.target.value
    },
    clearValue() {
      this.inputValue = ''
      this.$nextTick(() => {
        this.$refs.input.focus()
      })
    },
    onSearch() {
      if (!this.inputValue) return
      this.isShowSub = false
      this.isInput = false
      const searchWord = this.inputValue
      this.inputValue = ''
      if (this.$route.path === '/search') {
        this.$store.commit('search/setSearchWord', searchWord)
        this.$store.commit('search/setPageNo', 1)
        this.$store.dispatch('search/getResult')
        if (this.$route.query.wd === searchWord) return
        this.$router.replace({
          path: '/search',
          query: {
            wd: searchWord
          }
        })
        return
      }
      this.$router.push({
        path: '/search',
        query: {
          wd: searchWord
        }
      })
    },
    onSearchClick() {
      this.isInput = true
      this.$nextTick(() => {
        this.$refs.input.focus()
      })
    }
  }
}
</script>
<style lang="less" scoped>
.search-input {
  position: relative;
  .sub-search {
    position: absolute;
    z-index: 99;
    top: 52px;
    left: 0;
    background-color: #fff;
    padding: 10px 0;
    .sub-search-item {
      width: 314px;
      height: 32px;
      font-size: 14px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
      line-height: 20px;
      padding: 6px 16px;
      white-space: nowrap;
      cursor: pointer;
      &:hover {
        background: rgba(0, 0, 0, 0.06);
      }
      .text {
        width: 150px;
        overflow: hidden;
        white-space: nowrap;
        word-break: break-all;
        text-overflow: ellipsis;
        float: left;
      }
      .label {
        float: right;
        height: 20px;
        background: rgba(0, 0, 0, 0.1);
        border-radius: 10px;
        line-height: 20px;
        padding: 0 12px;
        padding-right: 17px;
        position: relative;
        .label-text {
          display: inline-block;
          max-width: 100px;
          overflow: hidden;
          white-space: nowrap;
          word-break: break-all;
          text-overflow: ellipsis;
        }
        .enter-icon {
          position: absolute;
          top: 6px;
          right: 7px;
        }
      }
    }
  }
  .icon {
    display: inline-block;
    vertical-align: middle;
    width: 30px;
    height: 30px;
    background: url('../../assets/images/search-icon.png') no-repeat center;
    background-size: 30px 30px;
    cursor: pointer;
  }
  .icon2 {
    background: url('../../assets/images/search-icon2.png') no-repeat center;
    background-size: 30px 30px;
  }
  .input-wrap {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    width: 314px;
    height: 32px;
    background: #efefef;
    border-radius: 2px;
    padding: 0 36px;
    .input-icon {
      position: absolute;
      left: 8px;
      top: 2px;
      width: 30px;
      height: 30px;
      background: url('../../assets/images/search-icon.png') no-repeat center;
      background-size: 30px 30px;
    }
    .close-icon {
      position: absolute;
      right: 16px;
      top: 9px;
      font-size: 16px;
      height: 16px;
      line-height: 16px;
      &:hover {
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }
  input {
    position: relative;
    z-index: 99;
    display: block;
    width: 100%;
    outline: none;
    -webkit-appearance: none; /*去除系统默认的样式*/
    box-shadow: none;
    background: none;
    border: none;
    opacity: 1;
    height: 32px;
    line-height: 18px;
  }
}
</style>
