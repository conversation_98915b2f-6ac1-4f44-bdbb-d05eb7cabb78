<template>
  <div class="apisearch-table-wrap">
    <PageTable
      :columns="apiSearchColumns"
      :list="apiOrderList"
    />
  </div>
</template>

<script>
import PageTable from '@/components/PageTable'
export default {
  components: {
    PageTable
  },
  data() {
    return {
      searchText: '',
      current: 1,
      apiOrderList: [],
      menuList: [],
      apiSearchColumns: [
        {
          renderHeader: (h) => h('span', this.$t('apiSearchTableColumns.title1')),
          key: 'title',
          tree: false,
          width: 250,
          render: (h, scope) => {
            let locationPathArr = location.pathname.split('/')
            let productCode = ''
            let htmlCon = ''
            // console.log(location.pathname, '<--location.pathname')
            // console.log(scope.row, '<--scope.row')
            // 兜底 `<a href="/docs/apis/${scope.row.uri}" target='_blank'>${scope.row.title}</a>`
            // 循环遍历 找是否存在当前api 不存在则不跳转
            let hasApi = []
            this.menuList.forEach(item => {
              if(item.contentUri.indexOf('apiCategories.json') > -1) {
                item.children.forEach(el => {
                  if(el.apiId === scope.row.apiId) {
                    hasApi.push(el)
                  }
                })
              }
            })

            if(location.pathname.indexOf('docs/apis') > -1) {
              productCode = `${locationPathArr[3]}/`
              htmlCon = `<a href="/docs/apis/${productCode}${scope.row.uri}" target='_blank'>${scope.row.title}</a>`
            } else {
              productCode = `${locationPathArr[3]}/`
              if(hasApi.length === 0) {
                htmlCon = `<a href="/docs/apis/${scope.row.uri}" target='_blank'>${scope.row.title}</a>`
              } else {
                htmlCon = `<a href="/docs/solutions/${productCode}${scope.row.uri}" target='_blank'>${scope.row.title}</a>`
              }
            }
            return h('div',{domProps: {
              innerHTML: htmlCon
            }})
          },
        },
        {
          renderHeader: (h) => h('span', this.$t('apiSearchTableColumns.title2')),
          key: 'desc',
          tree: false,
          type: 'html',
          width: 250,
        },
      ]
    }
  },
  watch: {
    '$store.state.apiDocs.showSpiApiData': {
      handler(newValue) {
        this.apiOrderList = newValue
      },
      immediate: true
    },
    '$store.state.solutions.menuList': {
      handler(newValue) {
        this.menuList = newValue
      },
      immediate: true
    },
    
  },
  mounted() {
  },
  methods: {
  }
}
</script>

<style lang="less" scoped>
.apisearch-table-wrap {
  // overflow-x: auto;
  width: 500px;
  /deep/ .ant-table {
    min-width: 700px;
  }
}
</style>
