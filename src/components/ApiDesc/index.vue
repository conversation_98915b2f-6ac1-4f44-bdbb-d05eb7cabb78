<template>
<ul class="api-desc" ref="desc">
  <li class="desc-item">
    <div class="label">{{$t('请求方式')}}：</div>
    <div class="api-path-wrap">
      <template v-if="definition.method">
        <div :class="method" :key="method" v-for="method in definition.method.split(',')">
          <span class="font-size-10px">{{ method }}</span>
        </div>
      </template>
      <div class="origin">
        <a-popover overlayClassName="custom-popover">
          <template slot="content">
            <p style="cursor: pointer;" @click="copy('https://sandbox.yeepay.com/yop-center')">沙箱地址：https://sandbox.yeepay.com/yop-center</p>
          </template>
          https://openapi.yeepay.com/yop-center
        </a-popover>
        <div class="path" @click="copy(definition.path)">
          <a-popover content="点击复制" overlayClassName="custom-popover">
            {{ definition.path }}
          </a-popover>
        </div>
      </div>
    </div>
    <div class="debug" @click="debug" v-if="!isMobile">
      <span class="icon iconfont icon-tiaoshi" style="font-size: 10px; margin-right: 4px;"></span>调试</div>
    <a href="https://open.yeepay.com/docs/platform/developTools/zxts" style="margin-left: 12px;font-size: 13px;">如何调试?</a>
  </li>
  <li class="desc-item">
    <div class="label">{{$t('对接要求')}}：</div>
    <ul class="tags-wrap">
      <li class="tags-item tags-green" v-if="definition.authority && definition.authority[0]">
        <Tooltip transfer max-width="370" :content="$t('cfcaTip')" placement="top">{{ $t('cfca') }}</Tooltip>
      </li>
      <template v-if="
            definition.securityReqs && definition.securityReqs.length > 0
          ">
        <li class="tags-item tags-yellow" v-if="getSecurity(definition.securityReqs)">
          <Tooltip transfer max-width="370" :getPopupContainer="() => document.body" :content="
                $t('securityTip', {
                  security: getSecurity(definition.securityReqs),
                })
              " placement="top">
            {{
              $t('security', {
              security: getSecurity(definition.securityReqs),
              })
              }}
          </Tooltip>
        </li>
      </template>
      <li class="tags-item tags-green" v-if="
            definition.securityReqs &&
            definition.securityReqs.length > 0 &&
            definition.securityReqs.find((item) => item === 'YOP-OAUTH2')
          ">
        <Tooltip transfer max-width="370" :content="$t('oauth2Tip')" placement="top">{{ $t('oauth2') }}</Tooltip>
      </li>
      <li class="tags-item tags-blue" v-if="definition.options && definition.options.includes('IDEMPOTENT')">
        <Tooltip transfer max-width="370" :content="$t('midengTip')" placement="top">{{ $t('mideng') }}</Tooltip>
      </li>
      <li class="tags-item tags-yellow" v-if="definition.options && definition.options.includes('SANDBOX')">
        {{ $t('sandbox') }}
      </li>
    </ul>
  </li>
  <li class="desc-item" style="align-items: flex-start;" v-if="apiDesc || definition.idempotentDesc">
    <div class="label">{{$t('apiDesc')}}：</div>
    <div class="desc" style="line-height: 24px;" >
      <div v-if="apiDesc" v-html="apiDesc"></div>
      <div v-if="definition.idempotentDesc" v-html="definition.idempotentDesc"></div>
    </div>
  </li>
</ul>
</template>

<script>
import { emtyDesc, formateA, goApifoxUrl } from '@/utils'
export default {
  props: ['definition', 'scenesMsg'],
  computed: {
    apiDesc() {
      if (emtyDesc(this.definition.desc)) {
        return this.definition.desc
      }
      if (emtyDesc(this.scenesMsg)) {
        return this.scenesMsg
      }
      return ''
    },
    isMobile() {
      return this.$store.state.isMobile
    },
  },
  watch: {
    apiDesc() { 
      this.fixA()
    }
  },
  mounted() {
    this.fixA()
  },
  methods: {
    getSecurity(list) {
      const newList = list
        .map((item) => {
          return item.split('-')[1]
        })
        .filter((item) => item !== 'OAUTH2')
      return newList.join('、')
    },
    copy(code) {
      var oInput = document.createElement('textarea') //创建一个隐藏input
      oInput.value = code //赋值
      document.body.appendChild(oInput)
      oInput.select() // 选择对象
      document.execCommand('Copy') // 执行浏览器复制命令
      oInput.className = 'oInput'
      oInput.style.display = 'none'
      this.$message.success(this.$t('copySuccess'))
    },
    debug() {
      this.$upLog(120, {
        feature_id: '调试'
      })
      goApifoxUrl(this.$store.state.apiDocs)
    },
    fixA() {
      this.$nextTick(() => {
        if (!this.$refs.desc) return
        formateA(this.$refs.desc)
      })
    },
  }
}
</script>

<style lang="less" scoped>
.api-desc {
  font-family: PingFangSC, PingFang SC;

  .desc-item {
    display: flex;
    margin-bottom: 8px;
    align-items: center;
    flex-wrap: wrap;
    &:last-child {
      margin-bottom: 0;
    }

    .label {
      min-width: 78px;
      font-weight: 400;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      line-height: 24px;
      flex-shrink: 0;
    }
    .api-path-wrap {
      background: rgba(0, 0, 0, 0.03);
      padding: 4px 8px 4px 4px;
      border-radius: 4px;
      display: flex;
      align-items: flex-start;
      justify-content: center;
      font-family: PingFangSC, PingFang SC;
      margin-right: 8px;
      .origin {
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
        line-height: 18px;
        word-break: break-all;
      }
      .path {
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        word-break: break-all;
        position: relative;
        cursor: pointer;
        display: inline;
        &:hover {
          &::after {
            content: '';
            position: absolute;
            bottom: 0px;
            left: 0;
            right: 0;
            display: block;
            border-bottom: 1.5px dashed rgba(0, 0, 0, 0.45);
          }
        }
      }
    }
  }
}
.debug {
  width: 60px;
  height: 30px;
  background: #FFFFFF;
  box-shadow: 0px 0px 4px 0px rgba(230, 255, 223, 0.12);
  border-radius: 6px;
  border: 1px solid #52BF63;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 400;
  font-size: 14px;
  color: #52BF63;
  cursor: pointer;
}

.tags-wrap {
  .tags-item {
    margin-right: 4px;
    cursor: pointer;
    display: inline-block;
    vertical-align: top;
    border-radius: 4px !important;
  }

  .tags-red {
    color: #F27B3F;
    background: rgba(255, 129, 66, 0.12);
  }

  .tags-yellow {
    color: #F27B3F;
    background: rgba(255, 129, 66, 0.12);
  }

  .tags-blue {
    color: #3B8CFF;
    background: rgba(22, 119, 255, 0.12);
  }

  .tags-green {
    color: #52BF63;
    background: rgba(82, 191, 99, 0.12);
  }

  .tags-red,
  .tags-yellow,
  .tags-blue,
  .tags-green {
    height: 24px;
    border-radius: 2px;
    font-size: 12px;
    font-weight: 400;
    line-height: 24px;
    padding: 0px 6px;

    :hover {
      opacity: 0.8;
    }
  }
}
</style>
