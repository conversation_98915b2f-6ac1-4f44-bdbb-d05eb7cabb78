import Vue from 'vue'
import VueRouter from 'vue-router'

const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err)
}
Vue.use(VueRouter)
const Product = () => import('@/views/Product')
const ApiDocs = () => import('@/views/ApiDocs')
const Guide = () => import('@/views/Guide')
const Open = () => import('@/views/Open')
const Problem = () => import('@/views/Problem')
const Home = () => import('@/views/PrerenderPage/Home.vue')
const Mhome = () => import('@/views/PrerenderPage/Mhome.vue')
const Search = () => import('@/views/Search')
const CustomSolution = () => import('@/views/CustomSolution')
const ProductCenter = () => import('@/views/Product/ProductCenter')
const Solution = () => import('@/views/PrerenderPage/Solution.vue')
const SolutionDetail = () => import('@/views/PrerenderPage/SolutionDetail.vue')
const routes = [
  {
    path: '/home',
    name: 'Home',
    component: Home,
    meta: {
      enableFixed: true,
      maxWidth: '1280px',
      title: '易宝开放平台'
    },
    alias: '/'
  },
  {
    path: '/mhome',
    name: 'Mhome',
    component: Mhome,
    meta: {
      enableFixed: true,
      maxWidth: 'none',
      title: '易宝开放平台'
    }
  },
  {
    path: '/productCenter',
    name: 'ProductCenter',
    component: ProductCenter,
    meta: {
      enableFixed: false,
      useMobile: true,
      maxWidth: '1280px',
      title: '产品中心'
    }
  },
  {
    path: '/solution',
    name: 'Solution',
    component: Solution,
    meta: {
      enableFixed: false,
      useMobile: true,
      maxWidth: '1280px',
      title: '解决方案'
    }
  },
  {
    path: '/solution/detail',
    name: 'SolutionDetail',
    component: SolutionDetail,
    meta: {
      enableFixed: false,
      useMobile: true,
      maxWidth: '1280px',
      title: '解决方案'
    }
  },
  {
    path: '/search',
    name: 'Search',
    component: Search,
    meta: {
      maxWidth: '1280px'
    }
  },
  {
    path: '/docs/*/products/*/apis',
    component: Product
  },
  {
    path: '/docs/solutions/*',
    component: CustomSolution
  },
  {
    path: '/docs/solutions/*/apis/*',
    component: CustomSolution
  },
  {
    path: '/docs/*/apis/*',
    component: ApiDocs
  },
  {
    path: '/docs-v2/*/apis/*',
    component: ApiDocs
  },
  {
    path: '/docs/*/products/*',
    component: Product
  },
  {
    path: '/docs/products/*',
    component: Product
  },
  {
    path: '/docs/*/open/*',
    component: Open
  },
  {
    path: '/docs/open/*',
    component: Open
  },
  {
    path: '/docs/apis/*',
    component: ApiDocs
  },
  {
    path: '/docs',
    component: ApiDocs
  },
  {
    path: '/docs-v2/apis/*',
    component: ApiDocs
  },
  {
    path: '/docs-v2',
    component: ApiDocs
  },
  {
    path: '/docs/platform/problem',
    component: Problem
  },
  {
    path: '/docs/platform*',
    component: Guide
  },
  {
    path: '/docs/*/platform/*',
    component: Guide
  },
  {
    path: '/docs/403',
    component: () =>
      import(/* webpackChunkName: "fail" */ '@/components/Exception/403'),
    meta: { title: '403', maxWidth: '1280px' }
  },

  {
    path: '/docs/404',
    component: () =>
      import(/* webpackChunkName: "fail" */ '@/components/Exception/404'),
    meta: { title: '404', maxWidth: '1280px' }
  },
  {
    path: '/docs/500',
    component: () =>
      import(/* webpackChunkName: "fail" */ '@/components/Exception/500'),
    meta: { title: '500', maxWidth: '1280px' }
  },
  {
    path: '*',
    component: () =>
      import(/* webpackChunkName: "fail" */ '@/components/Exception/404'),
    meta: { title: '404', maxWidth: '1280px' }
  }
]

const router = new VueRouter({
  mode: 'history',
  fallback: false,
  routes
})

export default router
