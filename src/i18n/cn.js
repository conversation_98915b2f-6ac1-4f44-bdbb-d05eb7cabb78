export default {
  tongzhicanshu: '通知参数',
  shilibaowen: '示例报文',
  copy: '复制',
  yes: '是',
  no: '否',
  recommend: '推荐',
  unnecessary: '无须',
  copySuccess: '复制成功',
  all: '全部',
  filter: '筛选搜索结果',
  serach: '搜索',
  searchNoResult: '抱歉，没有搜索到您想要的结果',
  update: '更新日期：',
  tableParamsTip:
    '该object({name})与上面第{level}层的({name})是同一个对象，不再重复展示',
  searchTip:
    '“<span style="color: #52bf63">{searchTipWord}</span>” 及其后面的字词均被忽略，因为查询关键词限制在<span style="color: #52bf63">40</span>个字符以内。',
  reset: '重置',
  pleaseInput: '请输入',
  codeLang: '语言',
  condeLangType: '类型',
  langTitle: 'English',
  keyWordSearch: '搜索',
  loginDeveloper: '登录',
  loginDeveloperM: '登录开发者中心',
  kuajingLogin: '跨境商户登录',
  biaozhunLogin: '标准商户登录',
  serviceHotline: '7*24小时客服:',
  phonenum: '95070',
  phonenumsub: '(YOP合作 请按7）',
  footerText:
    '易宝支付有限公司版权所有© 2003-{time}京ICP备08100193号-1京公网安备11010502007599号',
  home: {
    title: '开放平台',
    myService: '我的客服',
    evaluate: '评价与反馈',
    top: '回到顶部',
  },
  updateTime: '更新时间',
  cfcaTip: '需要使用CFCA证书才能正常调用',
  midengTip: '发起一次或者多次请求的结果是一致',
  oauth2Tip: '需要完成OAUTH2授权',
  securityTip: '需要使用{security}进行签名验签',
  cfca: '需要CFCA证书',
  mideng: '支持幂等',
  sandbox: '支持沙箱',
  oauth2: '需要OAUTH2授权',
  security: '需要签名验签',
  techTips: {
    title: '技术提示',
    nonIdempotent: {
      title: '⚠️ 非幂等接口异常处理',
      description: '此接口为非幂等操作，请求失败时请检查业务状态后再决定是否重试，避免重复处理导致业务异常。建议使用唯一业务单号进行幂等控制。'
    },
    retryMechanism: {
      title: '🔄 重试机制建议',
      description: '建议实现指数退避重试机制，最大重试3次，间隔时间：1s、2s、4s。对于网络超时等临时性错误可以重试，对于参数错误等永久性错误不应重试。'
    },
    timeoutHandling: {
      title: '⏱️ 超时处理',
      description: '建议设置合理的超时时间（建议30-60秒），并实现超时后的降级处理。超时不代表请求失败，请通过查询接口确认最终状态。'
    },
    concurrencyControl: {
      title: '🔒 并发控制',
      description: '涉及资金操作，建议在客户端实现防重复提交机制，避免并发请求导致重复扣款等问题。可使用分布式锁或唯一约束实现。'
    },
    signatureSecurity: {
      title: '🔐 签名安全',
      description: '签名密钥请妥善保管，建议定期轮换。生产环境禁止在前端暴露密钥，应在服务端进行签名计算。',
      actionText: '查看签名指南'
    },
    parameterValidation: {
      title: '✅ 参数校验',
      description: '请在客户端进行参数预校验，减少无效请求，提升用户体验。同时服务端也会进行完整的参数校验。'
    },
    rateLimiting: {
      title: '🚦 限流频控',
      description: '接口存在频率限制，建议合理控制调用频率，避免触发限流机制。建议实现请求队列和限流重试逻辑。'
    }
  },
  menu: {
    home: '首页',
    solution: '解决方案',
    product: '产品中心',
    doc: '文档中心',
    apiDocsTitle: 'API文档',
    guideTitle: '接入指引',
    questionTitle: '常见问题',
    developer: '开发者服务',
    tyfa: '通用方案',
    bzshsfk: '标准商户收付款方案',
    ptssfk: '平台商收付款方案',
    fwssfk: '服务商解决方案',
    dlssfk: '代理商解决方案',
    hyfa: '行业方案',
    retail: '零售行业',
    bankAndFund: '银基行业',
    consumptionFinancial: '消费金融',
    airTravel: '航旅行业',
    administrative: '政务行业',
    crossBorder: '跨境行业',
    electricity: '电力行业',
    insurance: '保险行业',
    energy: '能源行业',
    catering: '餐饮行业',
    cosmeticMedicine: '医美行业',
    nighttimeEconomy: '夜经济行业',
    zfcp: '支付产品',
    yjzf: '一键支付',
    eBank: '网银支付',
    wechatOffiaccount: '微信公众号支付',
    merchantScan: '商家扫码支付',
    userScan: '用户扫码支付',
    miniProgram: '小程序支付',
    sdkPay: '银联SDK支付',
    jsPay: '银联JS支付',
    alipayLife: '支付宝生活号',
    staticQR: '静态台牌',
    zjgl: '资金管理',
    recharge: '线下汇入',
    ptzz: '公对公转账',
    dfdf: '代付代发',
    rzfw: '认证服务',
    identify00001: '银行卡认证',
    identify00002: '实名认证',
    msgIdentify: '信息认证',
    userGuide: '快速接入',
    certIntro: '密钥配置',
    sdkGuide: '对接API',
    notifySummary: '结果通知',
    apiAll: 'API概览（旧）',
    apiOverview: 'API概览',
    developTools: '开发工具',
    sdkTools: '密钥工具',
    platformSdk: '平台SDK',
    yopIsvGateway: '结果通知工具',
    yizhanshi: '一站式开发套件',
    help: '帮助与支持',
    callUs: '联系我们',
    platformDesc: '平台简介',
    cooperation: '商务合作',
    yeepayCom: '易宝官网',
    deployTool: '部署工具',
    rongqiyun: '容器云平台',
  },
  tableColumns: {
    title1: '参数',
    title2: '类型（最大长度）',
    title3: '必填',
    title4: '描述',
    title5: '加密',
    tip: '示例值',
  },
  apiSearchTableColumns: {
    title1: '查单API',
    title2: '描述',
  },
  errorCodeTableColumns: {
    title1: '子错误码(subErrorCode)',
    title2: '子错误码描述(subErrorMsg)',
    title3: '解决方案',
  },
  format: '格式',
  securityReqsTip: '接口调用安全需求',
  authorityTip: '当前为高风险接口，需在开发者中心上传CFCA公钥证书',
  historyMsg:
    '当前为历史版本API，我们不推荐按本页面内容进行对接。为获得更好的技术支持，您可以点击查看{action}。',
  typeTip:
    '该服务可能根据系统、网络等综合需要采取但不仅限于限速、限并发等处理，请根据实际业务场景合理使用。',
  newVersion: '最新版本',
  apiDesc: '接口描述',
  instructions: '使用说明',
  commonRepParams: '公共请求参数',
  catCommonRepParams: '查看公共请求参数',
  commonRepParamsTip: '无SDK对接API，请查看请求头参数；若使用SDK对接无需关注。',
  repParams: '请求参数',
  reqHeader: '请求头',
  reqBody: '请求体',
  reqParameters: '查询参数',
  catReqExamples: '请求体示例',
  commonResParams: '公共响应参数',
  commonResParamsTip: '无SDK对接API，请查看响应头参数；若使用SDK对接无需关注。',
  catCommonResParams: '查看公共响应参数',
  respParams: '响应参数',
  resHeader: '响应头',
  resBody: '响应体',
  catRespExamples: '响应体示例',
  catExm: '查看示例',
  codeExm: '示例代码',
  errorCode: '业务错误码',
  errorCodeTipA: '平台错误码',
  errorCodeTip:
    '业务错误码包含错误码（固定值：40044，详情查看{action}）、错误描述（固定值：业务处理失败）、子错误码和子错误描述，其中子错误码、子错误描述以及对应的解决方案如下所示：',
  callback: '结果通知',
  callbackTipA: '结果通知机制说明',
  callbackTip: '下方为解密后的明文参数结构，解密过程和对接流程请参考：{action}',
  callbackTip1A: '查看解密方法',
  callbackTip1:
    '通知参数：response、customerIdentification ，其中 response 为密文串，获取到之后解密成明文。',
  callbackTip2A: '查看更多规则',
  callbackTip2:
    '通知策略：收到易宝回调通知需回写大写“SUCCESS”，如没有回写则最多通知 9次，重试延迟时间分别为：5,5,20,270,600,900,1800,3600,14400（秒），9次后没有拿到回写则停止通知。 {action}',
  callbackTip3: '接入结果通知的同时，请务必接入查单接口。当一笔订单没有收到结果通知时，若通知超时时间已超过您的系统容忍量，您可以主动调用查单接口查询订单状态。',
  apiList: 'API列表',
  apiSearchOrder: '查单API',
  apiListDes:
    '以下列表包含该产品所涉及的所有接口，点击“接口名称”可查看接口说明、请求参数、请求示例、响应参数、响应示例、示例代码、业务错误码。',
  apiName: '接口名称',
  apply: '应用场景',
  // # 报错页面
  '403ErrorTip': '抱歉，你无权访问该页面...',
  '404ErrorTip': '抱歉，访问的页面不存在...',
  '500ErrorTip': '抱歉，服务暂时不可用...',
  getBackPage: '返回上一页',
  getHomePage: '返回首页',
  reload: '刷新',
  productName: '产品名称',
  moduleName: '模块名称',
  productModal: '产品模块',
  apiName2: 'API名称',
  desc: '描述',
  accessDiagnosis: '接入诊断',
  '接口说明': '接口说明',
  '请求方式': '请求方式',
  '对接要求': '对接要求',
  '更多参数': '更多参数',
  '展开全部': '展开全部',
  '收起全部': '收起全部',
  '展开': '展开',
  '收起': '收起',
}
