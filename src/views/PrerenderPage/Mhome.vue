<template>
  <div class="mobile-home-wrap">
    <div class="header-wrap">
      <div class="title">易宝开放平台 为开发者赋能</div>
      <div class="sub-title">标准化、全场景覆盖、提升企业整体竞争力</div>
    </div>
    <div class="product-wrap">
      <div class="title">完整的产品能力</div>
      <div class="hot-product-list">
        <div class="product-item" v-for="item in hotProducts" :key="item.code" @click="goProduct(item.code)">
          <img class="icon" :src="item.icon" />
          <div class="name text-o">{{ item.title }}</div>
          <div class="desc text-o">{{ item.desc }}</div>
        </div>
      </div>
      <a class="link" href="/productCenter">探索更多产品<a-icon type="right" :style="{ fontSize: '14px' }" /></a>
    </div>
    <div class="solution-wrap">
      <div class="title">解决方案</div>
      <div
        class="solution-list-wrap"
        id="solution-list-wrap-mobile"
        @touchstart="touchstart"
        @touchend="touchend"
        @scroll.passive="onScroll"
      >
        <ul class="solution-list">
          <li v-for="(item, index) in solutionList" :key="index">
            <div
              class="solution-item"
              :class="{ 'solution-item-scalc': currentSolution !== index }"
            >
              <img :src="item.content.imageUrl" alt="" />
              <div class="content-wrap">
                <template v-if="index === 0">
                  <div class="fist-item" style="margin-bottom: 20px">
                    <div class="pay-item">
                      <a :href="item.content.list[0].linkUrl">
                        <img
                          src="https://img.yeepay.com/fe-resources/yop-docs/images/mhome/standard.png"
                          alt=""
                        />
                        <div class="text">标准商户收付</div>
                      </a>
                    </div>
                  </div>
                  <div class="fist-item">
                    <div class="pay-item" style="margin-right: 58px">
                      <a :href="item.content.list[1].linkUrl">
                        <img
                          src="https://img.yeepay.com/fe-resources/yop-docs/images/mhome/platform.png"
                          alt=""
                        />
                        <div class="text">平台商收付</div>
                      </a>
                    </div>
                    <div class="pay-item">
                      <a :href="item.content.list[2].linkUrl">
                        <img
                          src="https://img.yeepay.com/fe-resources/yop-docs/images/mhome/service.png"
                          alt=""
                        />
                        <div class="text">服务商收付</div>
                      </a>
                    </div>
                  </div>
                </template>
                <template v-else>
                  <div class="content-title">
                    {{ item.content.title }}
                  </div>
                  <div class="content-desc">
                    {{ item.content.desc }}
                  </div>
                  <a :href="item.content.linkUrl" class="solution-item-link"
                    >查看详情 ></a
                  >
                </template>
              </div>
            </div>
          </li>
        </ul>
      </div>
      <div class="solution-icon-wrap" id="solution-icon-wrap-mobile">
        <ul class="solution-icon-list">
          <li
            class="solution-icon-item"
            v-for="(item, index) in solutionList"
            :class="{ 'solution-icon-active': currentSolution === index }"
            @click="onIconClick(index)"
            :key="index"
          >
            <span
              class="iconfont icon"
              :style="{ fontSize: item.iconSize }"
              v-html="item.icon"
            ></span>
          </li>
        </ul>
      </div>
    </div>
    <div class="api-wrap">
      <div class="develop-tip">专为开发人员设计</div>
      <div class="develop-title">强大且易用的API</div>
      <div class="develop-img-wrap">
        <img
          src="https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>"
          alt=""
        />
      </div>
      <div class="develop-desc">
        我们来处理那些抽象的事物，让您的团队免去整合不同系统的麻烦，也不需要花费数月时间来集成支付功能。
      </div>
      <div class="desc-item" style="margin-bottom: 40px">
        <div class="desc-item-title">多种语言版本的SDK工具</div>
        <div class="desc-item-sub-title" style="margin-bottom: 8px">
          我们准备了从 Java、Go、Python、PHP、.NET、NodeJs
          等多种语言的SDK，方便你的快速接入。
        </div>
        <a class="link" href="/docs/platform/sdk_guide/sdk-guide">查看 ></a>
      </div>
      <div class="desc-item">
        <div class="desc-item-title">便捷的密钥工具</div>
        <div class="desc-item-sub-title" style="margin-bottom: 8px">
          可以方便你快速生成接入所需的密钥，并能安全下载并激活CFCA证书文件。
        </div>
        <a class="link" href="/docs/platform/developTools/keyTools">查看 ></a>
      </div>
    </div>
    <div class="data-wrap">
      <div class="data-tip">安全、合规、稳定、全面的交易服务</div>
      <div class="data-title">为企业提供强有力的支持 助力业务飞速拓展</div>
      <ul>
        <li class="desc-item">
          <div class="desc-item-title">2.6亿+</div>
          <div class="desc-item-sub-title">
            每天API请求次数，峰值达1,000每秒
          </div>
        </li>
        <li class="desc-item">
          <div class="desc-item-title">2,000个+</div>
          <div class="desc-item-sub-title">API服务接口</div>
        </li>
        <li class="desc-item">
          <div class="desc-item-title">30个+</div>
          <div class="desc-item-sub-title">交易服务解决方案与产品服务</div>
        </li>
        <li class="desc-item">
          <div class="desc-item-title">99.9%+</div>
          <div class="desc-item-sub-title">交易服务可用性</div>
        </li>
      </ul>
    </div>
    <div class="brand-wrap">
      <div class="brand-background"></div>
      <div class="brand-title">超过 100+企业正在使用易宝开放平台</div>
      <div class="brand-list">
        <div class="brand-item" v-for="item in 24" :key="item">
          <img
            :src="`https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/${item}.png`"
            alt=""
          />
        </div>
      </div>
      <div class="brand-start">
        <img
          class="img"
          src="https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>"
          alt=""
        />
        <div class="title">准备好开始了？</div>
        <div class="title-desc">
          与我们销售取得联系，签约并入网，联调成功后，就可以收款了。我们也可以为您的公司量身制定最合适的套餐。
        </div>
        <div class="link-wrap">
          <a
            class="link"
            href="https://www.yeepay.com/customerService/businessCooperation"
          >
            联系销售 >
          </a>
        </div>
      </div>
      <div class="item" style="margin-bottom: 10px">
        <img
          class="img"
          src="https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>"
          alt=""
        />
        <div class="text">
          <div class="text-title">开始你的集成</div>
          <div class="text-desc">
            若你还未签约入网成功或正在入网中，你现在根据需要了解平台的API。待服务开通后，就可以开始联调与部署工作了。
          </div>
          <a class="text-link" href="/docs">全部API ></a>
        </div>
      </div>
      <div class="item">
        <img
          class="img"
          src="https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>"
          alt=""
        />
        <div class="text">
          <div class="text-title">成为平台伙伴</div>
          <div class="text-desc">
            若你有足够的实力，想将你的服务集成到本平台，请留下你的联系信息与服务内容，我们收到信息后会及时与你联系。
          </div>
          <a
            class="text-link"
            href="https://www.yeepay.com/customerService/businessCooperation"
            >成为伙伴 ></a
          >
        </div>
      </div>
    </div>
    <PageFooter style="background-color: #fff" />
  </div>
</template>

<script>
import Api from '@/api/product'
export default {
  data() {
    return {
      active: 0,
      showSub: null,
      currentSolution: 0,
      onTouch: false,
      timer: null,
      listWrap: null,
      iconWrap: null,
      hotProducts: []
    }
  },
  computed: {
    footerLinkList() {
      return this.$store.state.footerLinkList
    },
    isMobile() {
      return this.$store.state.isMobile
    },
    solutionList() {
      return this.$store.state.solutionList
    },
    recommendAppList() {
      return this.$store.state.recommendAppList
    },
  },
  created() {
    if (window.__PRERENDER_INJECTED && window.__PRERENDER_INJECTED.pre) return
    if (!this.isMobile) this.$router.replace('/home')
  },
  mounted() {
    this.listWrap = document.querySelector('#solution-list-wrap-mobile')
    this.clientWidth = document.documentElement.clientWidth - 30
    this.loadProduct()
    this.$store.dispatch('getProductMenu')
  },
  methods: {
    goProduct(code) {
      window.location.href = `/docs/products/${code}/index.html`
    },
    changeApp(index) {
      this.active = index
      this.recommendListWrap.scrollLeft =
        document.documentElement.clientWidth * index
    },
    onIconClick(index) {
      this.currentSolution = index
      this.listWrap.scrollLeft = this.clientWidth * this.currentSolution
    },
    touchstart() {
      this.onTouch = true
    },
    touchend() {
      this.onTouch = false
      if (this.timer) return
      const scrollLeft = this.listWrap.scrollLeft
      const index = Math.round(scrollLeft / this.clientWidth)
      this.currentSolution = index
      this.listWrap.scrollLeft = this.clientWidth * this.currentSolution
    },
    onScroll() {
      if (this.timer) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        clearTimeout(this.timer)
        this.timer = null
        if (this.onTouch) return
        const scrollLeft = this.listWrap.scrollLeft
        const index = Math.round(scrollLeft / this.clientWidth)
        this.currentSolution = index
        this.listWrap.scrollLeft = this.clientWidth * this.currentSolution
      }, 500)
    },
    async loadProduct() {
      try {
        const docBasic = await Api.getDocBasic()
        const hotProduct = await Api.getHotProduct()
        this.hotProducts = hotProduct.map(code => {
          return {
            code,
            title: docBasic[`${code}.title`],
            desc: docBasic[`${code}.desc`],
            icon: docBasic[`${code}.icon`],
          }
        }).slice(0, 4)
      } catch (error) {
        console.log(error)
      }
    }
  },
}
</script>

<style lang="less" scoped>
.mobile-home-wrap {
  background-color: #fff;
  font-family: PingFangSC, PingFang SC;
  .header-wrap {
    height: 430px;
    background: url('../../assets/banner-mobile.png')
      no-repeat center;
    background-size: 100% 430px;
    padding-top: 321px;
    .title {
      height: 33px;
      font-weight: 600;
      font-size: 24px;
      color: #FFFFFF;
      line-height: 33px;
      text-align: center;
      margin-bottom: 8px;
    }
    .sub-title {
      height: 20px;
      font-weight: 400;
      font-size: 14px;
      color: #FFFFFF;
      line-height: 20px;
      text-align: center;
    }
  }
  .product-wrap {
    height: 644px;
    background: #F5F5F5;
    padding: 40px 0;
    .title {
      height: 30px;
      font-weight: 500;
      font-size: 22px;
      color: rgba(0,0,0,0.85);
      line-height: 30px;
      text-align: center;
      margin-bottom: 32px;
    }
    .hot-product-list {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      padding: 0 16px 16px;
      .product-item {
        min-width: 164px;
        height: 200px;
        background: #FFFFFF;
        border-radius: 16px;
        border: 1px solid #FFFFFF;
        display: inline-block;
        margin-bottom: 16px;
        margin-right: 16px;
        padding: 32px 16px 0;
        &:nth-of-type(even) {
          margin-right: 0;
        }
        flex: 1;
        .icon {
          width: 44px;
          height: 44px;
          margin: 0 auto 16px;
          display: block;
        }
        .name {
          height: 28px;
          font-weight: 600;
          font-size: 16px;
          color: rgba(0,0,0,0.8);
          line-height: 28px;
          text-align: center;
          margin-bottom: 8px;
        }
        .desc {
          height: 40px;
          font-weight: 400;
          font-size: 12px;
          color: rgba(0,0,0,0.45);
          line-height: 20px;
          text-align: center;
          white-space: wrap;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
        }
      }
    }
    .link {
      height: 20px;
      font-weight: 400;
      font-size: 14px;
      color: rgba(0,0,0,0.65);
      line-height: 20px;
      text-align: center;
      display: block;
    }
  }
  .solution-wrap {
    padding-top: 40px;
    .title {
      height: 30px;
      font-size: 22px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.65);
      line-height: 30px;
      text-align: center;
      margin-bottom: 40px;
    }
    .solution-list-wrap {
      width: 100vw;
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
      .solution-list {
        white-space: nowrap;
        float: left;
        padding: 0 20px 20px;
        display: flex;
        align-items: center;
        overflow: hidden;
        li {
          width: calc(100vw - 40px);
          height: 528px;
          margin-right: 10px;
          display: flex;
          align-items: center;
        }
        .solution-item {
          width: calc(100vw - 40px);
          height: 528px;
          background: #ffffff;
          box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.1);
          border-radius: 10px;
          .solution-item-link {
            display: block;
            text-align: center;
            height: 26px;
            font-size: 14px;
            font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei',
              SimSun, sans-serif;
            font-weight: 500;
            color: #fff;
            background: #52bf63;
            width: 110px;
            margin: 0 auto;
            border-radius: 13px;
            line-height: 26px;
          }
          img {
            width: calc(100vw - 40px);
            height: 235px;
          }
          .content-wrap {
            padding: 36px 20px 0;
            .fist-item {
              display: flex;
              justify-content: center;
              .pay-item {
                text-align: center;
                img {
                  width: 72px;
                  height: 72px;
                }
                .text {
                  height: 21px;
                  font-size: 15px;
                  font-family: PingFangSC-Regular, PingFang SC,
                    'Microsoft YaHei', SimSun, sans-serif;
                  font-weight: 400;
                  color: rgba(0, 0, 0, 0.85);
                  line-height: 21px;
                  text-align: center;
                }
              }
            }
            .content-title {
              height: 25px;
              font-size: 18px;
              font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei',
                SimSun, sans-serif;
              font-weight: 400;
              color: rgba(0, 0, 0, 0.85);
              line-height: 25px;
              text-align: center;
              margin-bottom: 20px;
            }
            .content-desc {
              height: 80px;
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei',
                SimSun, sans-serif;
              font-weight: 400;
              color: rgba(0, 0, 0, 0.45);
              line-height: 20px;
              text-align: center;
              white-space: pre-wrap;
              margin-bottom: 42px;
            }
            .content-img {
              width: 100%;
              height: auto;
            }
          }
        }
        .solution-item-scalc {
          width: calc(100vw - 40px);
          height: 500px;
        }
      }
    }
    .solution-icon-wrap {
      width: 100vw;
      text-align: center;
      height: 50px;
      line-height: 50px;
      .solution-icon-list {
        display: inline-flex;
        align-items: center;
        height: 50px;
        line-height: 50px;
        .solution-icon-item {
          width: 40px;
          height: 40px;
          display: flex;
          justify-content: center;
          align-items: center;
          .icon {
            font-size: 12px;
          }
        }
        .solution-icon-active {
          background: #f7f9fc;
          box-shadow: 0px 3px 5px 0px #e6e6e6;
          border-radius: 50%;
          .icon {
            color: #52bf63;
          }
        }
      }
    }
  }
  .api-wrap {
    padding-top: 16px;
    .develop-img-wrap {
      padding: 0 60px;
      margin-bottom: 41px;
      img {
        width: 100%;
      }
    }
    .develop-tip {
      height: 14px;
      font-size: 10px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
      line-height: 14px;
      text-align: center;
      margin-bottom: 10px;
    }
    .develop-title {
      height: 25px;
      font-size: 18px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
      line-height: 25px;
      text-align: center;
      margin-bottom: 20px;
    }
    .develop-desc {
      padding: 0 50px;
      height: 60px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.55);
      line-height: 20px;
      text-align: center;
      margin-bottom: 40px;
    }
    .desc-item {
      padding-left: 58px;
      padding-right: 50px;
      .desc-item-title {
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        line-height: 20px;
        margin-bottom: 10px;
        position: relative;
        &::before {
          content: '';
          width: 2px;
          height: 14px;
          background: #52bf63;
          border-radius: 1px;
          position: absolute;
          left: -6px;
          top: 3px;
        }
      }
      .desc-item-sub-title {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
        line-height: 20px;
      }
    }
  }
  .data-wrap {
    background: #041832;
    padding-top: 130px;
    padding-left: 60px;
    padding-bottom: 60px;
    position: relative;
    &::before {
      content: '';
      height: 100px;
      border-left: 100vw /2 solid #fff;
      border-top: 50px solid #fff;
      border-bottom: 50px solid transparent;
      border-right: 100vw /2 solid transparent;
      position: absolute;
      left: 0;
      top: -1px;
    }
    .data-tip {
      height: 17px;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.65);
      line-height: 17px;
      margin-bottom: 10px;
    }
    .data-title {
      width: 201px;
      height: 58px;
      font-size: 18px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 400;
      color: #ffffff;
      line-height: 25px;
      margin-bottom: 56px;
    }
    .desc-item {
      padding-left: 15px;
      position: relative;
      margin-bottom: 30px;
      &:last-child {
        margin-bottom: 0px;
      }
      &::before {
        content: '';
        width: 3px;
        height: 30px;
        background: #52bf63;
        border-radius: 1px;
        position: absolute;
        left: 0;
        top: 7px;
      }
      .desc-item-title {
        height: 45px;
        font-size: 32px;
        font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 500;
        color: #ffffff;
        line-height: 45px;
        margin-bottom: 16px;
      }
      .desc-item-sub-title {
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.65);
        line-height: 20px;
      }
    }
  }
  .brand-wrap {
    padding: 60px 20px;
    background: #f4f7fd;
    position: relative;
    z-index: 1;
    .brand-background {
      height: 436px;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      background: #fff;
      z-index: -1;
      &::after {
        content: '';
        height: 100px;
        border-right: 100vw /2 solid #f4f7fd;
        border-bottom: 50px solid #f4f7fd;
        border-top: 50px solid transparent;
        border-left: 100vw /2 solid transparent;
        position: absolute;
        left: 0;
        bottom: 0;
      }
    }
    .brand-title {
      height: 25px;
      font-size: 18px;
      font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
      line-height: 25px;
      text-align: center;
      margin-bottom: 30px;
    }
    .brand-list {
      display: flex;
      margin-right: -10px;
      flex-wrap: wrap;
      justify-content: center;
      margin-bottom: 61px;
      .brand-item {
        width: 105px;
        height: 37px;
        background: #ffffff;
        border-radius: 2px;
        margin-right: 10px;
        margin-bottom: 11px;
        img {
          width: 105px;
          height: 37px;
        }
      }
    }
    .brand-start {
      height: 413px;
      background: #ffffff;
      box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.1);
      border-radius: 6px;
      padding: 40px 20px;
      margin-bottom: 20px;
      .img {
        height: 106px;
        display: block;
        margin: 0 auto 40px;
      }
      .title {
        height: 30px;
        font-size: 22px;
        font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        line-height: 30px;
        text-align: center;
        margin-bottom: 20px;
      }
      .title-desc {
        height: 60px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        line-height: 20px;
        text-align: center;
        margin-bottom: 40px;
      }
      .link-wrap {
        display: flex;
        justify-content: center;
        align-items: center;
        .link-btn {
          width: 88px;
          height: 32px;
          background: #52bf63;
          border-radius: 16px;
          margin-right: 34px;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei',
            SimSun, sans-serif;
          font-weight: 400;
          color: rgba(255, 255, 255, 0.85);
          line-height: 32px;
          text-align: center;
        }
        .link {
          height: 20px;
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
            sans-serif;
          font-weight: 500;
          color: #52bf63;
          line-height: 20px;
        }
      }
    }
    .item {
      background: #ffffff;
      box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.1);
      border-radius: 6px;
      display: flex;
      align-items: flex-start;
      padding: 20px;
      .img {
        height: 65px;
        margin-right: 26px;
      }
      .text {
        text-align: left;
        .text-title {
          height: 20px;
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
            sans-serif;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.85);
          line-height: 20px;
          margin-bottom: 8px;
        }
        .text-desc {
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei',
            SimSun, sans-serif;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 17px;
          margin-bottom: 10px;
        }
        .text-link {
          height: 20px;
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
            sans-serif;
          font-weight: 500;
          color: #52bf63;
          line-height: 20px;
        }
      }
    }
  }
}
</style>
