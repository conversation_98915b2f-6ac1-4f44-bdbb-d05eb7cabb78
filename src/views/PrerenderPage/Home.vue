<template>
  <div class="home-wrap">
    <div class="banner-wrap">
      <div class="banner-box">
        <div class="title">易宝开放平台 为开发者赋能</div>
        <div class="sub-title">标准化、全场景覆盖、提升企业整体竞争力</div>
        <a class="sub-link link-btn" href="/docs/v2/platform/platform-profile">
          了解更多 >
        </a>
      </div>
    </div>
    <div class="product-capability">
      <div class="content">
        <div class="title">完整的产品能力</div>
        <div class="product-wrap">
          <div class="product-item" v-for="item in hotProducts" :key="item.code" @click="goProduct(item.code)">
            <img class="icon" :src="item.icon" />
            <div class="name text-o">{{ item.title }}</div>
            <div class="desc text-o">{{ item.desc }}</div>
          </div>
        </div>
        <a class="link" href="/productCenter">探索更多产品<a-icon type="right" :style="{ fontSize: '16px', marginLeft: '4px' }" /></a>
      </div>
    </div>
    <div class="solution-wrap">
      <div class="title">解决方案</div>
      <div class="solution-content">
        <SolutionLeft :data="solutionContent" />
        <div class="right-text">
          <ul class="solution-icon-list">
            <li class="solution-icon-item" :class="{ active: currentSolution === index }"
              @click="onSolutionItemClick(index)" v-for="(item, index) in solutionList" :key="index">
              <span class="iconfont icon" :style="{ fontSize: item.iconSize }" v-html="item.icon"></span>
              <span class="text">{{ item.name }}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="api-des-wrap">
      <div class="left">
        <img class="img" src="https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>" alt="" />
      </div>
      <div class="right">
        <div class="tip">专为开发人员设计</div>
        <div class="title">强大且易用的API</div>
        <div class="sub-title">
          我们来处理那些抽象的事物，让您的团队免去整合不同系统的麻烦，也不需要花费数月时间来集成支付功能。
        </div>
        <a class="link" href="/docs">查看文档 ></a>
        <div>
          <div class="desc-item" style="margin-right: 32px">
            <div class="desc-item-title">多种语言版本的SDK工具</div>
            <div class="desc-item-sub-title">
              我们准备了从 Java、Go、Python、PHP、.NET、NodeJs
              等多种语言的SDK，方便你的快速接入。
            </div>
            <a class="link" href="/docs/platform/sdk_guide/sdk-guide">查看 ></a>
          </div>
          <div class="desc-item">
            <div class="desc-item-title">便捷的密钥工具</div>
            <div class="desc-item-sub-title">
              可以方便你快速生成接入所需的密钥，并能安全下载并激活CFCA证书文件。
            </div>
            <a class="link" href="/docs/platform/developTools/keyTools">查看 ></a>
          </div>
        </div>
      </div>
    </div>
    <div class="api-detail" :style="{ '--width': varWidth }" id="earth-wrap">
      <div class="api-detail-content">
        <div class="tip">安全、合规、稳定、全面的交易服务</div>
        <div class="title">为企业提供强有力的支持 助力业务飞速拓展</div>
        <div>
          <div class="desc-item">
            <div class="desc-item-title">2.6亿+</div>
            <div class="desc-item-sub-title">
              每天API请求次数，峰值达1,000每秒
            </div>
          </div>
          <div class="desc-item">
            <div class="desc-item-title">2,000个+</div>
            <div class="desc-item-sub-title">API服务接口</div>
          </div>
          <div class="desc-item">
            <div class="desc-item-title">30个+</div>
            <div class="desc-item-sub-title">交易服务解决方案与产品服务</div>
          </div>
          <div class="desc-item">
            <div class="desc-item-title">99.9%+</div>
            <div class="desc-item-sub-title">交易服务可用性</div>
          </div>
        </div>
      </div>
      <div class="Globe js-globe"></div>
    </div>
    <div class="brand-wrap">
      <div class="brand-bc" :style="{ '--width': varWidth }"></div>
      <div class="content">
        <div class="title">超过 100+企业正在使用易宝开放平台</div>
        <ul class="brand-list-wrap">
          <li class="brand-item" v-for="item in 24" :key="item">
            <img :src="
                `https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>/${item}.png`
              " alt="" />
          </li>
        </ul>
        <div class="card-wrap">
          <div class="left">
            <img class="img" src="https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>" alt="" />
            <div class="text">
              <div class="text-title">准备好开始了？</div>
              <div class="text-desc">
                与我们销售取得联系，签约并入网，联调成功后，就可以收款了。我们也可以为您的公司量身制定最合适的套餐。
              </div>
              <a class="link-text" href="https://www.yeepay.com/customerService/businessCooperation">联系销售 ></a>
            </div>
          </div>
          <div class="right">
            <div class="item" style="margin-bottom: 16px">
              <img class="img" src="https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>" alt="" />
              <div class="text">
                <div class="text-title">开始你的集成</div>
                <div class="text-desc">
                  若你还未签约入网成功或正在入网中，你现在根据需要了解平台的API。待服务开通后，就可以开始联调与部署工作了。
                </div>
                <a class="link-text" href="/docs">全部API ></a>
              </div>
            </div>
            <div class="item">
              <img class="img" src="https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>" alt="" />
              <div class="text">
                <div class="text-title">成为平台伙伴</div>
                <div class="text-desc">
                  若你有足够的实力，想将你的服务集成到本平台，请留下你的联系信息与服务内容，我们收到信息后会及时与你联系。
                </div>
                <a class="link-text" href="https://www.yeepay.com/customerService/businessCooperation">成为伙伴 ></a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <PageFooter />
  </div>
</template>

<script>
import SolutionLeft from './components/SolutionLeft'
import Api from '@/api/product'
export default {
  components: {
    SolutionLeft,
  },
  data() {
    return {
      active: 0,
      currentSolution: 0,
      earthWrapTop: 1500,
      isLoadEarth: false,
      imgTopList: [],
      hotProducts: [],
    }
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile
    },
    varWidth() {
      return this.clientWidth < 1280 ? '640px' : this.clientWidth / 2 + 'px'
    },
    clientWidth() {
      return this.$store.state.clientWidth
    },
    scrollBarWidth() {
      return this.$store.state.scrollBarWidth
    },
    scrollTop() {
      return this.$store.state.scrollTop
    },
    scrollLeft() {
      return this.$store.state.scrollLeft
    },
    solutionList() {
      return this.$store.state.solutionList
    },
    solutionContent() {
      return this.solutionList[this.currentSolution].content
    },
    recommendAppList() {
      return this.$store.state.recommendAppList
    },
    recommendAppContent() {
      return this.recommendAppList[this.active].content
    },
  },
  watch: {
    scrollTop(newvalue) {
      this.loadEarth(newvalue)
    },
    scrollLeft(newvalue) {
      this.content.style.left = `-${newvalue}px`
    },
  },
  created() {
    if (this.isMobile) this.$router.replace('/mhome')
  },
  mounted() {
    this.init()
  },
  destroyed() {
    if (this.timer) {
      window.clearInterval(this.timer)
    }
  },
  methods: {
    goProduct(code) {
      window.location.href = `/docs/products/${code}/index.html`
    },
    onSolutionItemClick(index) {
      if (this.timer) {
        window.clearInterval(this.timer)
        this.timer = null
      }
      this.currentSolution = index
    },
    loadEarth(scrollTop) {
      if (this.isLoadEarth || scrollTop < this.earthWrapTop - 1000) {
        return
      }
      this.isLoadEarth = true
      import('../../utils/Globe').then(({ earth }) => {
        const a = new earth(document.querySelector('.js-globe'))
        a.load()
        a.play()
      })
    },
    init() {
      this.loadEarth(this.scrollTop)
      this.loadProduct()
      this.$store.dispatch('getProductMenu')
      this.earthWrapTop = document.querySelector('#earth-wrap').offsetTop
    },
    async loadProduct() {
      try {
        const docBasic = await Api.getDocBasic()
        const hotProduct = await Api.getHotProduct()
        this.hotProducts = hotProduct.map(code => {
          return {
            code,
            title: docBasic[`${code}.title`],
            desc: docBasic[`${code}.desc`],
            icon: docBasic[`${code}.icon`],
          }
        }).slice(0, 8)
      } catch (error) {
        console.log(error)
      }
    }
  },
}
</script>

<style lang="less" scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.4s;
}

.fade-enter,
.fade-leave-to

/* .fade-leave-active below version 2.1.8 */
  {
  opacity: 0;
}

.home-wrap {
  min-width: 1280px;
  width: 100%;
  overflow: hidden;

  .banner-wrap {
    .banner-box {
      max-width: 1920px;
      height: 680px;
      background-size: cover;
      background-image: url('../../assets/home-banner.jpg');
      background-position: center;
      background-repeat: no-repeat;
      position: relative;
      margin: 0 auto;
    }

    // background: url('https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>')
    // background: url('../../assets/images/home-banner.png')
    //   no-repeat center;
    width: 100%;
    background-color: #0F3168;

    .title {
      position: absolute;
      top: 240px;
      left: 50%;
      margin-left: -600px;
      height: 56px;
      font-size: 40px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.85);
      line-height: 56px;
      margin-bottom: 24px;
      z-index: 1;
    }

    .sub-title {
      position: absolute;
      top: 315px;
      left: 50%;
      margin-left: -600px;
      height: 22px;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.85);
      line-height: 22px;
      z-index: 1;
    }

    .sub-link {
      position: absolute;
      top: 380px;
      left: 50%;
      margin-left: -600px;
      z-index: 1;
      width: 120px;
      height: 32px;
      border-radius: 20px;
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 500;
      color: #fff;
      background-color: #52bf63;
      line-height: 32px;
      text-align: center;
      cursor: pointer;
    }

    .img {
      position: absolute;
      top: 0;
      left: 50%;
      margin-left: -200px;
      height: 100%;
      max-height: 800px;
      width: 877px;
      // background: url('https://img.yeepay.com/fe-resources/yop-docs/images/home/<USER>')
      //   no-repeat center;
      background-size: 877px 100%;
    }
  }

  .product-capability {
    height: 767px;
    background: rgba(0, 0, 0, 0.04);
    padding: 80px 0 72px;
    font-family: PingFangSC, PingFang SC;
    text-align: center;
    font-style: normal;

    .content {
      max-width: 1200px;
      margin: 0 auto;

      .title {
        font-weight: 500;
        font-size: 30px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 42px;
        margin-bottom: 60px;
      }

      .product-wrap {
        margin-bottom: 32px;
        margin-right: -16px;

        .product-item {
          width: 288px;
          height: 212px;
          background: #FFFFFF;
          border-radius: 16px;
          border: 1px solid #FFFFFF;
          margin-right: 16px;
          margin-bottom: 16px;
          padding: 40px 24px 0;
          display: inline-block;
          cursor: pointer;
          vertical-align: middle;
          &:hover {
            transform: translateY(-3px);
            transition: all 0.3s;
            box-shadow: 0px 4px 10px 0px rgba(0,0,0,0.08);
            border: 1px solid #FFFFFF;
          }
          .icon {
            width: 56px;
            height: 56px;
            border-radius: 12px;
            margin: 0 auto 24px;
          }

          .name {
            font-weight: 600;
            font-size: 18px;
            color: rgba(0, 0, 0, 0.8);
            line-height: 28px;
            margin: 0 auto 4px;
          }

          .desc {
            height: 20px;
            font-weight: 400;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
            line-height: 20px;
          }
        }
      }

      .link {
        font-weight: 500;
        font-size: 16px;
        line-height: 25px;
      }
    }
  }

  .solution-wrap {
    height: 683px;
    padding-top: 80px;
    background-color: #fff;

    .title {
      height: 42px;
      font-size: 30px;
      font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
      line-height: 42px;
      text-align: center;
      margin-bottom: 60px;
    }

    .solution-content {
      text-align: center;

      .right-text {
        display: inline-block;
        vertical-align: top;
        padding-top: 30px;

        .solution-icon-list {
          .solution-icon-item {
            height: 40px;
            line-height: 40px;
            padding-left: 13px;
            cursor: pointer;

            .icon {
              display: inline-block;
              margin-right: 9px;
              font-size: 12px;
            }

            .text {
              width: 50px;
              height: 17px;
              font-size: 12px;
              font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei',
                SimSun, sans-serif;
              font-weight: 400;
              color: rgba(0, 0, 0, 0.85);
              line-height: 17px;
              text-align: left;
              display: inline-block;
              vertical-align: middle;
            }
          }

          .active {
            width: 96px;
            height: 40px;
            background: #ffffff;
            box-shadow: 0px 2px 4px 0px #e6e6e6;

            .text,
            .icon {
              color: #52bf63;
            }
          }
        }
      }
    }
  }

  .api-des-wrap {
    padding-top: 120px;
    text-align: center;
    padding-bottom: 39px;
    background-color: #F7F7F7;
    .left {
      display: inline-block;
      vertical-align: top;
      margin-right: 124px;

      .img {
        height: 490px;
      }
    }

    .right {
      display: inline-block;
      vertical-align: top;
      text-align: left;

      .tip {
        height: 22px;
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
        line-height: 22px;
        margin-bottom: 24px;
      }

      .title {
        height: 42px;
        font-size: 30px;
        font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        line-height: 42px;
        margin-bottom: 24px;
        white-space: nowrap;
      }

      .sub-title {
        width: 520px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
        line-height: 28px;
        margin-bottom: 8px;
      }

      .link {
        display: block;
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 500;
        color: #52bf63;
        line-height: 20px;
        margin-bottom: 80px;
      }

      .desc-item {
        display: inline-block;
        vertical-align: top;
        padding-left: 6px;
        position: relative;

        &::before {
          content: '';
          width: 2px;
          height: 14px;
          background: #52bf63;
          border-radius: 1px;
          position: absolute;
          left: 0;
          top: 3px;
        }

        .desc-item-title {
          height: 20px;
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
            sans-serif;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.85);
          line-height: 20px;
          margin-bottom: 7px;
        }

        .desc-item-sub-title {
          width: 193px;
          height: 60px;
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei',
            SimSun, sans-serif;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.65);
          line-height: 20px;
          margin-bottom: 8px;
        }
      }
    }
  }

  .api-detail {
    height: 800px;
    background: #041832;
    position: relative;
    padding-top: 313px;
    transform: translateZ(0);

    .api-detail-content {
      max-width: 1280px;
      margin: 0 auto;
    }

    &::before {
      content: '';
      width: 0;
      height: 0;
      border-left: 50vw solid #F7F7F7;
      border-top: 60px solid #F7F7F7;
      border-bottom: 60px solid transparent;
      border-right: 50vw solid transparent;
      position: absolute;
      left: 0;
      top: 0;
      z-index: 10;
    }

    .tip {
      width: 256px;
      height: 22px;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.85);
      line-height: 22px;
      margin-bottom: 32px;
    }

    .title {
      width: 330px;
      height: 92px;
      font-size: 30px;
      font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.85);
      line-height: 42px;
      margin-bottom: 195px;
    }

    .desc-item {
      display: inline-block;
      vertical-align: top;
      padding-left: 15px;
      position: relative;
      z-index: 10;
      width: 260px;

      &::before {
        content: '';
        width: 3px;
        height: 30px;
        background: #52bf63;
        border-radius: 1px;
        position: absolute;
        left: 0;
        top: 7px;
      }

      .desc-item-title {
        height: 45px;
        font-size: 32px;
        font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 500;
        color: #ffffff;
        line-height: 45px;
        margin-bottom: 16px;
      }

      .desc-item-sub-title {
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.65);
        line-height: 20px;
      }
    }

    .Globe {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }
  }

  .brand-wrap {
    position: relative;
    background-color: #F7F7F7;
    padding-bottom: 120px;

    .brand-bc {
      position: absolute;
      top: 0;
      left: 0;
      width: 100vw;

      height: 342px;
      background-color: #fff;

      &::after {
        content: '';
        width: 0;
        height: 0;
        border-left: 50vw solid #fff;
        border-top: 60px solid #fff;
        border-bottom: 60px solid transparent;
        border-right: 50vw solid transparent;
        position: absolute;
        left: 0;
        bottom: -120px;
      }
    }

    .content {
      position: relative;
      z-index: 2;
      padding-top: 120px;
      text-align: center;
      margin: 0 auto;

      .title {
        height: 42px;
        font-size: 30px;
        font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        line-height: 42px;
        text-align: center;
        margin-bottom: 80px;
      }

      .brand-list-wrap {
        overflow: hidden;
        max-width: 1224px;
        text-align: center;
        margin-right: -24px;
        display: inline-block;
        margin-bottom: 100px;

        .brand-item {
          width: 180px;
          height: 64px;
          background: #ffffff;
          border-radius: 3px;
          float: left;
          margin: 0 24px 20px 0;

          img {
            width: 180px;
            height: 64px;
            background: #ffffff;
            border-radius: 3px;
          }
        }
      }

      .card-wrap {
        max-width: 1280px;
        margin: 0 auto;
        text-align: center;

        .left {
          display: inline-block;
          vertical-align: top;
          width: 640px;
          height: 338px;
          background: #ffffff;
          box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.1);
          border-radius: 6px;
          margin-right: 16px;
          padding: 73px 0 0 42px;

          .img {
            height: 170px;
            margin-right: 40px;
            vertical-align: top;
          }

          .text {
            display: inline-block;
            vertical-align: top;
            text-align: left;

            .text-title {
              height: 30px;
              font-size: 22px;
              font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei',
                SimSun, sans-serif;
              font-weight: 500;
              color: rgba(0, 0, 0, 0.85);
              line-height: 30px;
              margin-bottom: 16px;
            }

            .text-desc {
              width: 320px;
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei',
                SimSun, sans-serif;
              font-weight: 400;
              color: rgba(0, 0, 0, 0.85);
              line-height: 20px;
              margin-bottom: 32px;
            }

            .link-text {
              height: 20px;
              font-size: 14px;
              font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei',
                SimSun, sans-serif;
              font-weight: 500;
              color: #52bf63;
              line-height: 20px;
            }
          }
        }

        .right {
          display: inline-block;
          vertical-align: top;

          .item {
            width: 540px;
            height: 161px;
            background: #ffffff;
            box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.1);
            border-radius: 6px;
            line-height: 161px;
            padding-left: 40px;

            .img {
              height: 65px;
              margin-right: 32px;
            }

            .text {
              display: inline-block;
              text-align: left;
              vertical-align: middle;

              .text-title {
                height: 20px;
                font-size: 14px;
                font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei',
                  SimSun, sans-serif;
                font-weight: 500;
                color: rgba(0, 0, 0, 0.85);
                line-height: 20px;
                margin-bottom: 8px;
              }

              .text-desc {
                width: 320px;
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei',
                  SimSun, sans-serif;
                font-weight: 400;
                color: rgba(0, 0, 0, 0.85);
                line-height: 17px;
                margin-bottom: 8px;
              }

              .link-text {
                height: 20px;
                font-size: 14px;
                font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei',
                  SimSun, sans-serif;
                font-weight: 500;
                color: #52bf63;
                line-height: 20px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
