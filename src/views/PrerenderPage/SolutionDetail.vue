<template>
	<div>
		<div class="solution" v-if="solution && detail">
			<div class="header">
				<div class="header-content">
					<div class="title">{{ detail.title }}</div>
					<div class="desc" v-html="detail.desc"></div>
					<a
						class="sub-btn"
						type="primary"
            target="_blank"
						href="https://www.yeepay.com/customerService/businessCooperation"
					>
						立即咨询
					</a>
				</div>
				<img
					class="bg"
					:src="`https://img.yeepay.com/fe-resources/yop-docs/solution/detail/${solution}/logo.png`"
					alt=""
				/>
			</div>
			<div class="tabs-wrap">
				<ul class="tabs-list">
					<li
						class="tabs-item"
						v-for="item in tabsList"
						:key="item.value"
						@click="tabsClick(item.value)"
						:class="activeTab === item.value ? 'active' : ''"
					>
						{{ item.name }}
					</li>
				</ul>
			</div>
			<div class="content">
				<div class="pain-point-wrap" id="pain">
					<div class="pain-point">
						<div class="title">目前行业痛点</div>
						<ul class="pain-list">
							<li class="pain-item" v-for="(item, index) in detail.painList" :key="item.value">
								<div class="img">
									<img
										:src="`https://img.yeepay.com/fe-resources/yop-docs/solution/detail/${solution}/pain${
											index + 1
										}.png`"
										alt=""
									/>
								</div>
								<div class="name">{{ item.title }}</div>
								<div class="desc">{{ item.desc }}</div>
							</li>
						</ul>
					</div>
				</div>
				<div class="advantage-wrap" id="advantage">
					<div class="advantage">
						<div class="advantage-title">我们的方案优势</div>
						<div class="lr-item">
							<div class="left">
								<div>
									<div class="title">{{ detail.advantage[0].title }}</div>
									<template v-for="text in handlerDesc(detail.advantage[0].desc)">
										<div class="desc dot" :key="text">{{ text }}</div>
									</template>
								</div>
							</div>
							<div class="right">
								<img
									class="img"
									:src="`https://img.yeepay.com/fe-resources/yop-docs/solution/detail/${solution}/advantage1.png`"
									alt=""
								/>
							</div>
						</div>
						<div class="tb-wrap">
							<div class="tb-item">
								<div class="top">
									<div class="title">{{ detail.advantage[1].title }}</div>
									<template v-for="text in handlerDesc(detail.advantage[1].desc)">
										<div class="desc dot" :key="text">{{ text }}</div>
									</template>
								</div>
								<div class="bottom">
									<img
										class="img"
										:src="`https://img.yeepay.com/fe-resources/yop-docs/solution/detail/${solution}/advantage2.png`"
										alt=""
									/>
								</div>
							</div>
							<div class="tb-item">
								<div class="top">
									<div class="title">{{ detail.advantage[2].title }}</div>
									<template v-for="text in handlerDesc(detail.advantage[2].desc)">
										<div class="desc dot" :key="text">{{ text }}</div>
									</template>
								</div>
								<div class="bottom">
									<img
										class="img"
										:src="`https://img.yeepay.com/fe-resources/yop-docs/solution/detail/${solution}/advantage3.png`"
										alt=""
									/>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="product-wrap" id="linkProduct">
					<div class="product">
						<div class="title">关联产品</div>
						<ul class="list">
							<li
								class="item"
								v-for="code in detail.productCodes"
								:key="code"
								@click="goProduct(code)"
							>
								<img class="icon" :src="getIcon(code)" />
								<div class="right">
									<div class="name text-o">{{ docBasic[`${code}.title`] }}</div>
									<div class="desc text-o">{{ docBasic[`${code}.desc`] }}</div>
								</div>
							</li>
						</ul>
					</div>
				</div>
			</div>
			<PageFooter style="background-color: #fff" :mobile="isMobile" />
		</div>
	</div>
</template>

<script>
import Api from '@/api/product'
export default {
	data() {
		return {
			docBasic: {},
			activeTab: 'pain',
			tabsList: [
				{
					name: '行业痛点',
					value: 'pain',
				},
				{
					name: '方案优势',
					value: 'advantage',
				},
				{
					name: '关联产品',
					value: 'linkProduct',
				},
			],
			solutionData: {
				hl: {
					title: '航旅行业解决方案',
					desc: '根据航旅行业特点，构建航旅数字化中枢，<br>优化资源配置与全流程服务体验',
					painList: [
						{
							title: '支付效率与体验不足',
							desc: '传统流程复杂，购票、退改签等环节需多步骤操作，等待时间长、体验差；与航司内部业务系统对接困难，影响整体运营效率',
						},
						{
							title: '资金周转压力大',
							desc: '航司覆盖代理商、OTA、用户等多方资金结算，传统支付方式回款周期长，现金流压力大，对航司和高频次航线运营影响显著',
						},
						{
							title: '跨境支付成本与风控难题',
							desc: '国际航线涉及多币种结算，合规审核繁琐等问题推高成本；线上支付面临欺诈、数据泄露等风险，需兼顾安全与便捷性',
						},
					],
					advantage: [
						{
							title: '全场景支付提效',
							desc: '提供聚合支付接口（支持银行卡、数字钱包等），覆盖购票、退票、增值服务等全流程，简化用户操作；通过标准化API与航司系统快速对接，提升订单处理与资金对账效率',
						},
						{
							title: '实时清算加速资金流转',
							desc: '采用T+N实时清算模式，缩短航司与代理商、平台间的资金回笼周期，缓解现金流压力；支持分账功能，灵活处理多方分润需求，降低财务对账复杂度',
						},
						{
							title: '全球化支付与智能风控',
							desc: '全球账户体系，解决代理采购境外机票；基于准确高效风控系统，实时拦截欺诈交易，保障业务安全稳定允许',
						},
					],
					productCodes: [
						'account-payment',
						'onekey-payment',
						'merchant-scan',
						'user-scan',
						'qqb',
						'bill',
						'settlement',
					],
				},
				xf: {
					title: '消费金融解决方案',
					desc: '根据消费金融行业特点，构建智能消费金融中枢，<br>驱动精准风控与场景化服务升级',
					painList: [
						{
							title: '合规作业问题',
							desc: '  14号合规改造背景，本息回收代扣指令需由资方发起，如何解决资方无扣款协议号的问题，如何应对助贷平台和资方协作模式变化',
						},
						{
							title: '复杂场景变化',
							desc: '助贷+资方模式变化后，平台和资金指令变化带来的交易稳定性、系统复杂度及回款率变化，如何解决这些问题',
						},
						{
							title: '灵活的资金处理',
							desc: '用户提前还款、线下还款带来的挑战，手续费第三方承担，实时清分',
						},
					],
					advantage: [
						{
							title: '放款还款清分一体的服务',
							desc: '支持资金对公快捷、ACS充值等多种方式，资金方7*24小时实时放款还款支持灵活的空中清分',
						},
						{
							title: '产品方案合规灵活',
							desc: '提供多样化14号文合规解决方案，存量用户迁移方案，实现资方和助贷平台扣款协议号共享，灵活的资金处理方案',
						},
						{
							title: '增值服务',
							desc: '基于用户在易宝的全量、全类型历史交易，叠加各种用户行为特征，辅助客户在存量客群运营场景进行促活、提额、二次营销等',
						},
					],
					productCodes: [
						'bindcard-payment',
						'agreement-payment',
						'merchant-scan',
						'user-scan',
						'share',
						'bill',
						'settlement',
					],
				},
				zw: {
					title: '政务行业解决方案',
					desc: '打造政务资金数字化治理中枢，助力数字政务信息化建设',
					painList: [
						{
							title: '业务复杂度提升',
							desc: '多层级财政分账、跨区域考试规模扩大，传统人工模式效率低下',
						},
						{
							title: '效率瓶颈',
							desc: '高并发缴费场景系统崩溃、人工对账错误率高、退款时效差',
						},
						{
							title: '合规压力',
							desc: '满足“收支两条线”、电子票据管理等财政政策要求',
						},
					],
					advantage: [
						{
							title: '全面的技术支持与服务',
							desc: '提供专业的缴费技术，包括防掉单、秒补单、智能监控等，确保缴费过程的稳定性和安全性<br>VIP大客户服务，包括7*24小时客服支持和1对1专属商务服务，以及专属秒级响应群，确保服务的及时性和专业性',
						},
						{
							title: '强大的系统集成与定制能力',
							desc: '多年系统磨合，与主流系统报名深度集成，如鸥玛、卓帆、诺码信等，提供智能分账（中央、省、地市）、非税缴款/电子票据等能力<br>直连20余省财政系统，支持汇缴、直缴模式',
						},
						{
							title: '灵活的退费政策与合规性',
							desc: '提供全额退费、部分金额退费、单笔退费、批量退费、超长期退费等多种退费方式，保证退款时效的前提下，符合财政“收支两条线”的政策要求',
						},
					],
					productCodes: ['share'],
				},
				ls: {
					title: '零售行业解决方案',
					desc: '根据零售行业特点，建立全渠道智能系统，实现精准营销与供应链效能跃升',
					painList: [
						{
							title: '线上线下融合的复杂性',
							desc: '零售商实现线上线下业务的无缝衔接时，面临技术兼容性、数据同步和顾客体验一致性等问题',
						},
						{
							title: '多渠道收款的管理难题',
							desc: '随着支付方式的多样化，零售商必须应对多渠道收款带来的管理挑战',
						},
						{
							title: '转型与成本的双重压力',
							desc: '在零售行业的快速变化中，商户面临转型的迫切需求；与在控制成本的同时，还要推动业务的持续创新',
						},
					],
					advantage: [
						{
							title: '聚合支付场景：实现线上线下统一支付',
							desc: '支持商家扫码（B扫C）和用户扫码（C扫B）主流支付方式',
						},
						{
							title: '结算灵活',
							desc: '7*24资金处理，提升平台资金处理效率，降低财务人工成本',
						},
						{
							title: '灵活分账与结算',
							desc: '分账账期、分账金额、分账接收方、结算周期、结算账户由平台灵灵活 定义，统一交易对账和资金结算减少财务压力和运营成本',
						},
					],
					productCodes: [
						'merchant-scan',
						'user-scan',
						'merchant-netin',
						'share',
						'bill',
						'settlement',
						'multichannel',
					],
				},
				cy: {
					title: '餐饮行业解决方案',
					desc: '根据餐饮行业特点，聚焦行业特性打造智慧运营体系，提升消费体验与效能',
					painList: [
						{
							title: '支付场景复杂，管理难度高',
							desc: '门店分布广、数量多导致进件效率低且易出错，同时线上线下多场景支付能力接入和管理繁琐',
						},
						{
							title: '结算与对账效率低，财务压力大',
							desc: '不同门店需按通道或门店维度结算，不够灵活，且多通道对账单独立，需人工整合，工作量大且易出错',
						},
						{
							title: '支营销与技术对接成本高',
							desc: '微信、支付宝等通道的营销活动效率低且响应慢，同时分别接入不同通道耗费大量研发成本',
						},
					],
					advantage: [
						{
							title: '高效入网 场景全覆盖',
							desc: '提供多种快速进件工具：支持APP/PC、API多种进件方式<br>全场景支付：整合线下与线上支付场景，简化接入流程，提升管理效率',
						},
						{
							title: '结算灵活',
							desc: '灵活结算规则：支持多种结算方式，如定时结算、分时结算；按照商户号、终端号分开结算；间连微信、支付宝、云闪付合并结算、满足多样化需求。<br>多通道账单整合：自动整合微信、支付宝、银联等对账单，减少人工操作，降低财务压力',
						},
						{
							title: '低成本营销与技术对接',
							desc: '统一营销管理：支持营销活动统一运营，提升效率与响应速度。<br>标准化接口与一站式解决方案：降低技术对接难度与研发成本，助力企业快速实现数字化支付转型',
						},
					],
					productCodes: ['merchant-scan', 'user-scan', 'merchant-netin', 'bill', 'settlement'],
				},
				yjj: {
					title: '夜经济行业解决方案',
					desc: '根据夜经济行业特点，构建夜间经济智能生态，驱动业态创新与长效价值增长',
					painList: [
						{
							title: '跨天经营对账难，财务成本高',
							desc: '营业时间跨越凌晨，交易账单需按天拆分，人工对账复杂且易出错',
						},
						{
							title: '结算周期固定，资金流动性差',
							desc: '传统结算周期无法根据商户实际营业时间灵活调整，资金到账时间不匹配',
						},
						{
							title: '收银效率低，夜间经营特殊性未满足',
							desc: '高峰期易拥堵，用户等待时间长传统支付解决方案无法适配夜间营业场景',
						},
					],
					advantage: [
						{
							title: '定时结算，资金到账更灵活',
							desc: '支持微信、支付宝、银行卡、云闪付等多种支付方式，满足用户多样化支付需求<br>线上线下收款统一归集，资金实时到账，减少对账难度。支付一体化解决方案',
						},
						{
							title: '结算灵活',
							desc: '商户可根据营业时间，自由设置结算周期（如每日凌晨3点结算）营业结束后，资金即时到账，无需等待，资金流动性更强',
						},
						{
							title: '用户自助支付，提升收银效率',
							desc: '用户自助支付，减少排队等待时间，优化消费体验',
						},
					],
					productCodes: ['merchant-scan', 'user-scan', 'merchant-netin', 'bill', 'settlement'],
				},
				ny: {
					title: '能源行业解决方案',
					desc: '根据能源行业特点，搭建能源数字化中枢，实现绿色转型与能效协同优化',
					painList: [
						{
							title: '排队时间长，收款效率低',
							desc: '传统收银方式效率低，高峰期易造成拥堵，用户体验差',
						},
						{
							title: '对账工作量大',
							desc: '人工对账复杂，尤其是油品与非油品交易分开结算，增加财务工作量',
						},
						{
							title: '运营管理成本高，资金收益低',
							desc: '人工管理成本高，资金结算周期不灵活，影响资金流动性',
						},
					],
					advantage: [
						{
							title: '全场景智慧收款，提升效率',
							desc: '支持多种支付方式（微信、支付宝、银行卡等），用户手机便捷支付，减少排队拥堵，提升收款效率',
						},
						{
							title: '智能对账，降低财务压力',
							desc: '自动同步订单，油品与非油品交易合单支付，资金自动结算至不同账户，简化对账流程，降低财务工作量',
						},
						{
							title: '灵活结算，提高资金流动性',
							desc: '支持24小时营业与灵活交班，每班次资金实时到账，降低对账压力，提高资金周转效率',
						},
					],
					productCodes: [
						'merchant-scan',
						'user-scan',
						'merchant-netin',
						'bill',
						'settlement',
						'share',
					],
				},
				ym: {
					title: '医美行业解决方案',
					desc: '根据医美行业特点，构建智能医美服务闭环，保障医疗质量与客户价值双提升',
					painList: [
						{
							title: '分期需求高',
							desc: '医美项目单价高，用户分期支付需求强烈',
						},
						{
							title: '财务对账易出错，人工对账压力大',
							desc: '支付数据与财务系统独立，人工对账繁琐，易出错',
						},
						{
							title: '渠道商依赖度高，利润分配需求复杂',
							desc: '依赖渠道商拓展客户，利润分配复杂，资金结算效率低',
						},
					],
					advantage: [
						{
							title: '聚合支付与智能终端，提升收款效率',
							desc: '支持线上线下多渠道收款，助力多场景营销获客<br>提升收款效率，降低拓客成本，满足多样化支付需求',
						},
						{
							title: '智能分账，解决渠道结算难题',
							desc: '智能分账功能，解决合作渠道资金结算难题<br>简化利润分配流程，提升资金结算效率，降低渠道依赖',
						},
						{
							title: '统一支付数据与财务对账，降低人工压力',
							desc: '统一整合支付数据及财务报表，提供自动化对账工具<br>减少人工对账错误，提升财务对账效率，降低运营成本',
						},
					],
					productCodes: [
						'merchant-scan',
						'user-scan',
						'merchant-netin',
						'bill',
						'settlement',
						'share',
					],
				},
			},
			solution: '',
		}
	},
	computed: {
		isMobile() {
			return this.$store.state.isMobile
		},
		list() {
			return this.$store.state.productMenuList
		},
		detail() {
			return this.solutionData[this.solution]
		},
	},
	async created() {
		this.solution = this.$route.query.solution
		window.document.title = this.detail.title
		await this.getDocBasic()
		await this.$store.dispatch('getProductMenu')
	},
	methods: {
		handlerDesc(desc) {
			return desc.split('<br>')
		},
		tabsClick(tab) {
			this.activeTab = tab
			document.documentElement.scrollTop =
				document.querySelector(`#${tab}`).getBoundingClientRect().top +
				document.documentElement.scrollTop -
				120
		},
    goProduct(code) {
      window.open(`${window.location.origin}/docs/products/${code}/index.html`)
		},
		catalogueClick(code) {
			this.activeCode = code
		},
		getDocBasic() {
			return Api.getDocBasic().then((res) => {
				this.docBasic = res
			})
		},
		handleClick(e) {
			e.preventDefault()
		},
		anchorChange(anchor) {
			this.currentAnchor = anchor
		},
		getIcon(code) {
			const icon = this.docBasic[`${code}.icon`]
			if (!icon) return ''
			return `${window.location.origin}/${icon}`
		},
	},
}
</script>

<style lang="less" scoped>
.solution {
	font-family: PingFangSC, PingFang SC;
	min-width: 1425px;
	.header {
		height: 346px;
		padding: 80px 0 0 120px;
		position: relative;
		background: rgba(46, 46, 46);
		.header-content {
			max-width: 1425px;
			margin: 0 auto;
			position: relative;
			z-index: 22;
			.title {
				font-size: 40px;
				font-weight: 600;
				color: #ffffff;
				margin-bottom: 16px;
			}
			.desc {
				width: 360px;
				font-size: 16px;
				line-height: 25px;
				color: #ffffff;
				margin-bottom: 24px;
			}
			.sub-btn {
				display: inline-block;
				width: 120px;
				height: 40px;
				text-align: center;
				line-height: 40px;
				color: #fff;
				font-size: 14px;
				background: #52bf63;
				border-radius: 20px;
				&:hover {
					background: #78cc82;
				}
			}
		}
		.bg {
			position: absolute;
			top: 0;
			left: 50%;
			transform: translateX(-50%);
			height: 100%;
		}
	}
	.tabs-wrap {
		background: #ffffff;
		box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.04);
		position: sticky;
		top: 64px;
		z-index: 99;
		.tabs-list {
			margin: 0 auto;
			display: flex;
			justify-content: center;
			.tabs-item {
				display: inline-block;
				width: 64px;
				height: 64px;
				font-size: 16px;
				line-height: 64px;
				color: rgba(0, 0, 0, 0.88);
				margin-right: 40px;
				cursor: pointer;
				&:last-child {
					margin-right: 0;
				}
				&:hover {
					color: #52bf63;
				}
			}
			.active {
				font-weight: 500;
				color: #52bf63;
				border-bottom: 2px solid #52bf63;
			}
		}
	}
	.content {
		min-height: calc(100vh - 65px - 360px - 392px);
		.pain-point-wrap {
			padding: 80px 0 100px;
			background-color: rgba(0, 0, 0, 0.04);
			.pain-point {
				max-width: 1200px;
				margin: 0 auto;
				.title {
					font-size: 30px;
					font-weight: 500;
					line-height: 42px;
					text-align: center;
					color: rgba(0, 0, 0, 0.85);
					margin-bottom: 60px;
				}
				.pain-list {
					display: flex;
					.pain-item {
						display: inline-block;
						width: 388px;
						border-radius: 16px;
						background: #ffffff;
						margin-right: 18px;
						padding: 32px 32px;
						text-align: center;
						vertical-align: middle;
						&:last-child {
							margin: 0;
						}
						.img {
							width: 64px;
							height: 64px;
							margin: 0 auto 15px;
							img {
								width: 100%;
								height: 100%;
							}
						}
						.name {
							font-size: 20px;
							font-weight: 500;
							line-height: 28px;
							margin-bottom: 8px;
						}
						.desc {
							font-size: 14px;
							color: rgba(0, 0, 0, 0.45);
						}
					}
				}
			}
		}
		.advantage-wrap {
			padding: 80px 0 100px;
			.advantage {
				max-width: 1200px;
				margin: 0 auto;
				.advantage-title {
					font-size: 30px;
					font-weight: 500;
					line-height: 42px;
					text-align: center;
					color: rgba(0, 0, 0, 0.85);
					margin-bottom: 60px;
				}
				.lr-item {
					width: 1200px;
					height: 230px;
					border-radius: 16px;
					display: flex;
					background: linear-gradient(0deg, rgba(0, 0, 0, 0.04), rgba(0, 0, 0, 0.04)), #ffffff;
					box-sizing: border-box;
					border: 1px solid #f5f5f5;
					margin-bottom: 24px;
					.left {
						width: 50%;
						text-align: left;
						display: flex;
						align-items: center;
						padding-left: 56px;
						.title {
							font-size: 24px;
							font-weight: 600;
							line-height: 34px;
							color: rgba(0, 0, 0, 0.85);
							margin-bottom: 13px;
						}
						.desc {
							font-family: PingFang SC;
							font-size: 14px;
							font-weight: normal;
							line-height: 24px;
							letter-spacing: 0px;
							color: rgba(0, 0, 0, 0.85);
						}
					}
					.right {
						display: flex;
						flex: 1;
						align-items: center;
						justify-content: center;
						.img {
							width: 100%;
						}
					}
				}
				.tb-wrap {
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-bottom: 24px;
				}
				.tb-item {
					border-radius: 16px;
					background: linear-gradient(0deg, rgba(0, 0, 0, 0.04), rgba(0, 0, 0, 0.04)), #ffffff;
					box-sizing: border-box;
					border: 1px solid #f5f5f5;
					width: 588px;
					height: 476px;
					.top {
						height: 50%;
						padding: 48px 68px 0 56px;
						.title {
							font-size: 24px;
							font-weight: 600;
							color: rgba(0, 0, 0, 0.85);
							margin-bottom: 15px;
						}
						.desc {
							font-size: 14px;
							line-height: 24px;
							color: rgba(0, 0, 0, 0.85);
						}
					}
					.bottom {
						.img {
							width: 100%;
						}
					}
				}
			}
		}
		.product-wrap {
			padding: 80px 0 100px;
			background-color: rgba(0, 0, 0, 0.04);
			.product {
				max-width: 1200px;
				margin: 0 auto;
				.title {
					font-size: 30px;
					font-weight: 500;
					line-height: 42px;
					text-align: center;
					color: rgba(0, 0, 0, 0.85);
					margin-bottom: 60px;
				}
				.list {
					margin-right: -16px;
					margin-bottom: -16px;
					display: flex;
					justify-content: center;
					flex-wrap: wrap;
					.item {
						width: 335px;
						height: 100px;
						background: #ffffff;
						border-radius: 16px;
						border: 1px solid #ffffff;
						margin-right: 16px;
						margin-bottom: 16px;
						display: inline-flex;
						justify-content: center;
						align-items: center;
						padding: 24px;
						cursor: pointer;
						&:hover {
							transform: translateY(-3px);
							transition: all 0.3s;
							box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
						}
						.icon {
							width: 52px;
							height: 52px;
							border-radius: 12px;
							margin-right: 16px;
						}

						.right {
							flex: 1;
							max-width: 216px;
							.name {
								font-weight: 600;
								font-size: 18px;
								color: rgba(0, 0, 0, 0.8);
								line-height: 24px;
								margin-bottom: 8px;
							}

							.desc {
								height: 20px;
								font-weight: 400;
								font-size: 14px;
								color: rgba(0, 0, 0, 0.45);
								line-height: 20px;
							}
						}
					}
				}
			}
		}
	}
	.dot {
		position: relative;
		padding-left: 10px;
		&::before {
			content: '.';
			font-size: 14px;
			color: rgba(0, 0, 0, 0.85);
			text-align: center;
			line-height: 14px;
			position: absolute;
			left: 0;
			top: -2px;
			transform: scale(1.8);
		}
	}
}
</style>
