<template>
	<div>
		<div class="solution">
			<div class="header">
				<div class="header-content">
					<div class="left">
						<div class="title">易宝解决方案</div>
						<div class="desc">
							根据不同行业特点，提供多场景智慧解决方案，精准对接行业需求，助力企业高效增长
						</div>
						<a
							class="sub-btn"
							type="primary"
							target="_blank"
							href="https://www.yeepay.com/customerService/businessCooperation"
						>
							立即咨询
						</a>
					</div>
				</div>
				<img class="bg" src="../../assets/solution.png" alt="" />
			</div>
			<div class="content">
				<div class="profession-wrap">
					<div class="profession-content">
						<div class="title">行业解决方案，助力企业高效增长</div>
						<ul class="solution-list">
							<li
								class="solution-item"
								v-for="item in solutionData.profession"
								:key="item.title"
								@click="goDetail(item.key)"
							>
								<div class="img">
									<img :src="item.imgUrl" alt="" />
								</div>
								<div class="name">{{ item.title }}</div>
								<div class="desc">{{ item.desc }}</div>
							</li>
						</ul>
					</div>
				</div>
				<div class="standard-wrap">
					<div class="standard-content" style="padding-bottom: 64px">
						<div class="title">标准解决方案，助力商家标准接入</div>
						<ul class="solution-list">
							<li
								class="solution-item"
								v-for="item in solutionData.standard"
								:key="item.title"
								@click="goUrl(item.contentUri)"
							>
								<div class="img">
									<img :src="item.imgUrl" alt="" />
								</div>
								<div class="name" style="text-align: center">{{ item.title }}</div>
								<div class="desc" style="text-align: center; height: 42px">{{ item.desc }}</div>
							</li>
						</ul>
					</div>
				</div>
				<div class="consult">
					<div class="consult-content">
						<div class="title">咨询流程</div>
						<ul class="consult-list">
							<li class="consult-item" v-for="(item, index) in consultList" :key="item.title">
								<div class="consult-header">
									<div class="num">0{{ index + 1 }}</div>
									<img v-if="index !== 0" class="icon" src="../../assets/zixun.png" alt="" />
								</div>
								<div class="consult-title">{{ item.title }}</div>
								<div class="consult-desc">{{ item.desc }}</div>
							</li>
						</ul>
						<div class="understand">进一步了解</div>
						<div class="requirement">
							您的<span style="color: linear-gradient(256deg, #3b5fff 1%, #52bf63 98%)"
								>解决方案</span
							>需求
						</div>
						<a
							class="sub-btn"
							type="primary"
              target="_blank"
							href="https://www.yeepay.com/customerService/businessCooperation"
						>
							立即咨询
						</a>
					</div>
				</div>
			</div>
			<PageFooter style="background-color: #fff" :mobile="isMobile" />
		</div>
	</div>
</template>

<script>
export default {
	data() {
		return {
			docBasic: {},
			currentAnchor: '',
			solutionData: {
				profession: [
					{
						title: '零售行业',
						key: 'ls',
						desc: '针对零售场景量身打造解决方案',
						imgUrl: 'https://img.yeepay.com/fe-resources/yop-docs/solution/hyfa/ls.png',
					},
					{
						title: '餐饮行业',
						key: 'cy',
						desc: '针对餐饮行业一站式解决支付难题',
						imgUrl: 'https://img.yeepay.com/fe-resources/yop-docs/solution/hyfa/cy.png',
					},
					{
						title: '夜经济行业',
						key: 'yjj',
						desc: '灵活结算周期，夜间经营得心应手',
						imgUrl: 'https://img.yeepay.com/fe-resources/yop-docs/solution/hyfa/yjj.png',
					},
					{
						title: '航旅行业',
						key: 'hl',
						desc: '提供航旅行业全场景全球化支付方案',
						imgUrl: 'https://img.yeepay.com/fe-resources/yop-docs/solution/hyfa/hl.png',
					},
					{
						title: '能源行业',
						key: 'ny',
						desc: '多场景收银，提高收银效率',
						imgUrl: 'https://img.yeepay.com/fe-resources/yop-docs/solution/hyfa/ny.png',
					},
					{
						title: '医美行业',
						key: 'ym',
						desc: '面向医美提供聚合支付、资金管理',
						imgUrl: 'https://img.yeepay.com/fe-resources/yop-docs/solution/hyfa/ym.png',
					},
					{
						title: '政务行业',
						key: 'zw',
						desc: '基于政务行业，构建智慧政务服务平台',
						imgUrl: 'https://img.yeepay.com/fe-resources/yop-docs/solution/hyfa/zw.png',
					},
					{
						title: '消费金融行业',
						key: 'xf',
						desc: '场景化服务无缝升级，消费金融新体验',
						imgUrl: 'https://img.yeepay.com/fe-resources/yop-docs/solution/hyfa/xf.png',
					},
				],
				standard: [
					{
						title: '标准商户收付款方案',
						desc: '单一主体商户提供的，包括多场景收单和付款的综合解决方案',
						imgUrl: 'https://img.yeepay.com/fe-resources/yop-docs/solution/bzjjfa/sfk.png',
						contentUri: '/docs/products/bzshsfk/index.html',
					},
					{
						title: '平台商收付款方案',
						desc: '平台商户业务场景，提供支付/分账/结算等服务，帮助平台商快速展业',
						imgUrl: 'https://img.yeepay.com/fe-resources/yop-docs/solution/bzjjfa/pts.png',
						contentUri: '/docs/products/ptssfk/index.html',
					},
					{
						title: '服务商解决方案',
						desc: '行业SaaS服务商提供支付/结算/分账/付款综合解决方案',
						imgUrl: 'https://img.yeepay.com/fe-resources/yop-docs/solution/bzjjfa/fws.png',
						contentUri: '/docs/products/fwssfk/index.html',
					},
					{
						title: '代理商解决方案',
						desc: '为线下代理提供的综合解决方案，全面整合线上线下全渠道收单',
						imgUrl: 'https://img.yeepay.com/fe-resources/yop-docs/solution/bzjjfa/dls.png',
						contentUri: '/docs/products/dlssfk/index.html',
					},
				],
			},
			consultList: [
				{
					title: '需求咨询',
					desc: '提供相关需求，描述需求背景',
				},
				{
					title: '一对一服务',
					desc: '根据需求专业顾问一对一服务',
				},
				{
					title: '产品演示',
					desc: '意向产品演示及详细介绍',
				},
				{
					title: '方案定制',
					desc: '企业专属定制化解决方案',
				},
			],
		}
	},
	created() {},
	computed: {
		isMobile() {
			return this.$store.state.isMobile
		},
	},
	methods: {
		goDetail(solution) {
			this.$router.push({
				path: '/solution/detail',
				query: { solution },
			})
		},
		goUrl(url) {
			this.$router.push(url)
		},
	},
}
</script>

<style lang="less" scoped>
.solution {
	background-color: rgba(0, 0, 0, 0.04);
	font-family: PingFangSC, PingFang SC;
	min-width: 1425px;
	.header {
		height: 420px;
		background: #21273c;
		position: relative;
		.bg {
			position: absolute;
			top: 0;
			left: 50%;
			transform: translateX(-50%);
			height: 420px;
		}
		.header-content {
			max-width: 1200px;
			margin: 0 auto;
			position: relative;
			.left {
				padding-top: 120px;
				position: relative;
				z-index: 1;
			}

			.title {
				font-weight: 600;
				font-size: 40px;
				color: #ffffff;
				line-height: 56px;
				margin-bottom: 16px;
			}

			.desc {
				font-weight: 400;
				font-size: 16px;
				color: #ffffff;
				line-height: 28px;
				margin-bottom: 24px;
			}
			.sub-btn {
				width: 120px;
				height: 40px;
				border-radius: 20px;
			}
		}
	}

	.content {
		min-height: calc(100vh - 65px - 360px - 392px);
		.profession-wrap,
		.standard-wrap {
			.profession-content,
			.standard-content {
				max-width: 1200px;
				margin: 0 auto;
				padding: 80px 0;
				.title {
					font-size: 30px;
					font-weight: 500;
					line-height: 42px;
					text-align: center;
					color: rgba(0, 0, 0, 0.85);
					margin-bottom: 60px;
				}
				.solution-list {
					margin-right: -16px;
					.solution-item {
						display: inline-block;
						width: 288px;
						border-radius: 16px;
						background: #ffffff;
						box-sizing: border-box;
						margin: 0 16px 16px 0;
						cursor: pointer;
						vertical-align: middle;
						padding-bottom: 24px;
						&:hover {
							transform: translateY(-3px);
							transition: all 0.3s;
							box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
						}
						.img {
							height: 116px;
							width: 288px;
							margin-bottom: 24px;
							img {
								width: 288px;
								height: 100%;
							}
						}
						.name {
							font-size: 18px;
							font-weight: 600;
							line-height: 28px;
							margin-bottom: 4px;
							padding: 0 24px;
						}
						.desc {
							font-size: 14px;
							color: rgba(0, 0, 0, 0.45);
							padding: 0 24px;
						}
					}
				}
			}
		}
		.profession-wrap {
			background: linear-gradient(0deg, rgba(0, 0, 0, 0.04), rgba(0, 0, 0, 0.04)), #ffffff;
		}
		.standard-wrap {
			background: linear-gradient(0deg, #f5f5f5 0%, #ffffff 97%), #ffffff;
		}
		.consult {
			height: 645px;
			background: linear-gradient(0deg, #f5f5f5 0%, #ffffff 97%),
				linear-gradient(0deg, rgba(0, 0, 0, 0.04), rgba(0, 0, 0, 0.04)), #ffffff;
			.consult-content {
				max-width: 1200px;
				margin: 0 auto;
				padding: 80px 0;
				height: 100%;
				.title {
					font-size: 30px;
					font-weight: 500;
					line-height: 42px;
					text-align: center;
					color: rgba(0, 0, 0, 0.85);
					margin-bottom: 60px;
				}
				.consult-list {
					margin-bottom: 88px;
					.consult-item {
						display: inline-block;
						width: 300px;
						padding-left: 68px;
						.consult-header {
							display: flex;
							align-items: center;
							justify-content: space-between;
							height: 55px;
							padding-right: 24px;
							margin-bottom: 8px;
							position: relative;
							.num {
								font-size: 32px;
								font-weight: bold;
								color: rgba(0, 0, 0, 0.85);
							}
							.icon {
								width: 21px;
								height: 24px;
								position: absolute;
								top: 15px;
								left: -50px;
							}
						}
						.consult-title {
							font-size: 20px;
							font-weight: 500;
							line-height: 20px;
							color: rgba(0, 0, 0, 0.85);
							margin-bottom: 8px;
						}
						.consult-desc {
							font-size: 14px;
							font-weight: normal;
							line-height: 20px;
							color: rgba(0, 0, 0, 0.66);
						}
					}
				}
				.understand,
				.requirement {
					font-size: 40px;
					font-weight: 600;
					line-height: 56px;
					text-align: center;
					color: rgba(0, 0, 0, 0.65);
					margin-bottom: 8px;
				}
				.requirement {
					margin-bottom: 24px;
				}
				.sub-btn {
					width: 120px;
					height: 40px;
					border-radius: 20px;
					margin: 0 auto;
					display: block;
				}
			}
		}
	}
	.sub-btn {
		display: inline-block;
		width: 120px;
		height: 40px;
		text-align: center;
		line-height: 40px;
		color: #fff;
		font-size: 14px;
		background: #52bf63;
		&:hover {
			background: #78cc82;
		}
	}
}
</style>
