<template>
  <div class="solution-com-wrap">
    <img class="img" :src="data.imageUrl" alt="" />
    <template v-if="data.list">
      <ul class="solution-text-list">
        <li
          class="solution-item"
          v-for="(item, index) in data.list"
          :key="index"
        >
          <div class="solution-item-title">{{ item.title }}</div>
          <div class="solution-item-sub-title">
            {{ item.desc }}
          </div>
          <a :href="item.linkUrl" class="solution-item-link link-text"
            >查看详情 ></a
          >
        </li>
      </ul>
    </template>
    <template v-else>
      <div class="solution-text">
        <div class="title">{{ data.title }}</div>
        <div class="desc">{{ data.desc }}</div>
        <a :href="data.linkUrl" class="link-btn">查看详情 ></a>
        <img
          class="bank"
          :style="{
            width: data.title.indexOf('政务') !== -1 ? '380px' : '300px'
          }"
          v-if="data.iconUrl"
          :src="data.iconUrl"
          alt=""
        />
      </div>
    </template>
  </div>
</template>

<script>
export default {
  props: ['data']
}
</script>
<style lang="less" scoped>
.solution-com-wrap {
  display: inline-block;
  margin-right: 70px;
  .img {
    width: 600px;
    height: 375px;
    margin-right: 64px;
    vertical-align: top;
  }
  .solution-text-list {
    width: 350px;
    display: inline-block;
    vertical-align: middle;
    .solution-item {
      margin-bottom: 32px;
      text-align: left;
      &:last-child {
        margin-bottom: 0;
      }
      .solution-item-title {
        height: 25px;
        font-size: 18px;
        font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        line-height: 25px;
        margin-bottom: 8px;
      }
      .solution-item-sub-title {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        line-height: 26px;
        margin-bottom: 8px;
      }
      .solution-item-link {
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 500;
        color: #52bf63;
        line-height: 20px;
      }
    }
  }
  .solution-text {
    width: 350px;
    padding-top: 32px;
    display: inline-block;
    text-align: left;
    .title {
      height: 42px;
      font-size: 30px;
      font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
      line-height: 42px;
      margin-bottom: 24px;
    }
    .desc {
      width: 352px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
      line-height: 26px;
      margin-bottom: 24px;
    }
    .link-btn {
      display: block;
      width: 120px;
      height: 32px;
      background: #52bf63;
      border-radius: 20px;
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 500;
      color: #ffffff;
      line-height: 32px;
      text-align: center;
      margin-bottom: 70px;
    }
    .bank {
      width: 300px;
    }
  }
}
</style>
