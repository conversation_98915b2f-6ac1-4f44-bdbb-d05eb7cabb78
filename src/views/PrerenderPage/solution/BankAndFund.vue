<template>
  <div class="industry-wrap">
    <div class="header" style="background: #020709">
      <img
        class="header-img"
        src="https://img.yeepay.com/fe-resources/yop-docs/images/bank/banner.png"
        alt=""
      />
      <div class="title">银行与基金行业</div>
      <div class="desc">
        聚焦互联网金融持牌如银行、基金业务，开拓和深耕银行、基金业客户。顺应形势发展，不断进行产品创新，为银行和基金客户，提供优质服务。
      </div>
      <a
        class="btn"
        href="https://www.yeepay.com/customerService/businessCooperation"
        target="_blank"
        >立即咨询 ></a
      >
    </div>
    <div class="solution-wrap">
      <div class="content">
        <div class="text-wrap">
          <div class="title">投资通解决方案</div>
          <div class="title-desc">
            为满足商业银行迈向全面数字化转型的时代，获取互联网客户流量，以及日益丰富的银行理财场景，易宝支付推出满足银行线上业务需求的安全、高效、稳定大额的收付产品。
          </div>
          <a
            class="link-btn"
            href="https://open.yeepay.com/docs/products/tzt/index.html"
            >了解更多 ></a
          >
          <div class="sub-title">方案优势</div>
          <ul class="project-list">
            <li class="project-item">已成功对接60余家银行类商户</li>
            <li class="project-item">灵活定制</li>
            <li class="project-item">复合场景</li>
            <li class="project-item">对接简单</li>
            <li class="project-item">多场景</li>
          </ul>
        </div>
        <div class="img-wrap">
          <img
            src="https://img.yeepay.com/fe-resources/yop-docs/images/bank/touzitong.png"
            alt=""
          />
        </div>
      </div>
    </div>
    <div class="solution-wrap">
      <div class="content">
        <div class="img-wrap" style="margin-right: 100px">
          <img
            src="https://img.yeepay.com/fe-resources/yop-docs/images/bank/ririying.png"
            alt=""
          />
        </div>
        <div class="text-wrap">
          <div class="title">日日盈解决方案</div>
          <div class="title-desc">
            借助于易宝自身完善的账户体系，与背后强大的银行渠道资源，在确保资金使用的时效性和安全性的前提下，为合作商户的日常结算资金或代付资金提供保值增值服务。
          </div>
          <a
            class="link-btn"
            :href="
              encodeURI(
                'https://www.yeepay.com/customerService/businessCooperation?cooperationIntention=日日盈解决方案'
              )
            "
            target="_blank"
            >申请合作 ></a
          >
          <div class="sub-title">方案优势</div>
          <ul class="project-list">
            <li class="project-item">便捷线上开户，无需面签</li>
            <li class="project-item">资金安全、收益可观</li>
            <li class="project-item">服务24/7</li>
            <li class="project-item">配套服务支持正规回单下载、支持询证</li>
          </ul>
        </div>
      </div>
    </div>
    <div class="content">
      <Industry :sceneList="sceneList" />
      <Partner :partnerList="partnerList" />
    </div>
    <div class="guide-wrap">
      <div class="content">
        <p>入驻指南</p>
        <h3 style="margin: 32px 0 16px 0">准入条件</h3>
        <p class="condition-content">大陆境内，依法开展互联网存款业务的银行</p>
        <h3>开通流程</h3>
        <img
          class="flow-img"
          src="https://img.yeepay.com/fe-resources/yop-docs/images/bank/bank-process.png"
        />
      </div>
    </div>
    <PageFooter />
  </div>
</template>

<script>
export default {
  data() {
    return {
      partnerList: [
        {
          partnerImage:
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/yinlian.png',
        },
        {
          partnerImage:
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/zhonghang.png',
        },
        {
          partnerImage:
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/nongye.png',
        },
        {
          partnerImage:
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/gongshang.png',
        },
        {
          partnerImage:
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/jianshe.png',
        },
        {
          partnerImage:
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/zhaoshang.png',
        },
        {
          partnerImage:
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/jiaotong.png',
        },
        {
          partnerImage:
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/youzheng.png',
        },
        {
          partnerImage:
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/guangda.png',
        },
        {
          partnerImage:
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/pingan.png',
        },
        {
          partnerImage:
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/beijing.png',
        },
        {
          partnerImage:
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/pufa.png',
        },
        {
          partnerImage:
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/guangfa.png',
        },
        {
          partnerImage:
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/xingye.png',
        },
        {
          partnerImage:
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/zhongxin.png',
        },
        {
          partnerImage:
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/huaxia.png',
        },
        {
          partnerImage:
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/minsheng.png',
        },
        {
          partnerImage:
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/nongxin.png',
        },
        {
          partnerImage:
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/yilian.png',
        },
        {
          partnerImage:
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/baixin.png',
        },
        {
          partnerImage:
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/zhongguancun.png',
        },
        {
          partnerImage:
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/suning.png',
        },
        {
          partnerImage:
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/zhongbang.png',
        },
      ],
      sceneList: [
        {
          sceneImageOpen:
            'url(' +
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/bank1-2.png' +
            ')',
          sceneImageClose:
            'url(' +
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/bank1.png' +
            ')',
          closeText: '银行理财产品收单',
          product: '相关产品: 投资通',
          productUrl: 'https://open.yeepay.com/docs/products/tzt/index.html',
          target: '_self',
          openText: {
            content1: '用户选择银行理财产品，并开通银行存款账户（二类户）',
            content2: '用户使用绑定储蓄卡向已开通的银行存款账户充值资金',
            content3: '用户理财产品到期后发起对本金和利息的提现',
          },
        },
        {
          sceneImageOpen:
            'url(' +
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/bank2-2.png' +
            ')',
          sceneImageClose:
            'url(' +
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/bank2.png' +
            ')',
          closeText: '银行贷款还款',
          product: '相关产品: 投资通',
          productUrl: 'https://open.yeepay.com/docs/products/tzt/index.html',
          target: '_self',
          openText: {
            content1: '银行提供的贷款，用户按期还款',
            content2: '为其提供还款服务',
          },
        },
        {
          sceneImageOpen:
            'url(' +
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/business-2.png' +
            ')',
          sceneImageClose:
            'url(' +
            'https://img.yeepay.com/fe-resources/yop-docs/images/bank/business.png' +
            ')',
          closeText: '商户资金增值',
          product: '相关产品: 日日盈',
          productUrl:
            'https://www.yeepay.com/customerService/businessCooperation',
          target: '_blank',
          openText: {
            content1:
              '为企业账户内闲置资金，对接银行增值产品，帮助闲置资金产生额外收益。',
          },
        },
      ],
    }
  },
}
</script>
