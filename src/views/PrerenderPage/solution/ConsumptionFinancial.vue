<template>
  <div class="industry-wrap">
    <div class="header" style="background: #001015">
      <img
        class="header-img"
        src="https://img.yeepay.com/fe-resources/yop-docs/images/consumptionFinancial/banner.png"
        alt=""
      />
      <div class="title">消费金融</div>
      <div class="desc">
        专注于链接金融生态的消费端、资金端及消费场景，输出行业支付解决方案和增值服务。
      </div>
      <a
        class="btn"
        href="https://www.yeepay.com/customerService/businessCooperation"
        target="_blank"
        >立即咨询 ></a
      >
    </div>
    <div class="solution-wrap">
      <div class="content">
        <div class="text-wrap">
          <div class="title">授权扣款解决方案</div>
          <div class="title-desc">
            向消费金融行业商户提供：单笔或批量扣款、鉴权绑卡、
            退款、对账、分账、结算、查单等功能。
          </div>
          <div class="link-wrap">
            <a class="link-btn" href="/docs/products/paperorder/index.html"
              >老授权扣款 ></a
            >
            <a class="link-btn" href="/docs/products/newsqkk/index.html"
              >新授权扣款 ></a
            >
          </div>
          <div class="sub-title">方案优势</div>
          <ul class="project-list">
            <li class="project-item">
              首创“支付＋增值“为一体的全方位综合服务体系，为客户业务的健康发展和安全合规护航
            </li>
            <li class="project-item">
              以支付服务为核心，一站式解决“鉴权＋放款＋还款＋分账＋对账”需求
            </li>
            <li class="project-item">
              为消费金融客户量身打造分期支付产品，为资金方链接更多消费场景，为用户提供更多支付方式
            </li>
            <li class="project-item">
              叠加数据、账户、资金、风控、等增值服务，全方位服务客户更深层次的需求
            </li>
          </ul>
        </div>
        <div class="img-wrap">
          <img
            src="https://img.yeepay.com/fe-resources/yop-docs/images/consumptionFinancial/shouquan.png"
            alt=""
          />
        </div>
      </div>
    </div>
    <div class="content">
      <Industry :sceneList="sceneList" />
    </div>
    <div class="partner-wrap">
      <div class="content">
        <h2>合作伙伴</h2>
        <img
          style="margin-bottom: 90px"
          class="img"
          src="https://img.yeepay.com/fe-resources/yop-docs/images/consumptionFinancial/partner.png"
          alt=""
        />
      </div>
      <div class="guide-wrap">
        <div class="content">
          <p>入驻指南</p>
          <h3 style="margin: 32px 0 16px 0">接入流程向导</h3>
          <img
            src="https://img.yeepay.com/fe-resources/yop-docs/images/consumptionFinancial/flow.png"
          />
        </div>
      </div>
    </div>
    <PageFooter />
  </div>
</template>

<script>
export default {
  data() {
    return {
      sceneList: [
        {
          sceneImageOpen:
            'url(' +
            'https://img.yeepay.com/fe-resources/yop-docs/images/consumptionFinancial/scene1.png' +
            ')',
          sceneImageClose:
            'url(' +
            'https://img.yeepay.com/fe-resources/yop-docs/images/consumptionFinancial/scene1-2.png' +
            ')',
          closeText: '鉴权／签约',
          openText: [
            '用户提供姓名、身份证号、银行卡号、手机号等信息进行鉴权，银行鉴权成功后即可进行绑卡。',
          ],
        },
        {
          sceneImageOpen:
            'url(' +
            'https://img.yeepay.com/fe-resources/yop-docs/images/consumptionFinancial/scene2.png' +
            ')',
          sceneImageClose:
            'url(' +
            'https://img.yeepay.com/fe-resources/yop-docs/images/consumptionFinancial/scene2-2.png' +
            ')',
          closeText: '还款',
          openText: ['绑卡成功后，通过绑卡id即可完成扣款'],
        },
      ],
    }
  },
}
</script>
<style lang="less" scoped>
.link-wrap {
  .link-btn {
    display: inline-block !important;
    vertical-align: top;
    &:first-child {
      margin-right: 16px;
    }
  }
}
</style>
