<template>
  <div class="industry-wrap">
    <div class="header" style="background: #040605">
      <img
        class="header-img"
        src="https://img.yeepay.com/fe-resources/yop-docs/images/hanglv/banner.png"
        alt=""
      />
      <div class="title">航旅行业线</div>
      <div class="desc">
        深度扎根航旅行业，提供权威的支付服务，并联合金融机构提供金融增值服务解决方案。
      </div>
      <a
        class="btn"
        href="https://www.yeepay.com/customerService/businessCooperation"
        target="_blank"
        >立即咨询 ></a
      >
    </div>
    <div class="solution-wrap">
      <div class="content">
        <div class="text-wrap" style="margin-right: 78px">
          <div class="title">融旅通解决方案</div>
          <div class="title-desc">
            为客户提供全国多家机场、火车站相关的多项服务，包括贵宾休息室快速安检
            通道。提供接口对接方式，并提供配套的对账、发票等服务。
          </div>
          <a
            class="link-btn"
            :href="
              encodeURI(
                'https://www.yeepay.com/customerService/businessCooperation?cooperationIntention=融旅通解决方案'
              )
            "
            target="_blank"
            >申请合作 ></a
          >
          <div class="sub-title">方案优势</div>
          <ul class="project-list">
            <li class="project-item">一单一结，成本低，作业灵活</li>
            <li class="project-item">资源丰富，集众家所长+自谈直营</li>
          </ul>
        </div>
        <div class="img-wrap">
          <img
            src="https://img.yeepay.com/fe-resources/yop-docs/images/hanglv/ronglvtong.png"
            alt=""
          />
        </div>
      </div>
    </div>
    <div class="solution-wrap">
      <div class="content">
        <div class="img-wrap" style="margin-right: 180px">
          <img
            src="https://img.yeepay.com/fe-resources/yop-docs/images/hanglv/huanqiu.png"
            alt=""
          />
        </div>
        <div class="text-wrap">
          <div class="title">环球e汇解决方案</div>
          <div class="title-desc">
            环球e汇是易宝支付联合银行和卡组织推出的虚拟信用卡产品。商户在开通易宝账户和环球e汇后，可以进行充值、申卡，消卡、查询等操作。在得到虚拟信用卡后，商户可以在境外航旅相关行业进行消费，并通过环球e汇查询交易明细。
          </div>
          <a
            class="link-btn"
            href="https://open.yeepay.com/docs/products/vcc-global/index.html"
            >了解更多 ></a
          >
        </div>
      </div>
    </div>
    <div class="content">
      <Industry :sceneList="sceneList" />
    </div>
    <div class="partner-wrap">
      <div class="content">
        <h2>合作伙伴</h2>
        <img
          class="img"
          src="https://img.yeepay.com/fe-resources/yop-docs/images/hanglv/partner.png"
          alt=""
        />
      </div>
    </div>
    <div class="guide-wrap">
      <div class="content">
        <p>入驻指南</p>
        <h3 style="margin: 32px 0 16px 0">准入条件</h3>
        <div class="condition-wrap" style="margin-bottom: 16px">
          <div class="condition-left">
            <div class="title">
              <img
                class="title-img"
                src="https://img.yeepay.com/fe-resources/yop-docs/images/hanglv/left1.png"
                alt=""
              />
              企业
            </div>
            <ul class="condition-list">
              <li class="condition-item">
                营业执照
                <p>(彩色扫描件或数码照片)</p>
              </li>
              <li class="condition-item">
                组织机构代码证
                <p>(彩色扫描件或数码照片，若已三证合一，则无需提供)</p>
              </li>
              <li class="condition-item">
                对公银行账户
                <p>(包含开户行省市信息，开户账号)</p>
              </li>
              <li class="condition-item">
                法人身份证
                <p>(彩色扫描件或数码照片)</p>
              </li>
            </ul>
          </div>
          <div class="condition-right">
            <div class="title">
              <img
                class="title-img"
                src="https://img.yeepay.com/fe-resources/yop-docs/images/hanglv/right1.png"
                alt=""
              />事业单位
            </div>
            <ul class="condition-list">
              <li class="condition-item">
                营业执照
                <p>(彩色扫描件或数码照片)</p>
              </li>
              <li class="condition-item">
                组织机构代码证
                <p>(彩色扫描件或数码照片，若已三证合一，则无需提供)</p>
              </li>
              <li class="condition-item">
                对公银行账户
                <p>(包含开户行省市信息，开户账号)</p>
              </li>
              <li class="condition-item">
                法人身份证
                <p>(彩色扫描件或数码照片)</p>
              </li>
            </ul>
          </div>
        </div>
        <div class="condition-wrap" style="margin-bottom: 64px">
          <div class="condition-left">
            <div class="title">
              <img
                class="title-img"
                src="https://img.yeepay.com/fe-resources/yop-docs/images/hanglv/left2.png"
                alt=""
              />
              个体工商户
            </div>
            <ul class="condition-list">
              <li class="condition-item">
                营业执照
                <p>(彩色扫描件或数码照片)</p>
              </li>
              <li class="condition-item">
                组织机构代码证
                <p>(彩色扫描件或数码照片，若已三证合一，则无需提供)</p>
              </li>
              <li class="condition-item">
                对公银行账户
                <p>(包含开户行省市信息，开户账号)</p>
              </li>
            </ul>
          </div>
          <div class="condition-right">
            <div class="title">
              <img
                class="title-img"
                src="https://img.yeepay.com/fe-resources/yop-docs/images/hanglv/right2.png"
                alt=""
              />小微商户
            </div>
            <ul class="condition-list">
              <li class="condition-item">
                法人身份证
                <p>(彩色扫描件或数码照片)</p>
              </li>
            </ul>
          </div>
        </div>
        <h3>开通流程</h3>
        <img
          src="https://img.yeepay.com/fe-resources/yop-docs/images/hanglv/flow.png"
        />
      </div>
    </div>
    <PageFooter />
  </div>
</template>

<script>
export default {
  data() {
    return {
      sceneList: [
        {
          sceneImageOpen:
            'url(' +
            'https://img.yeepay.com/fe-resources/yop-docs/images/kuajing/scene1.png' +
            ')',
          sceneImageClose:
            'url(' +
            'https://img.yeepay.com/fe-resources/yop-docs/images/kuajing/scene1-2.png' +
            ')',
          closeText: '为旅客提供更丰富、优质的旅游服务',
          product: '相关产品: 融旅通',
          productUrl:
            'https://www.yeepay.com/customerService/businessCooperation',
          target: '_blank',
          openText: [
            '许多平台、企业客户在为旅客提供机票、车票服务 的同时，也希望为旅客提供更加丰富的辅营产品和 配套服务。以便企业在提升服务能力的同时，增加 业务营收和新的利润增长点',
          ],
        },
        {
          sceneImageOpen:
            'url(' +
            'https://img.yeepay.com/fe-resources/yop-docs/images/kuajing/scene2.png' +
            ')',
          sceneImageClose:
            'url(' +
            'https://img.yeepay.com/fe-resources/yop-docs/images/kuajing/scene2-2.png' +
            ')',
          closeText: '境外航旅消费服务',
          product: '相关产品: 环球e汇',
          productUrl:
            'https://open.yeepay.com/docs/products/vcc-global/index.html',
          target: '_self',
          openText: [
            '商户在开通易宝账户和环球e汇后，可以进行充值、申卡，消卡、查询等操作。在得到虚拟信用卡后，商户可以在境外航旅相关行业进行消费，并通过环球e汇查询交易明细。',
          ],
        },
      ],
    }
  },
}
</script>
<style lang="less" scoped>
.condition-wrap {
  .condition-left {
    margin-right: 16px;
  }
  .condition-left,
  .condition-right {
    width: 592px;
    height: 223px;
    background: #ffffff;
    border-radius: 6px;
    display: inline-block;
    vertical-align: top;
    padding: 16px;
    padding-left: 72px;
    text-align: left;
    .title {
      height: 48px;
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
      line-height: 48px;
      margin-bottom: 7px;
      position: relative;
      .title-img {
        width: 48px;
        height: 48px;
        position: absolute;
        left: -56px;
        top: 0;
      }
    }
    .condition-list {
      .condition-item {
        margin-bottom: 16px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        line-height: 20px;
        position: relative;
        padding-left: 14px;
        height: 20px;
        &:last-child {
          margin-bottom: 0;
        }
        p {
          font-size: 13px;
          font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei',
            SimSun, sans-serif;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.45);
          margin-top: 6px;
          display: inline;
        }
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 7px;
          width: 6px;
          height: 6px;
          background: #d8d8d8;
          border-radius: 50%;
        }
      }
    }
  }
}
</style>
