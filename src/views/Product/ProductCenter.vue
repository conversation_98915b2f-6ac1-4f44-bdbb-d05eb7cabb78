<template>
	<div>
		<div class="product-center" v-if="!isMobile">
			<div class="header">
				<div class="header-content">
					<div class="left">
						<div class="title">易宝云产品</div>
						<div class="desc">支持业内所有主流支付方式，满足用户多样化的支付需求</div>
					</div>
          <img class="bg" src="../../assets/productCenter.png" alt="">
				</div>
			</div>
			<div class="content">
				<div class="catalogue">
					<div class="inner">
						<div class="title">产品类目</div>
						<a-anchor
							@click="handleClick"
							:targetOffset="80"
							:affix="false"
              @change="anchorChange"
						>
							<a-anchor-link 
              :class="{'ant-anchor-link-active': !currentAnchor && index === 0}" 
              :id="`${item.code}-id`" v-for="(item, index) in list" :href="`#${item.code}`" :key="item.code" :title="item.name" />
						</a-anchor>
					</div>
				</div>
				<div>
					<div class="product" v-for="item in list" :key="item.code">
						<div class="title" :id="item.code">{{ item.name }}</div>
						<div v-for="(item2, i) in item.children" :key="item2.code">
							<div class="sub-title" :style="{ marginTop: i === 0 ? 0 : '32px' }" v-if="item2.code !== '_DEFAULT'">{{ item2.name }}</div>
							<div class="list">
								<div class="item" v-for="item3 in item2.children" :key="item3.code" @click="goProduct(item3.code)">
									<img class="icon" :src="docBasic[`${item3.code}.icon`]" />
									<div class="right">
										<div class="name text-o">{{ docBasic[`${item3.code}.title`] }}</div>
										<div class="desc text-o">{{ docBasic[`${item3.code}.desc`] }}</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
      <PageFooter style="background-color: #fff" :mobile="isMobile" />
		</div>
		<div v-else class="product-center-m" id="product-center-m">
      <div class="header-nav">
        <a-tabs size="small" @change="onTabsChange">
          <a-tab-pane v-for="item in list" :key="item.code" :tab="item.name"></a-tab-pane>
        </a-tabs>
      </div>
      <div class="header">
        <div class="title">易宝云产品</div>
        <div class="desc">支持业内所有主流支付方式，满足用户<br />多样化的支付需求</div>
        <div class="bg"></div>
      </div>
      <div class="product" v-for="item in list" :key="item.code">
        <div class="title" :id="item.code">{{ item.name }}</div>
        <div v-for="(item2, i) in item.children" :key="item2.code">
          <div class="sub-title" :style="{ marginTop: i === 0 ? 0 : '24px' }" v-if="item2.code !== '_DEFAULT'">{{ item2.name }}</div>
          <div class="list">
            <div class="item" v-for="item3 in item2.children" :key="item3.code" @click="goProduct(item3.code)">
              <img class="icon" :src="docBasic[`${item3.code}.icon`]" />
              <div class="right">
                <div class="name text-o">{{ docBasic[`${item3.code}.title`] }}</div>
                <div class="desc text-o">{{ docBasic[`${item3.code}.desc`] }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <PageFooter style="background-color: #fff" />
    </div>
	</div>
</template>

<script>
import Api from '@/api/product'
export default {
	data() {
		return {
      docBasic: {},
      currentAnchor: ''
		}
	},
	async created() {
    await this.getDocBasic()
    await this.$store.dispatch('getProductMenu')
	},
	computed: {
		isMobile() {
			return this.$store.state.isMobile
		},
		list() {
			return this.$store.state.productMenuList
		},
	},
  methods: {
		goProduct(code) {
			window.location.href = `/docs/products/${code}/index.html`
		},
		catalogueClick(code) {
			this.activeCode = code
		},
		getDocBasic() {
			return Api.getDocBasic().then((res) => {
				this.docBasic = res
			})
		},
		handleClick(e) {
			e.preventDefault()
		},
    anchorChange(anchor) {
      this.currentAnchor = anchor
		},
    onTabsChange(code) {
      const dom = document.querySelector(`#${code}`)
      const top = dom.getBoundingClientRect().top - 100 + document.documentElement.scrollTop
      document.documentElement.scrollTo({top, behavior: 'smooth'})
    }
	},
}
</script>

<style lang="less" scoped>
.product-center {
	background-color: rgba(0, 0, 0, 0.04);
	font-family: PingFangSC, PingFang SC;
	min-width: 1425px;
	.header {
		height: 420px;
    background: #2E2E2E;
    position: relative;
    .bg {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      height: 420px;
    }
		.header-content {
			max-width: 1200px;
			margin: 0 auto;
			display: flex;
      position: relative;
			.left {
				padding-top: 160px;
        position: relative;
        z-index: 1;
			}

			.title {
				font-weight: 600;
				font-size: 40px;
				color: #fff;
				line-height: 56px;
				margin-bottom: 16px;
			}

			.desc {
				font-weight: 400;
				font-size: 16px;
				color: #fff;
				line-height: 28px;
        width: 500px;
			}
		}
	}

	.content {
		max-width: 1200px;
		margin: 0 auto;
		padding: 80px 0 56px;
		display: flex;
		min-height: calc(100vh - 65px - 360px - 392px);
		.catalogue {
			margin-right: 80px;
			width: 82px;

			.inner {
				position: sticky;
				top: 20px;
				/deep/ .ant-anchor-ink {
					display: none;
				}
				/deep/ .ant-anchor-link {
					position: relative;
					padding-top: 0;
					font-weight: 400;
					color: rgba(0, 0, 0, 0.85);
					padding-bottom: 24px;
					line-height: 24px;
          .ant-anchor-link-title {
            font-size: 16px;
          }
					&::before {
						content: "";
						position: absolute;
						top: 7px;
						left: 0;
						width: 10px;
						height: 10px;
						background: #f7f7f7;
						border: 1px solid rgba(0, 0, 0, 0.45);
						border-radius: 50%;
					}

					&::after {
						content: "";
						position: absolute;
						top: 24px;
						left: 4.5px;
						width: 1px;
						height: 24px;
						background-color: rgba(0, 0, 0, 0.16);
					}

					&:last-child {
						&::after {
							display: none;
						}
					}
				}
				/deep/ .ant-anchor-link-active {
					&::before {
						background: #52bf63;
						border: none;
					}
					font-weight: 500;
				}
			}

			.title {
				font-weight: 400;
				font-size: 16px;
				color: rgba(0, 0, 0, 0.85);
				line-height: 33px;
				margin-bottom: 24px;
			}
		}

		.product {
			margin-bottom: 48px;
			flex: 1;

			.title {
				font-weight: 600;
				font-size: 24px;
				color: rgba(0, 0, 0, 0.85);
				line-height: 33px;
				margin-bottom: 16px;
			}

			.sub-title {
				font-weight: 400;
				font-size: 18px;
				color: rgba(0, 0, 0, 0.85);
				line-height: 25px;
				margin-bottom: 16px;
			}

			.list {
				margin-right: -16px;
				margin-bottom: -16px;

				.item {
					width: 335px;
					height: 100px;
					background: #ffffff;
					border-radius: 16px;
					border: 1px solid #ffffff;
					margin-right: 16px;
					margin-bottom: 16px;
					display: inline-flex;
					justify-content: center;
					align-items: center;
					padding: 24px;
					cursor: pointer;
          &:hover {
            transform: translateY(-3px);
          transition: all 0.3s;
            box-shadow: 0px 4px 10px 0px rgba(0,0,0,0.1);
          }
					.icon {
						width: 52px;
						height: 52px;
						border-radius: 12px;
						margin-right: 16px;
					}

					.right {
						flex: 1;
						max-width: 216px;
						.name {
							font-weight: 600;
							font-size: 18px;
							color: rgba(0, 0, 0, 0.8);
							line-height: 24px;
							margin-bottom: 8px;
						}

						.desc {
							height: 20px;
							font-weight: 400;
							font-size: 14px;
							color: rgba(0, 0, 0, 0.45);
							line-height: 20px;
						}
					}
				}
			}
		}
	}
}
.product-center-m {
  background-color: rgba(0, 0, 0, 0.04);
	font-family: PingFangSC, PingFang SC;
  height: 100vh;
  overflow-y: auto;
  padding-top: 50px;
  .header-nav {
    position: sticky;
    background-color: #fff;
    z-index: 33;
    top: 0px;
    /deep/ .ant-tabs-bar{
      margin: 0;
      .ant-tabs-tab {
        padding: 12px 0;
        margin-right: 0;
        margin-left: 16px;
        &:last-child {
          margin-right: 16px;
        }
      }
      .ant-tabs-ink-bar{
        width: 56px;
      }
      .ant-tabs-nav-scroll {
        overflow-x: auto;
      }
      .ant-tabs-nav-container-scrolling {
        padding: 0;
      }
      .ant-tabs-tab-arrow-show {
        display: none;
      }
    }
  }
  .header {
    background: #ECECEC;
    position: relative;
    padding-top: 227px;
    margin-bottom: 32px;
    height: 420px;
    .bg {
      background: url("../../assets/productCenterM.png") no-repeat center;
      width: 100%;
      height: 420px;
      background-size: contain;
      position: absolute;
      top: 0;
    }
    .title {
      height: 33px;
      font-weight: 600;
      font-size: 24px;
      color: #333333;
      line-height: 33px;
      padding: 0 24px;
      margin-bottom: 8px;
      position: relative;
      z-index: 2;
    }
    .desc {
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 20px;
      padding: 0 24px;
      z-index: 2;
      position: relative;
    }
  }
  .product {
    padding: 0 16px 32px;
    .title {
      font-weight: 600;
      font-size: 18px;
      color: rgba(0,0,0,0.85);
      line-height: 25px;
      margin-bottom: 16px;
    }
    .sub-title {
      font-weight: 400;
      font-size: 16px;
      color: rgba(0,0,0,0.85);
      line-height: 22px;
      margin-bottom: 12px;
    }
    .list {
      background: #FFFFFF;
      border-radius: 16px;
      padding: 8px 24px;
      .item {
        height: 64px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid rgba(0,0,0,0.04);
        &:last-child{
          border-bottom: none;
        }
        .icon {
          width: 38px;
          height: 38px;
          border-radius: 8px;
          margin-right: 12px;
        }
        .right {
          flex: 1;
          max-width: calc(100vw - 130px);
          .name {
            font-weight: 500;
            font-size: 14px;
            color: rgba(0,0,0,0.8);
            line-height: 24px;
          }
          .desc {
            font-weight: 400;
            font-size: 12px;
            color: rgba(0,0,0,0.45);
            line-height: 20px;
          }
        }
      }
    }
  }
}
</style>
