import { mapState } from 'vuex'
export default {
  computed: {
    ...mapState('search', [
      'searchParamsMap',
      'categoryList',
      'searchTipWord',
      'currentCode',
      'result',
      'loading',
      'searchWord'
    ]),
    showSubCategories() {
      return this.result.subCategories && this.result.subCategories.length > 0
    },
    totalCount() {
      if (!this.result.subCategories) return 0
      return this.result.subCategories.reduce(
        (total, item) => (total += item.count),
        0
      )
    },
    currentSubCategory() {
      if (!this.searchParamsMap[this.currentCode]) return ''
      return this.searchParamsMap[this.currentCode].subCategory
    },
    codeName() {
      if (!this.searchParamsMap[this.currentCode]) return ''
      return this.searchParamsMap[this.currentCode].codeName
    },
    currentPageNo() {
      if (!this.searchParamsMap[this.currentCode]) return 0
      return this.searchParamsMap[this.currentCode]._pageNo
    }
  },
  methods: {
    onSearch() {
      if (this.$refs.searchInput) this.$refs.searchInput.blur()
      this.$store.commit('search/setPageNo', 1)
      this.$store.dispatch('search/getResult')
      if (this.$route.query.wd === this.searchWord) return
      this.$router.replace({
        path: '/search',
        query: {
          wd: this.searchWord
        }
      })
    },
    onChange(e) {
      this.$store.commit('search/setSearchWord', e.target.value)
    },
    changeMenu(code) {
      this.$store.commit('search/setCurrentCode', code)
      this.$store.dispatch('search/getResult')
    },
    onPageChange(pageNo) {
      this.$store.commit('search/setPageNo', pageNo)
      this.$store.dispatch('search/getResult')
    },
    onSubCategoriesChange(code) {
      this.$store.commit('search/setSubCategories', code)
      this.$store.dispatch('search/getResult')
    }
  }
}
