<template>
  <div class="mobile">
    <MobileHeader :navName="$t('menu.questionTitle')" />
    <div class="screen-wrap">
      <div
        v-if="showSubCategories"
        class="sub-mobile-icon"
        @click="showSubMenu"
      >
        <img
          src="https://img.yeepay.com/fe-resources/yop-docs/images/search/screen.png"
          alt=""
        />
      </div>
      <div class="mobile-top-menu" @click.self="showSubMenu" v-if="subMenu">
        <div class="submenu">
          <div class="title">{{ $t('filter') }}</div>
          <div class="line"></div>
          <ul class="sub-menu-list">
            <li
              class="sub-menu-item"
              @click="changeSubCategory('')"
              :class="!currentSubCategory ? 'active' : ''"
              key="全部"
            >
              {{ $t('all') }}
            </li>
            <li
              class="sub-menu-item"
              v-for="(item, index) in result.subCategories"
              @click="changeSubCategory(item.code)"
              :class="currentSubCategory === item.code ? 'active' : ''"
              :key="index"
            >
              {{ item.name }}
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="search-wrap">
        <MobileSearchInput needSubMenu :searchWord="searchWord" />
      </div>
      <a-spin :spinning="loading" :delay="500">
        <div slot="indicator">
          <img
            src="@/assets/images/loading_32.gif"
            class="img-mobile-loading"
            alt
          />
        </div>
        <div class="menu-wrap">
          <ul class="menu-list">
            <li
              class="menu-item"
              v-for="(item, index) in categoryList"
              :key="index"
              @click="changeMenu(item.code)"
              :class="currentCode === item.code ? 'menu-active' : ''"
            >
              {{ item.name }}
            </li>
          </ul>
        </div>
        <div class="result-content">
          <div class="has-result" v-if="result.totalNum > 0">
            <div
              class="search-num"
              v-if="searchTipWord"
              v-html="$t('searchTip', { searchTipWord })"
            ></div>
            <ul class="search-list">
              <template v-for="(item, index) in result.mobilePageItems">
                <li
                  class="search-item"
                  v-if="item.content"
                  :key="index"
                >
                  <SearchItem
                    :detail="item"
                    :lineClamp="5"
                    :words="result.words"
                  />
                </li>
              </template>
            </ul>
          </div>
          <div class="no-result" v-else-if="!loading">
            <img
              class="no-result-img"
              src="@/assets/images/no-result.png"
              alt=""
            />
            <div class="search-num">
              {{ $t('searchNoResult') }}
            </div>
          </div>
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script>
import mixin from './mixin'
import MobileHeader from '@/components/MobileHeader'
import SearchItem from '@/components/SearchItem'
import MobileSearchInput from '@/components/SearchInput/mobileSearchInput'
export default {
  mixins: [mixin],
  components: {
    MobileHeader,
    MobileSearchInput,
    SearchItem,
  },
  data() {
    return {
      subMenu: false,
    }
  },
  computed: {
    scrollTop() {
      return this.$store.state.scrollTop
    },
  },
  watch: {
    scrollTop() {
      this.onPageScroll()
    },
  },
  methods: {
    showSubMenu() {
      this.subMenu = !this.subMenu
    },
    changeSubCategory(code) {
      this.showSubMenu()
      this.onSubCategoriesChange(code)
    },
    onPageScroll() {
      const height =
        document.documentElement.scrollHeight -
        document.documentElement.clientHeight
      if (
        this.scrollTop > height - 50 &&
        this.result.pageNo < this.result.totalPageNum &&
        !this.loading
      ) {
        this.onPageChange(this.result.pageNo + 1)
      }
    },
  },
}
</script>

<style lang="less" scoped>
.mobile {
  .screen-wrap {
    .sub-mobile-icon {
      position: fixed;
      top: 168px;
      right: 0;
      background: #fff;
      z-index: 10;
      width: 48px;
      height: 48px;
      border-radius: 100px 0px 0px 100px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.11);
      img {
        width: 20px;
        height: 20px;
      }
    }
    .mobile-top-menu {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.2);
      z-index: 2002;
      display: flex;
      justify-content: flex-end;
      .submenu {
        width: 265px;
        background: #ffffff;
        padding: 20px 15px 0;
        .title {
          height: 20px;
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.85);
          line-height: 20px;
          margin-bottom: 20px;
        }
        .line {
          height: 1px;
          background: #efefef;
          margin-bottom: 30px;
        }
        .sub-menu-list {
          .sub-menu-item {
            font-size: 14px;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.65);
            line-height: 20px;
            margin-bottom: 30px;
          }
          .active {
            color: #52bf63;
          }
        }
      }
    }
  }
  .content {
    padding-top: 65px;
    .search-wrap {
      bottom: 5px;
    }
    .menu-wrap {
      height: 50px;
      margin-bottom: 28px;
      border-bottom: 1px solid #efefef;
      text-align: center;
      .menu-list {
        display: flex;
        justify-content: space-between;
        flex-wrap: nowrap;
        overflow-x: auto;
        overflow-y: hidden;
        padding: 0 20px;
        .menu-item {
          display: inline-block;
          flex-shrink: 0;
          height: 49px;
          font-size: 16px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 49px;
          margin-right: 10px;
          cursor: pointer;
          &:last-child {
            margin-right: 0;
          }
        }
        .menu-active {
          color: #52bf63;
          border-bottom: 2px solid #52bf63;
        }
      }
    }
    .result-content {
      min-height: 500px;
    }
    .no-result {
      padding-top: 64px;
      .no-result-img {
        display: block;
        margin: 0 auto 24px;
        height: 54px;
      }
      .search-num {
        text-align: center;
      }
    }
    .has-result {
      padding: 0 20px;
      padding-bottom: 20px;
      .search-num {
        margin-bottom: 24px;
      }
      .search-list {
        .search-item {
          margin-bottom: 40px;
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}
</style>
