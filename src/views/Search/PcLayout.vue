<template>
  <div class="search-wrap">
    <div class="search-inner">
      <div class="header">
        <a-input
          placeholder=""
          ref="searchInput"
          allowClear
          :value="searchWord"
          @focus="onFocus"
          @blur="onBlur"
          @change="onChange"
          @pressEnter="() => onCategoryClick('ALL')"
          style="width: 800px;height:40px"
        >
          <a-button
            @click="() => onCategoryClick('ALL')"
            slot="addonAfter"
            type="primary"
            :disabled="!searchWord"
          >
            {{ $t('serach') }}
          </a-button>
        </a-input>
        <ul
          key="sub-search"
          class="sub-search"
          v-show="isShowSub && searchWord"
        >
          <li
            class="sub-search-item"
            v-for="item in categoryList"
            @click="onCategoryClick(item.code)"
            :key="item.code"
          >
            <div class="text">{{ searchWord }}</div>
            <div class="label">
              <div class="label-text">{{ item.name }}</div>
              <a-icon type="enter" style="font-size: 9px; color: #000" />
            </div>
          </li>
        </ul>
      </div>
      <div class="menu-wrap">
        <ul class="menu-list">
          <li
            class="menu-item"
            v-for="(item, index) in categoryList"
            :key="index"
            @click="changeMenu(item.code)"
            :class="currentCode === item.code ? 'menu-active' : ''"
          >
            {{ item.name }}
          </li>
        </ul>
      </div>
      <a-spin :spinning="loading">
        <div slot="indicator">
          <img
            src="@/assets/images/loading_64.gif"
            class="img-pc-loading"
            alt
          />
        </div>
        <div class="search-result-wrap">
          <div
            class="has-result"
            :class="{
              'has-result-noSub':
                !result.subCategories || result.subCategories.length < 1
            }"
            v-if="result.totalNum > 0"
          >
            <div class="left">
              <div
                class="search-num"
                v-if="searchTipWord"
                v-html="$t('searchTip', { searchTipWord })"
              ></div>
              <ul class="search-list">
                <template v-for="(item, index) in result.pageItems">
                  <li
                    class="search-item"
                    v-if="item.content"
                    :key="index"
                  >
                    <SearchItem
                      :detail="item"
                      :lineClamp="3"
                      :words="result.words"
                    />
                  </li>
                </template>
              </ul>
            </div>
            <div class="right">
              <div class="sub-menu">
                <div class="sub-menu-header">
                  <div class="icon"></div>
                  {{ codeName }}
                </div>
                <ul class="sub-menu-list">
                  <li
                    class="sub-menu-item"
                    @click="onSubCategoriesChange('')"
                    :class="!currentSubCategory ? 'active' : ''"
                    key="全部"
                  >
                    {{ $t('all') }}
                  </li>
                  <li
                    class="sub-menu-item"
                    v-for="(item, index) in result.subCategories"
                    @click="onSubCategoriesChange(item.code)"
                    :class="currentSubCategory === item.code ? 'active' : ''"
                    :key="index"
                  >
                    {{ item.name }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div class="no-result" v-else-if="!loading">
            <img
              class="no-result-img"
              src="@/assets/images/no-result.png"
              alt=""
            />
            <div class="search-num">
              {{ $t('searchNoResult') }}
            </div>
          </div>
        </div>
        <div class="pagination-wrap" v-if="result.totalNum > 10">
          <a-pagination
            :pageSize="20"
            :current="currentPageNo"
            :total="result.totalNum"
            @change="onPageChange"
          />
        </div>
      </a-spin>
    </div>
    <div class="search-line"></div>
    <PageFooter />
  </div>
</template>

<script>
import mixin from './mixin'
import SearchItem from '@/components/SearchItem'
import { Pagination } from 'ant-design-vue'
export default {
  mixins: [mixin],
  components: {
    SearchItem,
    [Pagination.name]: Pagination
  },
  data() {
    return {
      isShowSub: false
    }
  },
  methods: {
    onCategoryClick(code) {
      this.$store.commit('search/setCurrentCode', code)
      this.onSearch()
    },
    onFocus() {
      this.isShowSub = true
    },
    onBlur() {
      setTimeout(() => {
        this.isShowSub = false
      }, 300)
    }
  }
}
</script>

<style lang="less" scoped>
.search-wrap {
  .search-line {
    margin-top: 16px;
    width: 100%;
    height: 1px;
    background: #efefef;
  }
  .search-inner {
    width: 1000px;
    margin: 0 auto;
    padding-top: 60px;
    .header {
      text-align: center;
      margin-bottom: 60px;
      position: relative;
      /deep/ .ant-input-group-addon {
        padding: 0;
        border: none;
      }
      .sub-search {
        position: absolute;
        z-index: 10;
        top: 32px;
        left: 100px;
        background-color: #fff;
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.2);
        border-radius: 4px;
        padding: 8px 0;
        .sub-search-item {
          width: 800px;
          height: 36px;
          font-size: 14px;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.65);
          line-height: 20px;
          padding: 8px 16px;
          white-space: nowrap;
          cursor: pointer;
          &:hover {
            background: rgba(0, 0, 0, 0.06);
          }
          .text {
            width: 580px;
            overflow: hidden;
            white-space: nowrap;
            word-break: break-all;
            text-overflow: ellipsis;
            float: left;
            text-align: left;
          }
          .label {
            float: right;
            height: 20px;
            background: rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            line-height: 20px;
            padding: 0 12px;
            .label-text {
              display: inline-block;
            }
          }
        }
      }
    }
    .menu-wrap {
      height: 42px;
      margin-bottom: 16px;
      border-bottom: 1px solid #efefef;
      text-align: center;
      .menu-list {
        display: inline-block;
        .menu-item {
          display: inline-block;
          width: 115px;
          height: 42px;
          font-size: 16px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 22px;
          margin-right: 80px;
          cursor: pointer;
          overflow: hidden;
          white-space: nowrap;
          word-break: break-all;
          text-overflow: ellipsis;
          &:last-child {
            margin-right: 0;
          }
          &:hover {
            color: #52bf63;
          }
        }
        .menu-active {
          color: #52bf63;
          border-bottom: 2px solid #52bf63;
        }
      }
    }
    .search-num {
      margin-bottom: 32px;
    }
    .search-result-wrap {
      min-height: 500px;
      .has-result {
        .left,
        .right {
          display: inline-block;
          vertical-align: top;
        }
        .left {
          width: 800px;
        }
        .right {
          width: 200px;
          padding-left: 24px;
        }
      }
      .has-result-noSub {
        .left {
          width: 1000px;
        }
        .right {
          display: none;
        }
      }
      .search-list {
        margin-bottom: 0;
        .search-item {
          padding: 16px;
          &:hover {
            box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.03),
              0px -3px 9px 0px rgba(0, 0, 0, 0.03);
            border-radius: 6px;
          }
        }
      }
      .sub-menu {
        width: 176px;
        border-radius: 2px;
        .sub-menu-header {
          height: 44px;
          background: rgba(0, 0, 0, 0.03);
          border-radius: 2px;
          padding-left: 42px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-size: 16px;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.65);
          line-height: 44px;
          position: relative;
          .icon {
            position: absolute;
            left: 17px;
            top: 14px;
            width: 15px;
            height: 16px;
            background: url('../../assets/images/file-icon.png') no-repeat
              center;
            background-size: 15px 16px;
          }
        }
        .sub-menu-list {
          .sub-menu-item {
            padding: 8px 17px;
            font-size: 14px;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.65);
            line-height: 20px;
            cursor: pointer;
            &:hover {
              color: #52bf63;
            }
          }
          .active {
            color: #52bf63;
          }
        }
      }
      .no-result {
        padding: 100px 0;
        .no-result-img {
          display: block;
          margin: 0 auto 24px;
          height: 54px;
        }
        .search-num {
          text-align: center;
        }
      }
    }
    .pagination-wrap {
      padding-top: 8px;
      text-align: right;
    }
  }
}
</style>
