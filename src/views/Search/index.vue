<template>
  <div>
    <Component :is="showComponents" />
  </div>
</template>
<script>
import PcLayout from './PcLayout'
import MobileLayout from './MobileLayout'
export default {
  components: {
    MobileLayout,
    PcLayout
  },
  data() {
    return {}
  },
  computed: {
    showComponents() {
      return this.$store.state.showComponents
    },
    searchWord() {
      return this.$store.state.search.searchWord
    }
  },
  watch: {
    '$store.state.langType'() {
      this.init()
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      if (this.$route.query.wd) {
        this.$store.commit('search/setSearchWord', this.$route.query.wd)
      }
      if (this.$route.query.cg) {
        this.$store.commit('search/setCurrentCode', this.$route.query.cg)
      }
      this.$store.dispatch('search/getResult')
    }
  }
}
</script>
