<template>
  <Spin :spinning="detailLoading">
    <div class="Product-wrap">
      <template v-if="html || content">
        <ProductApi v-if="templateId === 'PRODUCT_APIS'" />
        <FaqDoc v-else-if="templateId === 'DOC_FAQS'" :faqList="docFaqList" />
        <template v-else>
          <a-row>
            <a-col :sm="24" :md="24" :lg="comAnchorList.length > 1 ? 20 : 24">
              <RenderMarkdown :html="html" @getAnchorList="getAnchorList" />
              <FapPage :faqList="pageFaqList" />
            </a-col>
            <a-col
              :sm="0"
              :md="0"
              :lg="comAnchorList.length > 1 ? 4 : 0"
              style="padding-left: 20px"
              v-if="!isMobile"
            >
              <AnchorList :anchorList="comAnchorList" />
            </a-col>
          </a-row>
        </template>
      </template>
      <div v-else-if="!loading" style="padding-top: 200px">
        <LazyComponent :time="500">
          <a-empty />
        </LazyComponent>
      </div>
    </div>
  </Spin>
</template>
<script>
import RenderMarkdown from '@/components/RenderMarkdown'
import FapPage from '@/components/FapPage'
import FaqDoc from '@/components/FaqDoc'
import utils from '@/utils'
import ProductApi from './ProductApi'
export default {
  components: {
    RenderMarkdown,
    ProductApi,
    FaqDoc,
    FapPage
  },
  data() {
    return {
      anchorList: []
    }
  },
  computed: {
    html() {
      return this.$store.state.open.html
    },
    templateId() {
      return this.$store.state.open.templateId
    },
    content() {
      return this.$store.state.open.content
    },
    isMobile() {
      return this.$store.state.isMobile
    },
    loading() {
      return this.$store.state.loading
    },
    detailLoading() {
      return this.$store.state.open.detailLoading
    },
    pageFaqList() {
      return this.$store.state.open.pageFaqList
    },
    docFaqList() {
      return this.content ? this.content.items : []
    },
    comAnchorList() {
      const { anchorList,  pageFaqList } = this
      return utils.hanleAnchorList(anchorList, pageFaqList)
    }
  },
  methods: {
    getAnchorList(list) {
      this.anchorList = list
    }
  }
}
</script>

<style lang="less" scoped>
.Product-wrap {
  min-height: calc(100vh - 64px);
}
</style>
