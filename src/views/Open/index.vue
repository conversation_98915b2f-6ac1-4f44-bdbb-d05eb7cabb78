<template>
	<div>
		<Component :is="showComponents" />
	</div>
</template>
<script>
import PcLayout from './PcLayout'
import utils from '@/utils'
import MobileLayout from './MobileLayout'
export default {
  components: {
    MobileLayout,
    PcLayout
  },
  data() {
    return {}
  },
  computed: {
    showComponents() {
      return this.$store.state.showComponents
    }
  },
  watch: {
    $route() {
      this.init2()
    },
    '$store.state.langType'() {
      this.init()
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.$store.commit('setRightActive', '2')
      const { path } = this.$route
      const docNoAndLocation = path.replace('/index.html','').split('open')[1]
      let docNo = docNoAndLocation.split('/')[1]
      let location = docNoAndLocation.replace(`/${docNo}`,'')
      const openKeys = utils.getOpenKeys(location)
      this.$store.commit('open/setUpCurrentLocation', openKeys)
      this.$store.commit('open/setDocNo', docNo)
      this.$store.dispatch('open/getMenu', {docNo, location: docNoAndLocation.replace(`/${docNo}/`,'')})
    },
    init2() {
      this.$store.commit('setRightActive', '2')
      const { path } = this.$route
      const docNoAndLocation = path.replace('/index.html','').split('open')[1]
      let docNo = docNoAndLocation.split('/')[1]
      let location = docNoAndLocation.replace(`/${docNo}`,'')
      const openKeys = utils.getOpenKeys(location)
      this.$store.commit('open/setUpCurrentLocation', openKeys)
      this.$store.dispatch('open/getContent', docNoAndLocation.replace(`/${docNo}/`,''))
    }
  }
}
</script>
