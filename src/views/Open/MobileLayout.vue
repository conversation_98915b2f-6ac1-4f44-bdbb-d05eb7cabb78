<template>
  <div class="mobile">
    <MobileHeader :navName="docInfo.docTitle">
      <template v-if="menuList.length > 0">
        <h3 class="mobile-h3-title">{{ docInfo.docTitle }}</h3>
        <a-menu
          :style="{
            overflow: 'hidden',
            borderRight: 0
          }"
          :inlineIndent="16"
          mode="inline"
          :default-selected-keys="[currentLocation]"
          :open-keys="openKeys"
          :selectedKeys="[currentLocation]"
          @openChange="onOpenChange"
          @click="onMenuClick"
        >
          <template v-for="item in menuList">
            <a-menu-item
              class="level-1"
              v-if="!item.children || item.children.length < 1"
              :key="item.location"
            >
              <span>{{ item.title }}</span>
            </a-menu-item>
            <PcMenuItem
              @titleClick="titleClick"
              v-else
              :key="item.location"
              :menu-info="item"
            />
          </template>
        </a-menu>
      </template>
      <template v-else>
        <a-empty
          v-if="!loading"
          style="margin-top: 200px"
          :image="simpleImage"
        />
      </template>
    </MobileHeader>
    <div class="mobile-content2" id="content">
      <Detail />
    </div>
  </div>
</template>
<script>
import PcMenuItem from '@/components/PcMenuItem'
import MobileHeader from '@/components/MobileHeader'
import utils from '@/utils'
import Detail from './Detail'
import { mapState } from 'vuex'
import { Empty } from 'ant-design-vue'
export default {
  beforeCreate() {
    this.simpleImage = Empty.PRESENTED_IMAGE_SIMPLE
  },
  components: {
    Detail,
    MobileHeader,
    PcMenuItem
  },
  data() {
    return {
      collapsed: false,
      busy: false,
      openKeys: []
    }
  },
  watch: {
    upCurrentLocation: {
      handler(newValue) {
        if (newValue) this.openKeys = newValue
      },
      immediate: true
    }
  },
  computed: {
    ...mapState('open', [
      'menuList',
      'docInfo',
      'docNo',
      'upCurrentLocation',
      'currentLocation'
    ]),
    top() {
      return this.$store.state.top
    },
    loading() {
      return this.$store.state.loading
    },
    scrollBarWidth() {
      return this.$store.state.scrollBarWidth
    }
  },
  methods: {
    toggleShowSearch() {
      this.isShowSearch = !this.isShowSearch
    },
    onOpenChange(openKeys) {
      this.openKeys = openKeys
    },
    titleClick({ key }) {
      const item = utils.findProductItem(this.menuList, key)
      const { location, hasContent } = item
      console.log(this.currentLocation)
      if (this.currentLocation !== location && hasContent) {
        this.$router.push(`/docs/open/${this.docNo}/${location}`)
      }
    },
    onMenuClick({ keyPath }) {
      this.$store.commit('setSubMenu', false)
      const item = utils.findProductItem(this.menuList, keyPath[0])
      const { location } = item
      if (this.currentLocation === location) return
      this.$router.push(`/docs/open/${this.docNo}/${location}`)
    }
  }
}
</script>
<style lang="less" scoped>
.pc-menu {
  .api-path {
    word-break: break-all;
    white-space: pre-line;
    font-size: 12px;
    font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
      sans-serif;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.65);
    line-height: 17px;
    margin-bottom: 5px;
  }
  .api-name {
    word-break: break-all;
    height: 17px;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
      sans-serif;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.45);
    line-height: 17px;
  }
}
.search-menu {
  padding: 20px 15px 0;
}
</style>
