<template>
	<Spin :spinning="detailLoading">
		<div class="bg-wrap">
			<div class="bg"></div>
		</div>
		<div
			class="api-content"
			:class="{ 'api-content-lg': !is1440, 'api-content-mobile': isMobile }"
		>
			<div
				class="left"
				:style="{ width: `calc(100vw - ${leftWidth + 300}px)` }"
			>
				<div class="header">
					<h1
						class="header-title"
						id="title"
						:style="{
							width: isMobile ? '67vw' : 'auto',
						}"
					>
						<div style="flex: 1">{{ definition.title }}</div>
						<a
							class="login-btn"
							:href="accessDiagnosisUrl"
							target="_blank"
							v-if="!isMobile"
						>
							<span
								class="icon iconfont icon-zhenduan"
								style="font-size: 14px"
							></span>
							{{ $t('accessDiagnosis') }}
						</a>
						<div
							class="select"
							v-if="historyList.length !== 0"
						>
							<Select
								style="width: 82px; height: 30px"
								v-model="version"
								@on-change="onSelectVersion"
								placement="bottom-end"
							>
								<Option
									v-for="(version, index) in historyList"
									:key="version.apiId"
									:value="version.apiId"
									:label="`Ver${historyList.length - index}.0`"
								>
									<div class="api-history-version-item">
										<div class="api-history-version-item-text">
											<div class="api-history-version-item-text-l">
												{{ `Ver${historyList.length - index}.0` }}
											</div>
											<div class="api-history-version-item-text-r">
												<template v-for="method in version.method.split(',')">
													<div
														:class="method"
														:key="method"
														style="display: inline-block"
													>
														<span class="font-size-10px">{{ method }}</span>
													</div>
												</template>
												{{ version.path }}
											</div>
										</div>
									</div>
								</Option>
							</Select>
						</div>
					</h1>
					<div
						class="time"
						id="time"
						v-if="definition.lastModifiedDate"
					>
						{{ $t('updateTime') }}:{{ definition.lastModifiedDate }}
					</div>
					<div style="margin-top: 16px">
						<a
							class="login-btn"
							:href="accessDiagnosisUrl"
							target="_blank"
							v-if="isMobile"
						>
							<span
								class="icon iconfont icon-zhenduan"
								style="font-size: 14px"
							></span>
							{{ $t('accessDiagnosis') }}
						</a>
					</div>
				</div>
				<div class="header-msg">
					<a-alert
						type="error"
						banner
						style="margin-bottom: 16px"
						v-if="historyList.length > 0 && version !== historyList[0].apiId"
					>
						<div slot="message">
							<i18n path="historyMsg">
								<template #action>
									<span
										style="color: rgba(237, 51, 56, 1); cursor: pointer"
										@click="onSelectVersion(historyList[0].apiId)"
										>{{ $t('newVersion') }}</span
									>
								</template>
							</i18n>
						</div>
					</a-alert>
					<a-alert
						v-if="definition.type && definition.type !== 'COMMON' && definition.type !== 'WEB'"
						style="margin-bottom: 16px"
						type="info"
						:message="$t('typeTip')"
						banner
					/>
				</div>
				<!--接口说明<-->
				<h3 id="anchor1">{{ $t('接口说明') }}</h3>
				<ApiDesc
					:definition="definition"
					:scenesMsg="scenesMsg"
				/>
				<!--使用说明-->
				<h3
					id="anchor2"
					v-if="apiInvokingMsg"
				>
					{{ $t('instructions') }}
				</h3>
				<div
					class="text invoking"
					v-if="apiInvokingMsg"
				>
					<RenderMarkdown :html="apiInvokingMsg" />
				</div>
				<!-- 请求参数 -->
				<h2 id="anchor3">{{ $t('repParams') }}</h2>
				<a-row :gutter="40">
					<a-col
						:sm="24"
						:md="24"
						:lg="12"
            class="print-col-wrap"
					>
						<ParamsHeader
							:tip="$t('commonRepParamsTip')"
							id="anchor3-1"
							:title="$t('reqHeader')"
							:list="common.reqParams"
							:apiGroup="definition.apiGroup"
						/>
						<ParamsBody
							id="anchor3-2"
							v-if="definition.reqParams && definition.reqParams[reqParamsValue]"
							:title="
								definition.method.toUpperCase().includes('GET')
									? $t('reqParameters')
									: $t('reqBody')
							"
							:list="definition.reqParams[reqParamsValue]"
							:apiGroup="definition.apiGroup"
							:type="reqParamsValue"
						/>
					</a-col>
					<a-col
						:sm="24"
						:md="24"
						:lg="12"
            class="code-col-wrap"
					>
						<CodeExm v-if="showCodeExm" />
					</a-col>
				</a-row>
				<!-- 响应参数 -->
				<h2 id="anchor4">{{ $t('respParams') }}</h2>
				<a-row :gutter="40">
					<a-col
						:sm="24"
						:md="12"
            class="print-col-wrap"
					>
						<ParamsHeader
							:tip="$t('commonResParamsTip')"
							id="anchor4-1"
							:title="$t('resHeader')"
							:list="computedCommonRespParams"
							:apiGroup="definition.apiGroup"
						/>
						<ParamsBody
							id="anchor4-2"
							v-if="definition.respParams && definition.respParams[respParamsValue]"
							:title="$t('resBody')"
							:list="definition.respParams[respParamsValue]"
							:apiGroup="definition.apiGroup"
							:type="respParamsValue"
						/>
					</a-col>
					<a-col
						v-if="definition.respExamples && definition.respExamples[respParamsValue]"
						:sm="24"
						:md="12"
            class="code-col-wrap"
					>
						<ResCode
							:title="$t('catRespExamples')"
							:code="definition.respExamples[respParamsValue]"
						/>
					</a-col>
				</a-row>
				<!-- 错误码 -->
				<h2 id="anchor6">{{ $t('errorCode') }}</h2>
				<div
					class="text"
					style="margin-bottom: 12px"
				>
					<i18n path="errorCodeTip">
						<template #action>
							<a
								href="javascript:goToUrl('https://open.yeepay.com/docs/v2/platform/sdk_guide/error_code/index.html', '_blank');"
								>{{ $t('errorCodeTipA') }}</a
							>
						</template>
					</i18n>
				</div>
				<ErrorCodeTable :key="definition.path" />
				<!-- 结果通知 -->
				<h2
					id="anchor7"
					v-if="callbackList.length > 0"
				>
					{{ $t('callback') }}
				</h2>
				<div
					class="text"
					style="margin-bottom: 16px"
					v-if="callbackList.length > 0"
				>
					<i18n path="callbackTip">
						<template #action>
							<a
								href="javascript:goToUrl('https://open.yeepay.com/docs/open/platform-doc/notifys/notify-summary-sm', '_blank');"
								>{{ $t('callbackTipA') }}</a
							>
						</template>
					</i18n>
				</div>
				<div
					class="callback-wrap"
					v-if="callbackList.length > 0"
				>
					<a-tabs @tabClick="checkTabs">
						<a-tab-pane
							v-for="callbackItem in callbackList"
							:key="callbackItem.title"
							:tab="callbackItem.title"
							:id="`${callbackItem.title}`"
						>
							<CallbackTable
								ref="callbackTable"
								:spiName="callbackItem.name"
								:currentApiId="currentApiId"
								@getBaowen="getBaowen"
							/>
							<h4
								v-if="showSpiApiData && showSpiApiData.length > 0"
								id="anchor10"
							>
								{{ $t('apiSearchOrder') }}
							</h4>
							<ApiSearchTable
								v-if="showSpiApiData && showSpiApiData.length > 0"
								key="0dlkkj"
							/>
						</a-tab-pane>
					</a-tabs>
				</div>
				<template v-if="apiFaqList.length > 0">
					<h2 id="anchor8">{{ $t('menu.questionTitle') }}</h2>
					<div class="faq-wrap">
						<FaqItem
							v-for="item in apiFaqList"
							:key="item.id"
							:item="item"
						/>
					</div>
				</template>
			</div>
			<div class="right">
				<ApiToc
					:currentAnchor="currentAnchor"
					:reqParamsValue="reqParamsValue"
					:respParamsValue="respParamsValue"
					:isShowBaowen="isShowBaowen"
					:showSpiApiData="showSpiApiData"
				/>
			</div>
		</div>
	</Spin>
</template>

<script>
import CodeExm from '@/components/CodeExm/newCode'
import CallbackTable from '@/components/CallbackTable/index2.vue'
import ErrorCodeTable from '@/components/ErrorCodeTable'
import ApiSearchTable from '@/components/ApiSearchTable'
import FaqItem from '@/components/FaqItem'
import { Select, Option } from 'view-design'
import RenderMarkdown from '@/components/RenderMarkdown'
import ApiDesc from '@/components/ApiDesc'
import ParamsHeader from '@/components/Params/paramsHeader'
import ParamsBody from '@/components/Params/paramsBody'
import ApiToc from '@/components/ApiToc'
import { mapState } from 'vuex'
import utils, { emtyDesc } from '@/utils'
import { Tabs } from 'ant-design-vue'
export default {
	props: ['leftWidth'],
	components: {
		Select,
		CodeExm,
		CallbackTable,
		RenderMarkdown,
		ErrorCodeTable,
		ApiSearchTable,
		FaqItem,
		Option,
		[Tabs.name]: Tabs,
		[Tabs.TabPane.name]: Tabs.TabPane,
		ApiDesc,
		ParamsHeader,
		ParamsBody,
		ApiToc,
	},
	data() {
		return {
			version: '',
			currentAnchor: '',
			isShowBaowen: false,
			respParamsValue: 'application/json',
			reqParamsValue: 'application/x-www-form-urlencoded',
			showSpiApiData: [],
			apiBasicData: {},
			spiApiMapData: {},
			hrefMap: {
				'error-code': '#anchor6',
				'error-msg': '#anchor6',
				'notify-url': '#anchor7',
			},
			activeKey: '',
		}
	},
	computed: {
		...mapState('apiDocs', [
			'subMenuApiId',
			'currentApiId',
			'definition',
			'pageNo',
			'common',
			'resHeader',
			'scenesMsg',
			'invokingMsg',
			'menuList',
			'itemsList',
			'historyList',
			'detailLoading',
			'callbackList',
			'apiFaqList',
		]),
		apiInvokingMsg() {
			if (emtyDesc(this.invokingMsg)) {
				return this.invokingMsg
			}
			return ''
		},
		isMobile() {
			return this.$store.state.isMobile
		},
		subMenuList() {
			return this.$store.getters.subMenuList
		},
		showCodeExm() {
			const { sampleCodes = {} } = this.definition
			return Object.keys(sampleCodes).length > 0
		},
		computedCommonRespParams() {
			if (!this.resHeader[this.respParamsValue]) {
				return this.common.respParams
			}
			if (!this.common.respParams) {
				return this.resHeader[this.respParamsValue]
			}
			return this.common.respParams.concat(this.resHeader[this.respParamsValue])
		},
		accessDiagnosisUrl() {
			const hostname = window.location.hostname
			if (hostname === 'open.yeepay.com') {
				return `https://mp.yeepay.com/auth/signin?redirectUrl=https://mp.yeepay.com/yop-developer-center/cas?redirectUrl=https://mp.yeepay.com/mp-developer-center/index.html#/dev-services/access-diagnosis?apiId=${this.currentApiId}`
			}
			return `https://qamp.yeepay.com/auth/signin?redirectUrl=https://qamp.yeepay.com/yop-developer-center/cas?redirectUrl=https://qamp.yeepay.com/mp-developer-center/index.html#/dev-services/access-diagnosis?apiId=${this.currentApiId}`
		},
		is1440() {
			return this.$store.state.is1440
		},
	},
	watch: {
		'definition.reqParams': {
			handler(newValue) {
				if (!newValue) return
				this.reqParamsValue = Object.keys(newValue)[0]
			},
			immediate: true,
		},
		'definition.respParams': {
			handler(newValue) {
				if (!newValue) return
				this.respParamsValue = Object.keys(newValue)[0]
			},
			immediate: true,
		},
		currentApiId: {
			handler(newValue) {
				this.version = newValue
			},
			immediate: true,
		},
		'$store.state.apiDocs.showSpiApiData': {
			handler(newValue) {
				this.showSpiApiData = newValue
			},
			immediate: true,
		},
		// api 基本信息json
		'$store.state.apiDocs.apiBasicData': {
			handler(newValue) {
				this.apiBasicData = newValue
			},
			immediate: true,
		},
		'$store.state.apiDocs.spiApiMapData': {
			handler(newValue) {
				this.spiApiMapData = newValue
			},
			immediate: true,
		},
	},
	methods: {
		checkTabs(row) {
			let curSpi = this.callbackList.filter((element) => element.title === row)
			let spiName = curSpi[0].name
			// 直接读取vuex 数据 筛选对应表单的内容
			let currShowSpiApiData = []
			if (this.spiApiMapData[spiName]) {
				for (let key in this.apiBasicData) {
					this.spiApiMapData[spiName].forEach((item) => {
						if (key === item) {
							currShowSpiApiData.push(this.apiBasicData[key])
						}
					})
				}
			}
			this.$store.commit('apiDocs/setShowSpiApiData', currShowSpiApiData)
		},
		onSelectVersion(value) {
			this.version = value
			const api = utils.findHistoryApi(this.itemsList, value)
			this.$store.dispatch('apiDocs/init', {
				apiId: value,
				spiCount: api.spiCount,
			})
		},
		handleClick(e) {
			e.preventDefault()
		},
		onAnchorChange(currentAnchor) {
			this.currentAnchor = currentAnchor
		},
		getBaowen(show) {
			this.isShowBaowen = show
		},
	},
}
</script>

<style lang="less">
.api-desc-wrap {
	ul > li {
		list-style: disc inside !important;
	}

	ol > li {
		list-style: decimal inside !important;
	}
}

.ivu-table-wrapper {
	.ivu-table-cell {
		display: flex;
		align-items: center;

		.ivu-table-cell-tree {
			padding: 10px;
		}

		.param-con {
			position: relative;
			padding: 2px;

			.param-name {
				display: inline-block;
				max-width: 230px;
			}

			.required-text {
				position: absolute;
				top: 4px;
				display: inline-block;
				padding: 0px 2px;
				width: max-content;
				background-color: rgba(255, 101, 46, 0.1);
				color: #ff652e;
				border-radius: 2px;
				font-size: 12px;
			}
		}
	}

	.htip {
		width: 14px;
		height: 16px;
		display: inline-block;
		line-height: 16px;
		vertical-align: middle;
		cursor: pointer;
		margin-left: 4px;

		svg {
			width: 14px;
			height: 14px;
		}
	}

	.ivu-table-row-hover {
		td {
			background: rgba(0, 0, 0, 0.02) !important;
		}
	}

	.ivu-table-cell-tree {
		border: none;
		background: transparent;
		padding-left: 6px;
		height: inherit;
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		display: flex;
		align-items: center;
	}

	.ivu-table-cell-tree:hover {
		.ivu-icon-ios-add:before {
			border-left-color: rgba(0, 0, 0, 0.65);
		}

		.ivu-icon-ios-remove:before {
			border-top-color: rgba(0, 0, 0, 0.65);
		}
	}

	.ivu-icon-ios-add:before {
		content: '';
		display: inline-block;
		width: 0;
		height: 0;
		margin-top: -3px;
		border: 4px solid transparent;
		border-left-color: rgba(0, 0, 0, 0.35);
		vertical-align: middle;
	}

	.ivu-icon-ios-remove:before {
		content: '';
		display: inline-block;
		width: 0;
		height: 0;
		border: 4px solid transparent;
		border-top-color: rgba(0, 0, 0, 0.35);
	}
}

.invoking {
	.vditor-reset p {
		margin-bottom: 0;
		line-height: 22px;
	}
}

// 请求参数 响应参数 列表
.ivu-table-header th.scerate-box {
	.ivu-table-cell {
		width: 100%;
		padding: 0;
		cursor: pointer;

		.ivu-poptip {
			width: 100%;
		}
	}
}
</style>
<style lang="less" scoped>
.bg-wrap {
	position: relative;
	.bg {
		position: absolute;
		top: 0px;
		right: 0;
		left: 0;
		background: url('../../assets/apiDetailBg.png') no-repeat right;
		height: 351px;
		background-size: auto 351px;
	}
}

.header {
	margin-bottom: 32px;
	font-family: PingFangSC, PingFang SC;

	.header-title {
		font-weight: 500;
		font-size: 24px !important;
		margin-bottom: 0 !important;
		color: rgba(0, 0, 0, 0.85);
		line-height: 33px;
		text-align: left;
		font-style: normal;
		display: flex;
		align-items: center;

		.login-btn {
			height: 20px;
			font-weight: 500;
			font-size: 14px;
			color: #52bf63;
			line-height: 20px;
			font-style: normal;
			display: flex;
			align-items: center;
			.icon {
				margin-right: 4px;
			}
		}
	}

	h1 {
		display: inline-block;
	}

	.time {
		height: 14px;
		font-size: 14px;
		font-weight: 400;
		color: rgba(0, 0, 0, 0.45);
		line-height: 12px;
		margin-top: 8px;
	}

	.select {
		margin-left: 16px;
		margin-top: -4px;
		.api-history-version-item {
			box-sizing: border-box;
			min-height: 32px;
			font-size: 14px;
			font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun, sans-serif;
			font-weight: 400;
			line-height: 32px;
			padding-left: 12px;
			cursor: pointer;
		}

		.api-history-version-item-text {
			box-sizing: border-box;
			display: inline-block;
			vertical-align: middle;
			margin-right: 6px;
			box-sizing: border-box;
		}

		.api-history-version-item-text-l {
			width: 44px;
			box-sizing: border-box;
			display: inline-block;
			vertical-align: text-top;
			height: 22px;
			font-size: 14px;
			font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun, sans-serif;
			font-weight: 400;
			line-height: 22px;
		}

		.api-history-version-item-text-r {
			display: inline-block;
			line-height: 22px;
		}

		/deep/ .ivu-select-single .ivu-select-selection .ivu-select-placeholder,
		.ivu-select-single .ivu-select-selection .ivu-select-selected-value {
			height: 30px;
			line-height: 30px;
		}

		/deep/ .ivu-select-single .ivu-select-selection {
			height: 30px;
			line-height: 30px;
		}

		/deep/ .ivu-select-single .ivu-select-selection .ivu-select-selected-value {
			height: 30px;
			line-height: 30px;
		}
	}
}

.header-msg {
	margin-bottom: 12px;

	.header-msg-path {
		font-size: 18px;
		font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun, sans-serif;
		font-weight: 500;
		color: #333333;
		line-height: 18px;
		margin-bottom: 12px;
		word-break: break-all;
	}
}
.api-content {
	box-sizing: border-box;
	padding: 48px 0 48px 56px;
	display: flex;
	position: relative;
	z-index: 1;
	.left {
		flex: 1;
		width: 100%;
	}
	.right {
		width: 244px;
		padding-left: 48px;
	}
}
.api-content-lg {
	padding-right: 72px;
	.right {
		width: 0;
		padding: 0;
	}
}
.api-content-mobile {
	padding: 40px 24px 24px !important;
}

.callback-wrap {
	/deep/ .ant-tabs {
		.ant-tabs-bar {
			margin-bottom: 0;
		}

		.ant-tabs-content {
			border-top: none;
		}

		.ant-tabs-tab {
			margin-right: 15px;
			padding: 12px 0;
		}
	}
}
</style>
