<template>
  <div class="table-wrap">
    <a-table
      :columns="columns"
      :data-source="tableList"
      bordered
      :pagination="false"
      rowKey="location"
    >
      <a :href="getLink(record)" slot="name" slot-scope="text, record">{{
        text
      }}</a>
    </a-table>
  </div>
</template>

<script>
import utils from '@/utils'
export default {
  props: ['list'],
  data() {
    return {
      columns: [
        {
          title: () => this.$t('productName'),
          width: '220px',
          dataIndex: 'title',
          customRender: (value, row) => {
            return {
              children: <a href={this.getLink(row)}>{value}</a>,
              attrs: {
                rowSpan: row.rowSpan
              }
            }
          }
        },
        {
          title: () => this.$t('moduleName'),
          width: '220px',
          dataIndex: 'name',
          scopedSlots: { customRender: 'name' }
        },
        {
          title: () => this.$t('desc'),
          dataIndex: 'desc'
        }
      ]
    }
  },
  computed: {
    tableList() {
      return utils.handlerApiOverview(this.list)
    },
    itemsList() {
      return this.$store.state.apiDocs.itemsList
    }
  },
  methods: {
    getLink({ location }) {
      const item = this.itemsList.find(
        item => item.location.indexOf(location) !== -1
      )
      // 精简链接内容
      item.linkLocation = `${item.docNo}/${item.pageNo}`
      if (!item) return '#'
      const url = window.location.href
      let firstUrl = 'docs'
      if (url.indexOf('docs-v2') !== -1) {
        firstUrl = 'docs-v2'
      }
      return `/${firstUrl}/apis/${item.linkLocation}`
    }
  }
}
</script>
<style lang="less" scoped>
.table-wrap {
  width: 100%;
  overflow-y: auto;
}
</style>
