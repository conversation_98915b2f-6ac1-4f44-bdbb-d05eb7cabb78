<template>
  <div>
    <Component :is="showComponents" />
  </div>
</template>
<script>
import PcLayout from './PcLayout'
import MobileLayout from './MobileLayout'
export default {
  components: {
    MobileLayout,
    PcLayout
  },
  data() {
    return {
      startTime: Date.now()
    }
  },
  computed: {
    showComponents() {
      return this.$store.state.showComponents
    }
  },
  watch: {
    $route(newvalue, oldvalue) {
      if(newvalue.path === oldvalue.path) return
      this.init2()
    },
    '$store.state.langType'() {
      this.init()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init(needMenu = true) {
      const { path } = this.$route
      let location = path.split('apis/')[1] || ''
      location = location.replace('/index.html', '')
      // 兼容老链接代码(/docs/v2/products/fwssfk/apis/options__yos__v1.0__sys__merchant__qual__upload/index.html)
      const product = path.match(/\/docs\/v2\/products\/(.*?)\/apis/)
      if (product && product[1]) {
        location = `${product[1]}/${location}`
      }
      if (needMenu) {
        this.$store.dispatch('apiDocs/getMenu', location)
      } else {
        this.$store.dispatch('apiDocs/getConent', location)
      }
    },
    init2() {
      this.$upLog(119, {
        stayTime: Math.floor((Date.now() - this.startTime) / 1000)
      })
      this.startTime = Date.now()
      const { path } = this.$route
      if (path === '/docs') {
        this.$store.commit('apiDocs/setSubMenuActive', '')
        this.$store.commit('apiDocs/setMenuActive', [])
        return
      }
      this.init(false)
    }
  }
}
</script>
