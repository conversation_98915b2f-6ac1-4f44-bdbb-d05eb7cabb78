<template>
	<div class="simple-wrap" style="margin-top: 2px">
		<a-layout id="components-layout" class="pc">
			<div id="pc-left-menu" v-if="!collapsed">
				<a-layout-sider
					width="230"
					:style="{
						height: '100%',
						background: '#fff',
					}"
					v-show="!collapsed"
					:trigger="null"
					collapsible
				>
					<div class="search-menu" style="margin-bottom: 16px">
						<a-input
							allowClear
							ref="searchInput"
							:value="keyWord"
							:maxLength="100"
							:placeholder="$t('keyWordSearch')"
							@change="onChange"
							@search="onChange"
						>
							<template #prefix><a-icon type="search" /></template>
						</a-input>
					</div>
					<template v-if="filterMenuList.length > 0">
						<a-menu
							:style="{
								overflow: 'hidden',
								borderRight: 0,
							}"
							:inlineIndent="16"
							mode="inline"
							:open-keys="openKeys"
							:selectedKeys="subMenuApiId ? [subMenuApiId] : []"
							@openChange="onOpenChange"
							@click="onMenuClick"
						>
              <ApiMenuItem v-for="menu in filterMenuList" :menu-info="menu" :key="menu.location" />
						</a-menu>
					</template>
					<template v-else>
						<a-empty v-if="!loading" style="margin-top: 20px" />
					</template>
				</a-layout-sider>
			</div>
			<a-layout id="pc-right-content">
				<div>
					<Detail v-if="subMenuApiId" :leftWidth="leftWidth"/>
					<LazyComponent v-else :time="500">
						<ApiOverview />
					</LazyComponent>
				</div>
			</a-layout>
		</a-layout>
		<div
			class="trigger-wrap"
			@click="() => (collapsed = !collapsed)"
			:style="{
				left: !collapsed ? '213px' : 0,
				borderRadius: !collapsed ? '50%' : '0 50% 50% 0',
			}"
		>
			<a-icon class="trigger" :type="collapsed ? 'menu-unfold' : 'menu-fold'" />
		</div>
	</div>
</template>
<script>
import mixin from '@/mixins'
import Detail from './DetailWrap'
export default {
	mixins: [mixin],
	components: {
		Detail,
	},
	data() {
		return {
			collapsed: false,
			busy: false,
			isShowSearch: false,
		}
	},
	watch: {
		isShowSearch(newValue) {
			if (newValue) {
				this.$nextTick(() => {
					this.$refs.searchInput.focus()
				})
			}
		},
	},
	computed: {
		top() {
			return this.$store.state.top
		},
		loading() {
			return this.$store.state.loading
		},
		scrollBarWidth() {
			return this.$store.state.scrollBarWidth
		},
		leftWidth() {
			return (this.collapsed ? 0 : 230) + this.scrollBarWidth
		},
	},
	methods: {
		toggleShowSearch() {
			this.isShowSearch = !this.isShowSearch
		},
	},
}
</script>
<style lang="less" scoped>
.pc-menu {
	.api-path {
		word-break: break-all;
		white-space: pre-line;
		font-size: 12px;
		font-family: PingFangSC-Medium, PingFang SC, "Microsoft YaHei", SimSun, sans-serif;
		font-weight: 500;
		color: rgba(0, 0, 0, 0.65);
		line-height: 17px;
		margin-bottom: 5px;
	}
	.api-name {
		word-break: break-all;
		height: 17px;
		font-size: 12px;
		font-family: PingFangSC-Regular, PingFang SC, "Microsoft YaHei", SimSun, sans-serif;
		font-weight: 400;
		color: rgba(0, 0, 0, 0.45);
		line-height: 17px;
	}
}
.search-menu {
	padding: 20px 15px 0;
}
#components-layout {
	display: flex;
	background-color: #fff;
	#pc-left-menu {
		box-shadow: 1px 0px 0px 0px rgba(0, 0, 0, 0.09);
		width: 230px;
    height: calc(100vh - 67px);
    flex-shrink: 0;
    position: sticky;
    top: 66px;
	}
	#pc-right-content {
		flex: 1;
		background-color: #fff;
	}
}
</style>
