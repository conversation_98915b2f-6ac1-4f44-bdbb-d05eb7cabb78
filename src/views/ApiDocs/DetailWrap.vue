<template>
  <div>
    <div v-if="detailVer === 'old'" class="old-ver-wrap" :style="{ width: `calc(100vw - ${leftWidth}px)`}">
     <div style="background-color: #fff;">
      <DetailOld v-bind="$props" />
     </div>
    </div>
    <Detail v-else v-bind="$props" />
  </div>
</template>
<script>
import DetailOld from './DetailOld'
import Detail from './Detail'
export default {
  props: ['leftWidth'],
  components: {
    DetailOld,
    Detail
  },
  computed: {
    detailVer() {
      return this.$store.state.apiDocs.detailVer
    }
  }
}

</script>
<style lang="less" scoped>
.old-ver-wrap {
  background: #f0f2f5;
  padding: 16px
}
</style>