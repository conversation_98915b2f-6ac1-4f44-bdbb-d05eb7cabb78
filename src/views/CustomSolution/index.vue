<template>
	<div>
		<Component :is="showComponents" />
	</div>
</template>
<script>
import PcLayout from './PcLayout'
import utils from '@/utils'
import { mapState } from 'vuex'
import MobileLayout from './MobileLayout'
export default {
  components: {
    MobileLayout,
    PcLayout
  },
  data() {
    return {}
  },
  computed: {
    ...mapState('solutions', [
      'menuList',
    ]),
    showComponents() {
      return this.$store.state.showComponents
    }
  },
  watch: {
    $route(newvalue, oldvalue) {
      if(newvalue.path === oldvalue.path) return
      this.init2()
    },
    '$store.state.langType'() {
      this.init()
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.$store.commit('setRightActive', '2')
      const { path } = this.$route
      const docNoAndLocation = path.replace('/index.html','').split('solutions')[1]
      let docNo = docNoAndLocation.split('/')[1]
      let location = docNoAndLocation.replace(`/${docNo}`,'')
      this.$store.commit('solutions/setDocNo', docNo)
      this.$store.dispatch('solutions/getMenu', { docNo, location: docNoAndLocation.replace(`/${docNo}/`,'') })
        .then(() => {
          if (location.includes('apis')) {
            location = utils.findProductItem(this.menuList, location.replace('/', '')).originLocation
            if(!location) return
            location = '/' + location
            const openKeys = utils.getOpenKeys(location)
            this.$store.commit('solutions/setUpCurrentLocation', openKeys)
          }
      })
    },
    init2() {
      this.$store.commit('setRightActive', '2')
      const { path } = this.$route
      const docNoAndLocation = path.replace('/index.html','').split('solutions')[1]
      let docNo = docNoAndLocation.split('/')[1]
      let location = docNoAndLocation.replace(`/${docNo}`, '')
      if (location.includes('apis')) {
        location = utils.findProductItem(this.menuList, location.replace('/', '')).originLocation
        location = '/' + location
      }
      const openKeys = utils.getOpenKeys(location)
      this.$store.commit('solutions/setUpCurrentLocation', openKeys)
      this.$store.dispatch('solutions/getContent', docNoAndLocation.replace(`/${docNo}/`,''))
    }
  }
}
</script>
