<template>
  <div class="problem-wrap">
    <RenderMarkdown :html="docs" />
  </div>
</template>

<script>
import apiGuide from '@/api/guide'
import RenderMarkdown from '@/components/RenderMarkdown'
export default {
  components: {
    RenderMarkdown
  },
  data() {
    return {
      docs: ''
    }
  },
  watch: {
    '$store.state.langType'() {
      this.getInfo()
    }
  },
  created() {
    this.getInfo()
  },
  methods: {
    getInfo() {
      apiGuide.getInfo().then(res => {
        const { docVersion } = res.data
        this.getHtml(docVersion)
      })
    },
    getHtml(docVersion) {
      apiGuide
        .getHtml({
          location: 'faq/faq',
          docVersion
        })
        .then(res => {
          this.docs = res
        })
    }
  }
}
</script>

<style lang="less" scoped>
.problem-wrap {
  min-height: calc(100vh - 64px);
}
</style>
