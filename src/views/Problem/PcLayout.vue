<template>
  <div>
    <a-layout id="components-layout" class="pc">
      <a-layout style="margin-top: 2px">
        <a-layout
          :style="{
            padding: '16px',
            display: 'block'
          }"
        >
          <div
            :style="{
              background: '#fff',
              padding: '24px',
              paddingTop: 0
            }"
          >
            <Detail />
          </div>
        </a-layout>
      </a-layout>
    </a-layout>
  </div>
</template>
<script>
import Detail from './Detail'
export default {
  components: {
    Detail
  },
  data() {
    return {}
  },
  computed: {
    top() {
      return this.$store.state.top
    },
    scrollBarWidth() {
      return this.$store.state.scrollBarWidth
    }
  },
  methods: {}
}
</script>
<style lang="less" scoped>
.pc-menu {
  .api-path {
    word-break: break-all;
    white-space: pre-line;
    font-size: 12px;
    font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
      sans-serif;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.65);
    line-height: 17px;
    margin-bottom: 5px;
  }
  .api-name {
    word-break: break-all;
    height: 17px;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
      sans-serif;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.45);
    line-height: 17px;
  }
}
.search-menu {
  padding: 20px 15px 0;
}
</style>
