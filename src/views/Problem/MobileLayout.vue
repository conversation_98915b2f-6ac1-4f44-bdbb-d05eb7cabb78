<template>
	<div class="mobile">
		<MobileHeader :navName="$t('menu.questionTitle')" />
		<div class="mobile-content2" id="content">
			<Detail />
		</div>
	</div>
</template>

<script>
import MobileHeader from '@/components/MobileHeader'
import Detail from './Detail'
export default {
  components: {
    Detail,
    MobileHeader
  },
  data() {
    return {}
  },
  watch: {},
  methods: {}
}
</script>
