<template>
  <div>
    <Component :is="showComponents" />
  </div>
</template>
<script>
import PcLayout from './PcLayout'
import MobileLayout from './MobileLayout'
export default {
  components: {
    MobileLayout,
    PcLayout
  },
  data() {
    return {}
  },
  computed: {
    showComponents() {
      return this.$store.state.showComponents
    }
  },
  watch: {
    $route(newvalue, oldvalue) {
      if(newvalue.path === oldvalue.path) return
      this.init2()
    },
    '$store.state.langType'() {
      this.init()
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      const { path } = this.$route
      const location = path.replace('/index.html', '').split('platform/')[1]
      if (location) {
        const arr = location.split('/')
        this.$store.commit('guide/setUpCurrentLocation', arr[0])
        this.$store.dispatch('guide/getMenu', location)
      } else {
        this.$store.dispatch('guide/getMenu')
      }
    },
    init2() {
      const { path } = this.$route
      const location = path.replace('/index.html', '').split('platform/')[1]
      if (location) {
        const arr = location.split('/')
        this.$store.commit('guide/setUpCurrentLocation', arr[0])
        this.$store.dispatch('guide/getContent', location)
      }
    }
  }
}
</script>
