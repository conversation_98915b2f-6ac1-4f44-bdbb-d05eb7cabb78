import platform from 'platform'
export default {
  findHistoryApi,
  addMenuTier,
  getOpenKeys,
  handlerApiList,
  handlerApiOverview,
  findProductItem,
  findChild,
  getCustomUserId,
  addMenuFaq,
  hanleAnchorList,
  ignoreUrl,
  filterProductMenuList,
  formateApiMenu
}
function findHistoryApi(list, apiId) {
  let api = {}
  for (let i = 0; i < list.length; i++) {
    if (list[i].apiId === apiId) {
      api = list[i]
      break
    }
  }
  return api
}
// post__，options__，get__，不影响api匹配
function apiUriCompar(uri, comparUri) {
  if (!/^apis/.test(uri) || !/^apis/.test(comparUri)) {
    return false
  }
  let newUri = uri.split('/').pop()
  let newComparUri = comparUri.split('/').pop()
  if (/(^get__)|(^post__)|(^options__)/.test(newComparUri)) {
    newUri = newUri.replace(/^get__/, '__')
    .replace(/^post__/, '__')
    .replace(/^options__/, '__')
    newComparUri = newComparUri.replace(/^get__/, '__')
    .replace(/^post__/, '__')
    .replace(/^options__/, '__')
    return newUri === newComparUri
  }
  return newUri === newComparUri
}
function findProductItem(list, location) {
  let item = ''
  if (!location) return list[0]
  for (let i = 0; i < list.length; i++) {
    if (list[i].location === location || list[i].originLocation === location) {
      item = list[i]
      break
    }
    if (apiUriCompar(list[i].location, location)) {
      item = list[i]
      break
    }
    if (list[i].children && list[i].children.length > 0 && !item) {
      item = findProductItem(list[i].children, location)
    }
  }
  return item
}
function findChild(list, location) {
  let item = ''
  let ignorePageNo = ''
  if (!location) return list[0]
  for (let i = 0; i < list.length; i++) {
    let locationSplitArr = list[i].location.split('/')
    let pageNo = locationSplitArr[locationSplitArr.length -1]
    ignorePageNo = `/${pageNo}`
    if(ignorePageNo.includes('/post__') || ignorePageNo.includes('/get__') || ignorePageNo.includes('/options__')) {
      if (ignoreUrl(`${ignorePageNo}`) === ignoreUrl(`/${location}`)) {
        item = list[i]
        break
      }
    }
    if (pageNo === location) {
      item = list[i]
      break
    }
    if (list[i].children && list[i].children.length > 0 && !item) {
      item = findChild(list[i].children, location)
    }
  }
  return item
}
function addMenuTier(list) {
  for (let i = 0; i < list.length; i++) {
    if (!list[i].children || list[i].children.length === 0) {
      list[i].maxTier = 1
    } else {
      list[i].maxTier = findTier(list[i].children, 1)
    }
  }
}
function findTier(list, num) {
  const item = list.find(item => item.children && item.children.length > 0)
  if (!item) {
    return num + 1
  }
  return findTier(item.children, num + 1)
}
function getOpenKeys(str) {
  const arr = []
  if (!str) return []
  const stringArr = str.split('/')
  if (stringArr.length < 2) return []
  for (let i = 1; i < stringArr.length; i++) {
    let key = stringArr.slice(1, i + 1).join('/')
    arr.push(key)
  }
  return arr
}

function handlerApiList(list) {
  if(!list || !Array.isArray(list)) return []
  const arr = []
  list.forEach(item => {
    const { name, items } = item
    if (items.length < 1) return
    items.forEach((subItem, index) => {
      arr.push({
        ...subItem,
        name,
        extend: {
          scenes:
            subItem.extend.scenes === '无\n' || subItem.extend.scenes === '无'
              ? ''
              : subItem.extend.scenes
        },
        rowSpan: index === 0 ? items.length : 0
      })
    })
  })
  return arr
}
function handlerApiOverview(list) {
  const arr = []
  list.forEach(item => {
    const { name, children: items } = item
    if (items.length < 1) return
    if (items.length === 1) {
      arr.push({
        location: item.location,
        title: name,
        name: '',
        desc: ''
      })
      return
    }
    items.forEach((subItem, index) => {
      arr.push({
        desc: subItem.desc,
        name: subItem.name,
        location: `${item.location}/${subItem.code}`,
        title: name,
        rowSpan: index === 0 ? items.length : 0
      })
    })
  })
  return arr
}

function getCustomUserId() {
  const info = platform.parse(window.navigator.userAgent)
  const id = `${info.name}-${info.version}-${info.os}-${new Date().getTime()}`
  return window.btoa(id)
}

function addMenuFaq(menu, docNo) {
  menu.push({
    contentType: 'JSON',
    contentUri: `/docs/faqs/doc/${docNo}/faqs.json`,
    hasContent: true,
    hasRef: false,
    location: 'faqs',
    templateId: 'DOC_FAQS',
    title: '常见问题',
  })
}

function hanleAnchorList(anchorList, pageFaqList) {
  const pageFaqListAnchor = []
  if(pageFaqList.length > 0) {
    pageFaqListAnchor.push(
      {
        id: '#faqListAnchor',
        title: '常见问题',
        children: pageFaqList.map(item => {
          return {
            id: `#${item.title}faq`,
            title: item.title,
            children: [] 
          }
        })
      }
    )
  }
  return [...anchorList, ...pageFaqListAnchor]
}
// 文档链接开头 忽略/post__、/get__、/options__ 函数处理
function ignoreUrl(url) {
  return url.replace(/(\/post__|\/get__|\/options__)/, function($0, $1) {
    return {
      '/post__' : '',
      '/get__' : '',
      '/options__' : '',
    }[$1]
  })
}
function filterProductMenuList(list, filterList) {
  if(filterList.length < 1) return list
  let newList = []
  for (let i = 0; i < list.length; i++) {
    if (!filterList.includes(list[i].pageId)) {
      if (list[i].children && list[i].children.length > 0) {
        list[i].children = filterProductMenuList(list[i].children, filterList.filter(f => f !== list[i].pageId))
      }
      newList.push(list[i])
    }
  }
  return newList
}
function formateApiMenu(list) {
  list.forEach(l => {
    if (l.items && l.items.length > 0) {
      l.items.forEach(item => {
        const { uri, location } = item
        item.originLocation = `apis/${location.substring(location.indexOf('/') + 1)}`
        item.location = `apis/${uri}`
      })
    }
  })
}
export function emtyDesc(text = '') {
  return text
  .replace('无', '')
  .replace('\n', '')
  .replace('//填写API的主要功能。发布后将展示到产品详情“API列表”的描述字段中//', '')
  .replace('//调用该接口和服务需要有哪些特殊的要点和注意事项//', '')
  .replace('//填写接口调用说明、注意事项等相关信息。发布后将展示到“API详情”页面的“使用说明”字段中//', '')
  .replace('<div class="markdown-con">', '')
  .replace('</div>', '')
  .trim()
}

export function formateA(dom) {
  const arr = dom.querySelectorAll('a')
  arr.forEach(a => {
    let href = a.getAttribute('href')
    if(!href) return
    if (href.indexOf('javascript:goToUrl') !== -1 || href.indexOf('#anchor') !== -1) {
      return
    }
    const target = a.getAttribute('target')
    if (href.indexOf('http') === -1 && /^\/.*/.test(href)) {
      href = `${window.location.origin}${href}`
    }
    a.setAttribute('href', `javascript:goToUrl('${href}', '${target}');`)
    a.removeAttribute('target')
  })
}
export function goApifoxUrl({ apifoxMap, operationId, definition }) {
  const projectId = apifoxMap['####projectId####']
  const debugUrlAllTemplate = apifoxMap['####debugUrlAllTemplate####']
  const debugUrlTemplate = apifoxMap['####debugUrlTemplate####']
  let url = 'https://apifox.com/apidoc/project-3155515'
  if (projectId) {
    if (debugUrlTemplate) {
      let apiFoxId = ''
      if (operationId) {
        apiFoxId = apifoxMap[operationId]
      }
      if (!apiFoxId) {
        apiFoxId = apifoxMap[definition.path]
      }
      if (apiFoxId) {
        url = debugUrlTemplate
          .replace('$projectId', projectId)
          .replace('$apiFoxId', apiFoxId)
      }
    } else if (debugUrlAllTemplate) {
      url = debugUrlAllTemplate
        .replace('$projectId', projectId)
    }
  }
  window.open(url)
}