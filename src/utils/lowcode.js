// import { createService } from '@/utils/request'
// const request = createService('/pmc-server')

const fetchSchema = async (pageCode) => {
  const origin = window.location.origin
  let jsonUrl = ''
  if (origin === 'https://open.yeepay.com') {
    jsonUrl = `https://img.yeepay.com/pmc-static/pmc/page/config/${pageCode}_RELEASE.json`
  } else if (origin === 'https://ncsrs.yeepay.com') {
    jsonUrl = `https://img.yeepay.com/pmc-static/pmc/page/config/${pageCode}_SNAPSHOT.json`
  } else {
    jsonUrl = `https://qastaticres.yeepay.com/ptyfb-fmc-static/pmc/page/config/${pageCode}_RELEASE.json`
  }
  // const res = await request.get(`https://qastaticres.yeepay.com/ptyfb-fmc-static/pmc/page/config/test_page.json`)
  return fetch(jsonUrl, {
    cache: 'no-cache'
  }).then((response) => {
    return response.json().then((data) => {
      if (data && data.newestContentUrl) {
        return fetch(data.newestContentUrl).then((res) => {
          return res.json().then((data) => {
            return data
          })
        })
      }
    })
  })
}

export {
  fetchSchema
}