import Vue from 'vue'
// ant-design-vue
import {
  message,
  Button,
  Modal,
  Table,
  Menu,
  Spin,
  Input,
  ConfigProvider,
  Empty,
  Popover,
  Layout,
  Alert,
  Anchor,
  Result,
  Icon,
  Radio,
  Row,
  Col,
  Tooltip,
  Pagination
} from 'ant-design-vue'
message.config({
  top: '80px',
  duration: 2,
  maxCount: 3
})
Vue.prototype.$message = message
Vue.use(Modal)
Vue.component(Button.name, Button)
Vue.component(Alert.name, Alert)
Vue.component(Result.name, Result)
Vue.component(Table.name, Table)
Vue.component(Menu.name, Menu)
Vue.component(Menu.SubMenu.name, Menu.SubMenu)
Vue.component(Menu.Item.name, Menu.Item)
Vue.component(Menu.ItemGroup.name, Menu.ItemGroup)
Vue.component(Spin.name, Spin)
Vue.component(Input.name, Input)
Vue.component(Input.Search.name, Input.Search)
Vue.component(Input.TextArea.name, Input.TextArea)
Vue.component(ConfigProvider.name, ConfigProvider)
Vue.component(Tooltip.name, Tooltip)
Vue.component(Popover.name, Popover)
Vue.component(Empty.name, Empty)
Vue.component(Layout.name, Layout)
Vue.component(Layout.Sider.name, Layout.Sider)
Vue.component(Icon.name, Icon)
Vue.component(Anchor.name, Anchor)
Vue.component(Anchor.Link.name, Anchor.Link)
Vue.component(Row.name, Row)
Vue.component(Col.name, Col)
Vue.component(Radio.name, Radio)
Vue.component(Radio.Button.name, Radio.Button)
Vue.component(Radio.Group.name, Radio.Group)
Vue.component(Pagination.name, Pagination)
