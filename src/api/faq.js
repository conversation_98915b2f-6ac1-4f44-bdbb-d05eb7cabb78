import { get, post } from './request'

export default {
  feedback(params) {
    return post('/commons/doc/feedback', params)
  },
  // 文档-常见问题列表
  getDocFaqList(params) {
    const url = `/docs/faqs/doc/${params.docNo}/faqs.json`
    return handleFaq(url)
  },
  // 页面常见问题列表
  getPageFaqList(params) {
    const url = `/docs/faqs/page/${params.pageId}/faqs.json`
    return handleFaq(url)
  },
  // API常见问题列表
  getApiFaqList(params) {
    const url = `/docs/faqs/api/${params.apiId}/faqs.json`
    return handleFaq(url)
  },
}

function handleFaq(url) {
  return new Promise((resolve) => {
    get(url)
    .then(res => {
      if(res.items && res.items.length > 0) {
        resolve(res.items)
      } else {
        resolve([])
      }
    })
    .catch(() => {
      resolve([])
    })
  })
}