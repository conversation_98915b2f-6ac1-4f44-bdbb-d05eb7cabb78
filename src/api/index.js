import { get } from './request'

export default {
  checkLink(url) {
    return get(`/commons/go?url=${url}`)
  },
  getMenuTree() {
    const url = window.location.href
    if (url.indexOf('docs-v2') === -1) {
      return get('docs/apis/tree.json').then(res => {
        const list = []
        res.forEach(item => {
          list.push(...item.children)
        })
        list.forEach(item => {
          item.level = 2
        })
        return list
      })
    }
    return get('docs/apis/docking-product-tree.json').then(res => {
      return res.map(item => {
        if (item.children && item.children.length === 1 && item.children[0].code === '_DEFAULT') {
          item.children = item.children[0].children
          item.level = 2
        }
        return item
      })
    })
  },
  getMenuTreeNew() {
    return get('docs/apis/docking-product-tree.json')
  },
  getDocRelation() {
    return get('docs/products/docking-product-doc-relation.json')
  },
  getApiExtend(params) {
    return get(`docs/products/${params.docNo}/api_extend/${params.pageNo}.json`)
  },
  getCommon(params) {
    const url = `docs/apis/common/${params.refName}.json`
    return get(url)
  },
  getDefinition(params) {
    const url = `docs/apis/${params.apiId}/definition.json`
    return get(url)
  },
  getHistory(params) {
    const url = `docs/apis/${params.apiId}/history.json`
    return get(url)
  },
  getErrcode(params) {
    const url = `docs/apis/${params.apiId}/errcode.json`
    return get(url)
  },
  getSpi(params) {
    const url = `docs/apis/${params.apiId}/spi.json`
    return get(url)
  },
  getModels(params) {
    const url = `docs/models/${params.apiGroupCode}/${params.modelName}.json`
    return get(url)
  },
  getCallbackSpis(params) {
    const url = `docs/spis/${params.spiName}.json`
    return get(url)
  },
  // 获取spi api 关联数据
  getSpiApiMap(params) {
    const url = `docs/apis/${params.apiId}/spi-api-mapping.json`
    return get(url)
  },
  // 获取api 基本信息 
  getApibasic() {
    const url = 'docs/apis/basic.json'
    return get(url)
  },
  getApifoxMap() {
    const url = 'docs/apis/apifox_id_mapping.json'
    return get(url)
  }
}
