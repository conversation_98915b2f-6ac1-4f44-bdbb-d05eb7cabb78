export default function handleUrl(url) {
  const langType = window.localStorage.getItem('langType')
  if (!langType) return url
  if (langType === 'en_US') {
    const urlArr = url.split(/(\.json|\.html)/)
    if (urlArr.length > 1) {
      return `${urlArr[0]}_en_US${urlArr[1]}`
    }
    if(urlArr[0].indexOf('?') !== -1) {
      return `${urlArr[0]}&lang=en_US`
    }
    return `${urlArr[0]}?lang=en_US`
  }
  return url
}
