import { get } from './request'

export default {
  getInfo(docNo) {
    return get(`/commons/doc/${docNo}/info`)
  },
  getMenu(params) {
    let url = `docs/${params.splitCode}/${params.docNo}/${params.docVersion}/pages.json`
    if (params.splitCode === 'platform') {
      url = `docs/${params.splitCode}/${params.docVersion}/pages.json`
    }
    return get(url)
  },
  getJson(url) {
    return get(url)
  },
  getHtml(url) {
    return get(url)
  }
}