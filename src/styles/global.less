@import './antdGlobal.less';
@import './industry.less';
@import '../assets/font/iconfont.css';
html {
  height: 100%;
  overflow: hidden auto;
}
* {
  margin: 0;
  padding: 0;
}
div,
ul,
li,
ol,
p,
span {
  box-sizing: border-box;
  list-style: none;
  -webkit-overflow-scrolling: touch;
}
.api-content .left a {
  &:hover {
    border-bottom: 1px solid;
  }
}
.global-wrap {
  .ant-tabs {
    overflow: inherit;
  }
  .body-lock {
    overflow: hidden !important;
  }
  .ivu-table td {
    padding: 13px 0 !important;
    position: relative;
  }
  #pc-left-menu {
    overflow: hidden;
    border-right: 0;
    background: transparent;
    position: fixed;
    z-index: 997;
    top: 66px;
    left: 0;
    bottom: 0;
    transition: height 0;
    .title {
      padding: 0 16px;
      word-break: break-all;
    }
  }
  #pc-left-menu:hover {
    overflow-y: auto !important;
  }
  .ant-layout {
    overflow: inherit !important;
  }
  .mobile {
    width: 100vw;
    .mobile-content {
      padding: 84px 15px;
    }
    .mobile-content2 {
      padding: 0px 15px;
      padding-top: 40px;
      padding-bottom: 90px;
    }
    .mobile-nav {
      display: flex;
      height: 50px;
      align-items: center;
      justify-content: center;
      position: relative;
      z-index: 900;
      background-color: #fff;
      box-shadow: 0px 0px 1px 0px #d8d8d8;
      margin-bottom: 1px;
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      .left-icon-nav {
        position: absolute;
        top: 13px;
        left: 15px;
      }
      .title-nav {
        height: 24px;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 500;
        color: #000000;
        line-height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        span {
          max-width: 200px;
          height: 24px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          word-break: break-all;
        }
      }
    }
  }
  // .ivu-table-wrapper {
  //   overflow-x: auto;
  //   table {
  //     width: 900px !important;
  //   }
  // }
  .ivu-drawer-body {
    padding: 0 !important;
  }
  .ivu-menu-horizontal.ivu-menu-light:after {
    display: none !important;
  }
  .ivu-menu-horizontal {
    height: 64px !important;
    line-height: 64px !important;
  }
  .POST,
  .GET,
  .PUT,
  .PATCH,
  .DELETE,
  .HEAD,
  .OPTIONS {
    line-height: 22px;
    border-radius: 4px;
    font-weight: 400;
    font-size: 12px;
    color: #FFFFFF;
    text-align: center;
    height: 22px;
    padding: 0 6px;
    margin-right: 8px;
  }
  .POST {
    background: #F27B3F;
  }
  .GET {
    background: #3B8CFF;
  }
  .DELETE {
    background: #ff827b;
  }
  .PUT,
  .PATCH {
    background: #fdbb17;
  }
  .link-text,
  .link {
    &:hover {
      color: #78cc82 !important;
    }
  }
  .link-btn {
    &:hover {
      background: #78cc82 !important;
    }
  }
  h1 {
    font-size: 24px !important;
    margin-bottom: 32px;
  }
  h2 {
    font-size: 20px !important;
    margin-top: 48px;
    margin-bottom: 16px;
  }
  h3 {
    font-size: 18px !important;
  }
  h4 {
    font-size: 16px !important;
  }
  h5 {
    font-size: 15px !important;
  }
  h6 {
    font-size: 14px !important;
  }
  h3,
  h4,
  h5,
  h6 {
    margin-top: 24px;
    margin-bottom: 8px;
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: #000000;
  }
  .text {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
      sans-serif;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
    line-height: 22px;
    white-space: pre-wrap;
    word-break: break-all;
  }
  .trigger-wrap {
    cursor: pointer;
    text-align: center;
    line-height: 40px;
    font-size: 16px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #fff;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    box-shadow: 0px 4px 8px -2px rgba(0,0,0,0.08), 0px 8px 32px -8px rgba(25,15,15,0.07);
    border: 1px solid rgba(0,0,0,0.12);
    background: #fff;
    position: fixed;
    z-index: 998;
    bottom: 50px;
  }
  // 方法二
  .codes {
    width: 500px;
    margin: 0 auto;
  }
  .ivu-input-icon {
    color: rgba(0, 0, 0, 0.15) !important;
  }
  .hljs {
    position: relative;
    text-align: left;
    padding: 12px 2px 7px 40px;
    background-color: #fcfcfc;
    margin-bottom: 0 !important;
    code {
      display: block;
      margin: 0 10px;
    }
  }
  .client-height {
    height: calc(100vh - 64px);
  }
  // 共用
  .line-numbers-rows {
    background: #f7f7f7;
    position: absolute;
    padding-top: 12px;
    pointer-events: none;
    top: 1px;
    bottom: 7px;
    left: 0;
    font-size: 0.9em;
    width: 40px;
    text-align: center;
    letter-spacing: -1px;
    // border-right: 1px solid rgba(0, 0, 0, 0.66);
    user-select: none;
    counter-reset: linenumber;
    overflow: hidden;
    span {
      pointer-events: none;
      display: block;
      counter-increment: linenumber;
      &:before {
        content: counter(linenumber);
        color: #999;
        display: block;
        text-align: center;
      }
    }
  }
  .select-header {
    margin-bottom: 12px;
    .lang {
      .label {
        min-width: 42px;
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 400;
        color: #666666;
        line-height: 20px;
        display: inline-block;
        vertical-align: middle;
      }
      .text {
        display: inline-block;
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 400;
        color: #666666;
        line-height: 20px;
      }
      /deep/ .ant-radio-button-wrapper {
        height: 24px;
        line-height: 24px;
      }
    }
  }
  .right-go-top {
    .ant-popover-inner-content {
      padding: 5px 10px;
    }
  }
  .right-go-top-wrapper {
    position: fixed;
    right: 32px;
    bottom: 10%;
    // background: #ffffff;
    z-index: 12;

    .fix-right-icon {
      width: 40px;
      height: 40px;
      background: #ffffff;
      box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
      border-radius: 2px;
      border: 1px solid rgba(0, 0, 0, 0.06);
      padding-top: 10px;
      display: block;
      text-align: center;
      margin-bottom: 8px;
      cursor: pointer;
      position: relative;
    }
    .fix-right-icon svg {
      width: 18px;
      height: 20px;
      fill: rgba(0, 0, 0, 0.65);
      vertical-align: sub;
    }
    .fix-right-icon .drop-left {
      visibility: hidden;
      height: 30px;
      transition: all 0.4s;
      text-align: center;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
      line-height: 30px;
      box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
      border-radius: 2px;
      border: 1px solid rgba(0, 0, 0, 0.06);
      background: #ffffff;
      position: absolute;
      top: 5px;
      left: -100px;
    }
    .fix-right-icon .drop-left::after {
      content: '';
      position: absolute;
      right: -4px;
      top: 11px;
      width: 8px;
      border-top: 1px solid rgba(0, 0, 0, 0.06);
      border-right: 1px solid rgba(0, 0, 0, 0.06);
      height: 8px;
      background: #ffffff;
      transform: rotate(45deg);
    }
    .fix-right-icon:hover svg {
      fill: #52bf63;
    }
    .fix-right-icon:hover .drop-left {
      visibility: visible;
    }
  }
  .code-wrap {
    .hljs {
      display: block;
      overflow-x: auto;
      padding: 0.5em;
      color: #383a42;
      background: #fafafa;
    }

    .hljs-comment,
    .hljs-quote {
      color: #a0a1a7;
      font-style: italic;
    }

    .hljs-doctag,
    .hljs-keyword,
    .hljs-formula {
      color: #0F8ACC;
    }

    .hljs-section,
    .hljs-name,
    .hljs-selector-tag,
    .hljs-deletion,
    .hljs-subst {
      color: #F1932D;
    }

    .hljs-literal {
      color: #0184bb;
    }

    .hljs-string,
    .hljs-regexp,
    .hljs-addition,
    .hljs-attribute,
    .hljs-meta-string {
      color: #429D2D;
    }

    .hljs-built_in,
    .hljs-class .hljs-title {
      color: #F7775C;
    }

    .hljs-attr,
    .hljs-variable,
    .hljs-template-variable,
    .hljs-type,
    .hljs-selector-class,
    .hljs-selector-attr,
    .hljs-selector-pseudo,
    .hljs-number {
      color: #0184bb;
    }

    .hljs-symbol,
    .hljs-bullet,
    .hljs-link,
    .hljs-meta,
    .hljs-selector-id,
    .hljs-title {
      color: #F7775C;
    }

    .hljs-emphasis {
      font-style: italic;
    }

    .hljs-strong {
      font-weight: bold;
    }

    .hljs-link {
      text-decoration: underline;
    }
    .hljs,
    .language-plaintext {
      padding: 12px 2px 7px 40px !important;
    }
    code, kbd, samp {
      font-size: 0.9em;
    }
  }

  .ivu-menu-light.ivu-menu-horizontal .ivu-menu-item,
  .ivu-menu-light.ivu-menu-horizontal .ivu-menu-submenu {
    font-size: 16px;
    font-weight: 400;
    -webkit-font-smoothing: subpixel-antialiased;
    -moz-osx-font-smoothing: inherit;
  }
  .ivu-menu-light.ivu-menu-horizontal .ivu-menu-item-active,
  .ivu-menu-light.ivu-menu-horizontal .ivu-menu-submenu-active,
  .ivu-menu-light.ivu-menu-horizontal .ivu-menu-item:hover,
  .ivu-menu-light.ivu-menu-horizontal .ivu-menu-submenu:hover {
    color: #52bf63;
  }
  .mobile-left-menu {
    position: fixed;
    top: 49px;
    left: 0;
    background: #fff;
    z-index: 14;
  }
  .submenu {
    width: 100vw;
    height: 100vh;
    overflow-y: auto;
    background: #fff;
    padding-top: 4px;
    padding-bottom: 260px;
    position: relative;
  }
  .search {
    height: 52px;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    padding: 0 16px;
  }
  .menu-tree {
    background: #fff;
    display: flex;
    height: calc(100vh - 90px);
    .menu-tree-left {
      .menu-tree-item {
        width: 100px;
        height: 40px;
        text-align: center;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.65);
        line-height: 40px;
        position: relative;
      }
      .active {
        background: #f9fafc;
        &::before {
          content: '';
          display: inline-block;
          position: absolute;
          top: 0;
          bottom: 0;
          width: 3px;
          background: #52bf63;
          left: 0;
        }
      }
    }
    .menu-tree-right {
      flex: 1;
      padding: 10px 15px;
      overflow-y: auto;
      background: #f9fafc;
      .menu-tree-right-item {
        margin-bottom: 20px;
        &:last-child {
          margin-bottom: 0;
        }
        word-break: break-all;
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 400;
        color: #000000;
        line-height: 20px;
      }
      .menu-tree-right-active {
        color: #52bf63;
      }
    }
  }
  @media screen and (max-width: 767px) {
    _::-webkit-full-page-media,
    _:future,
    :root .safari_only {
      padding-bottom: 65px; //resize
    }
  }
  .mobile-h3-title {
    padding: 0 16px;
  }
  .api-anchor-wrap > .ant-anchor-link-title {
    color: rgba(0, 0, 0, 0.85);
  }
}
.ant-anchor-ink-ball.visible {
  display: inline-block !important;
}

.vditor-reset {
  .hljs {
    word-break: break-all;
    white-space: pre-wrap;
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    border-bottom: none !important;
  }
}
.Product-wrap {
  .ant-row {
    display: flex;
  }
}
.text-o {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.code-col-wrap {
  padding-top: 18px; 
  position: sticky; 
  top: 64px
}