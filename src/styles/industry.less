.industry-wrap {
  min-width: 1280px;
  .header {
    min-width: 1280px;
    overflow: hidden;
    height: 450px;
    background-size: 100% 450px;
    padding-top: 168px;
    position: relative;
    .header-img {
      width: 1920px;
      height: 450px;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      margin: 0 auto;
    }
    .title {
      text-align: center;
      height: 56px;
      font-size: 40px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.85);
      line-height: 56px;
      margin-bottom: 16px;
      position: relative;
      z-index: 2;
    }
    .desc {
      position: relative;
      z-index: 2;
      max-width: 980px;
      margin: 0 auto;
      text-align: center;
      height: 22px;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.85);
      line-height: 22px;
      margin-bottom: 40px;
    }
    .btn {
      position: relative;
      z-index: 2;
      text-align: center;
      display: block;
      margin: 0 auto;
      width: 160px;
      height: 40px;
      background: #52bf63;
      border-radius: 20px;
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 500;
      color: #ffffff;
      line-height: 40px;
      &:hover {
        background: #78cc82 !important;
      }
    }
  }
  .content {
    width: 1280px;
    margin: 0 auto;
  }
  .solution-wrap {
    text-align: center;
    padding: 100px 0;
    margin-bottom: 67px;
    background-color: #fff;
    &:nth-child(odd) {
      background: #fafafa;
    }
    .text-wrap {
      width: 500px;
      text-align: left;
      display: inline-block;
      vertical-align: middle;
      .title {
        height: 33px;
        font-size: 24px;
        font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        line-height: 33px;
        margin-bottom: 32px;
      }
      .title-desc {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        line-height: 26px;
        margin-bottom: 16px;
      }
      .link-btn {
        display: block;
        width: 120px;
        height: 32px;
        background: #52bf63;
        border-radius: 20px;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 500;
        color: #ffffff;
        line-height: 32px;
        text-align: center;
        margin-bottom: 40px;
      }
      .sub-title {
        margin-bottom: 16px;
        height: 25px;
        font-size: 18px;
        font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
          sans-serif;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        line-height: 25px;
      }
      .project-list {
        .project-item {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei',
            SimSun, sans-serif;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 18px;
          padding-left: 16px;
          position: relative;
          margin-bottom: 12px;
          &::before {
            content: '';
            width: 8px;
            height: 8px;
            background: #52bf63;
            border-radius: 50%;
            position: absolute;
            left: 0;
            top: 5px;
          }
        }
      }
    }
    .img-wrap {
      display: inline-block;
      vertical-align: middle;
    }
  }
  .guide-wrap {
    background: #fafafa;
    font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
      sans-serif;
    text-align: center;
    padding: 60px 0;
    p:first-child {
      font-size: 24px;
      font-weight: 500;
      color: #000000;
    }
    .flow-img {
      height: 132px;
      display: block;
      margin: 0 auto;
    }
    .condition-content {
      color: rgba(0, 0, 0, 0.85);
      margin-bottom: 40px;
    }
  }
  .partner-wrap {
    margin: 50px 0 70px;
    h2 {
      text-align: center;
      margin-bottom: 39px;
    }
    .partner-desc {
      width: 1000px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
      line-height: 26px;
      margin: 0 auto;
      margin-bottom: 40px;
    }
    .img {
      display: block;
      width: 1200px;
      margin: 0 auto;
    }
  }
  .flow-title {
    height: 25px;
    font-size: 18px;
    font-family: PingFangSC-Medium, PingFang SC, 'Microsoft YaHei', SimSun,
      sans-serif;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    line-height: 25px;
    text-align: center;
  }
}
