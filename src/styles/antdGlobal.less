.ant-anchor {
  font-size: 13px;
}
.ant-layout-header {
  .ant-menu-horizontal,
  .ant-menu-horizontal > .ant-menu-item:hover {
    border-bottom: 2px solid transparent;
  }
  .ant-menu-horizontal > .ant-menu-item-selected {
    border-bottom: 2px solid #52bf63 !important;
  }
  .ant-menu-horizontal .ant-menu-item {
    margin-top: -2px;
  }
  padding: 0;
  display: flex;
  background: #fff;
  justify-content: space-between;
  .logo {
    display: flex;
    align-items: center;
    padding-left: 24px;
    .img {
      margin-right: 17px;
      width: 109px;
      height: 32px;
    }
    .text {
      height: 20px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC, 'Microsoft YaHei', SimSun,
        sans-serif;
      font-weight: 400;
      color: #555555;
      line-height: 20px;
    }
  }
}
.ant-table {
  &-thead > tr,
  &-tbody > tr {
    > th,
    > td {
      min-width: 100px;
    }
  }
  &-tbody > tr:hover:not(.ant-table-expanded-row) > td,
  .ant-table-row-hover,
  .ant-table-row-hover > td {
    background: rgba(0, 0, 0, 0.02) !important;
  }
  .ant-table-selection-column {
    min-width: 0;
  }
}
.ant-menu-sub.ant-menu-inline .ant-menu-item {
  padding-right: 15px;
  line-height: 40px !important;
  margin: 0;
}
.ant-anchor-ink-ball {
  width: 3px !important;
  height: 18px !important;
  background: #52bf63;
  border-radius: 2px;
  border: none;
  margin-top: -5px;
}
.ant-table-thead > tr > th,
.ant-table-tbody > tr > td {
  padding: 13px 16px;
}
.ant-menu-submenu-inline
  > .ant-menu-submenu-title
  .ant-menu-submenu-arrow::before {
  height: 1px;
}
.ant-menu-submenu-inline
  > .ant-menu-submenu-title
  .ant-menu-submenu-arrow:after {
  height: 1px;
}
.ant-table-thead > tr > th {
  background-color: #f8f8f9 !important;
}
.ant-radio-button-wrapper {
  padding: 0 10px;
}
.ant-anchor-link-title {
  font-size: 14px;
  font-weight: 400 !important;
}
.ant-anchor-wrapper {
  background-color: transparent;
}
.ant-menu-inline .ant-menu-item,
.ant-menu-inline .ant-menu-submenu-title {
  width: 100%;
}
.ant-menu {
  -webkit-font-smoothing: subpixel-antialiased;
  -moz-osx-font-smoothing: inherit;
  font-size: 14px !important;
}
.level-1 {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 400;
}
.level-1-enmty > .ant-menu-item-group-title {
  height: 0;
  padding: 0;
}
.level-2 {
  color: rgba(0, 0, 0, 0.65);
  font-weight: 400;
}
.level-3 {
  color: rgba(0, 0, 0, 0.65);
}
.ant-menu-item-selected {
  color: #52bf63;
}
.ant-menu-inline .ant-menu-item,
.ant-menu-inline .ant-menu-submenu-title {
  width: 100% !important;
  margin: 0 !important;
}
.ant-menu-item-group-list .ant-menu-item,
.ant-menu-item-group-list .ant-menu-submenu-title {
  padding-right: 26px !important;
}