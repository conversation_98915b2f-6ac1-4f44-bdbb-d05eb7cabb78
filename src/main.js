import 'core-js/stable'
import 'regenerator-runtime/runtime'
import 'babel-polyfill'
import './utils/base64'
import Vue from 'vue'
import App from './App.vue'
import router from './router'
import './i18n'
import store from './store'
import directive from './utils/directive'
import Viewer from 'v-viewer'
import { upLog } from '@/api/request'
import { Tooltip } from 'view-design'
import components from '@/components'
import Renderer, { material, materials } from '@yeepay/lowcode-renderer'
import Antd, { AntdMaterials } from '@yeepay/antd-materials'
import '@yeepay/lowcode-renderer/dist/styles/index.less'
import '@yeepay/antd-materials/dist/styles/index.less'
// 样式文件
import 'viewerjs/dist/viewer.css'
import './styles/my-theme.less'
import './utils/importUI'
import './styles/global.less'

Vue.config.productionTip = false
Vue.prototype.$upLog = upLog
Vue.use(Viewer)
Vue.use(directive)
Vue.use(components)
Vue.use(Renderer) // 渲染器 vueuse
material.install(AntdMaterials) // antd 物料集    
Vue.use(Antd, { materials }) // antd 物料集vueuse
Vue.component('Tooltip', Tooltip)
new Vue({
  router,
  store,
  render: h => h(App),
  mounted() {
    // You'll need this for renderAfterDocumentEvent.
    document.dispatchEvent(new Event('render-event'))
  }
}).$mount('#app')
