import ApiOverview from '@/views/ApiDocs/ApiOverview'
import ApiMenuItem from '@/components/ApiMenuItem'
import utils from '@/utils'
export default {
  components: {
    ApiOverview,
    ApiMenuItem
  },
  data() {
    return {
      openKeys: []
    }
  },
  watch: {
    menuCode: {
      handler(newValue) {
        if (newValue) this.openKeys = newValue
      },
      immediate: true
    }
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile
    },
    subMenuList() {
      return this.$store.getters['apiDocs/subMenuList']
    },
    keyWord() {
      return this.$store.state.apiDocs.keyWord
    },
    filterMenuList() {
      return this.$store.state.apiDocs.filterMenuList
    },
    menuList() {
      return this.$store.state.apiDocs.menuList
    },
    itemsList() {
      return this.$store.state.apiDocs.itemsList
    },
    menuCode() {
      return this.$store.state.apiDocs.menuCode
    },
    menuMap() {
      return this.$store.state.apiDocs.menuMap
    },
    subMenuApiId() {
      return this.$store.state.apiDocs.subMenuApiId
    }
  },
  methods: {
    handlerFilterMenuList(list) {
      return list.filter(item => {
        if (item.children) {
          item.children = this.handlerFilterMenuList(item.children)
        }
        if (item.items && item.items.length > 0) {
          item.items = item.items.filter(item => {
            if (
              item.title.indexOf(this.keyWord) !== -1 ||
              item.path.indexOf(this.keyWord) !== -1
            ) {
              item.show = true
              return true
            } else {
              return false
            }
          })
        }
        return item.children.length > 0 || item.items.length > 0
      })
    },
    findFirstLocation(list) {
      if (list.length < 0) return ''
      if (list[0].items && list[0].items.length > 0) {
        return list[0].items[0].location
      }
      return this.findFirstLocation(list[0].children)
    },
    getFilterMenuList() {
      if (!this.keyWord.trim()) {
        this.$store.commit('apiDocs/setFilterMenuList', this.menuList)
        return
      }
      let list = JSON.parse(JSON.stringify(this.menuList))
      list = this.handlerFilterMenuList(list)
      if (list.length > 0) {
        const location = this.findFirstLocation(list)
        const item = this.itemsList.find(menu => menu.location === location)
        this.$store.commit('apiDocs/setMenuActive', item.openKeys)
      }
      this.$store.commit('apiDocs/setFilterMenuList', list)
    },
    onOpenChange(openKeys) {
      const newKeys = this.getAutoOpenKey(openKeys)
      this.openKeys = newKeys
      this.$store.commit('apiDocs/setMenuActive', newKeys)
    },
    getAutoOpenKey(openKeys) {
      if(openKeys.length < this.openKeys.length) {
        return openKeys
      }
      let newKeys = []
      openKeys.filter(loc => !this.openKeys.find(cur => cur === loc)).forEach(item => {
        if(item.split('/').length === 2) {
          const menu = utils.findProductItem(this.filterMenuList, item)
          if(menu.children.length === 1) {
            newKeys.push(
              menu.children[0].location
            )
          }
        }
      })
      return [...new Set(openKeys.concat(newKeys))]
    },
    onMenuClick({ key }) {
      let keyArr = key.split('/')
      let goToUrl = `${keyArr[2]}/${keyArr[4]}`
      const url = window.location.href
      let firstUrl = 'docs'
      if (url.indexOf('docs-v2') !== -1) {
        firstUrl = 'docs-v2'
      }
      this.$router.push(`/${firstUrl}/apis/${goToUrl}`)
      this.$store.commit('setSubMenu', false)
    },
    onChange(e) {
      this.$store.commit('apiDocs/setKeyWord', e.target.value)
      this.getFilterMenuList()
    }
  }
}
