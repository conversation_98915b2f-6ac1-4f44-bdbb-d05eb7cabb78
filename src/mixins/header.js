export default {
  computed: {
    langType() {
      return this.$store.state.langType
    }
  },
  methods: {
    goDeveloper() {
      window.location.href =
        'https://mp.yeepay.com/auth/signin?redirectUrl=https://mp.yeepay.com/yop-developer-center/cas?redirectUrl=https://mp.yeepay.com/mp-developer-center/index.html'
    },
    changeLang() {
      const lang = this.langType === 'en' ? '' : 'en_US'
      window.localStorage.setItem('langType', lang)
      this.$store.commit('setLangType', this.langType === 'en' ? 'cn' : 'en' )
      // window.location.reload()
    }
  }
}
