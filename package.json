{"name": "yop-docs", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "rm -rf dist.zip && vue-cli-service build --mode production && zip -q -r dist.zip dist/", "lint": "vue-cli-service lint"}, "dependencies": {"@vue/composition-api": "^1.7.0", "@yeepay/client-utils": "^0.0.24", "@yeepay/lowcode-renderer": "2.23.3", "@yeepay/antd-materials": "2.23.3", "ant-design-vue": "^1.7.2", "axios": "^0.21.1", "babel-polyfill": "^6.26.0", "core-js": "^3.6.5", "highlight.js": "^9.18.5", "platform": "^1.3.6", "qs": "^6.9.4", "replace-in-file": "^6.3.5", "v-viewer": "^1.5.1", "vditor": "3.3.9", "view-design": "^4.4.0", "vue": "2.6.14", "vue-i18n": "^8.22.4", "vue-router": "^3.2.0", "vue-virtual-scroller": "^1.0.10", "vuex": "^3.4.0"}, "devDependencies": {"@babel/plugin-syntax-dynamic-import": "^7.8.3", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.3", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "less": "^3.0.4", "less-loader": "^5.0.0", "prerender-spa-plugin": "^3.4.0", "vue-template-compiler": "2.6.14"}}