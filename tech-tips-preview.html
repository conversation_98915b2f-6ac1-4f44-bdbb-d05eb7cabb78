<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API技术提示组件预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            margin-bottom: 32px;
            text-align: center;
            font-size: 28px;
        }
        
        .demo-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #fff;
        }
        
        .demo-section h2 {
            color: #495057;
            margin-bottom: 16px;
            font-size: 18px;
        }
        
        .api-tech-tips {
            margin-bottom: 16px;
        }
        
        .tips-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px 4px 0 0;
        }
        
        .tips-title {
            margin: 0;
            font-size: 14px;
            font-weight: 500;
            color: #495057;
            display: flex;
            align-items: center;
        }
        
        .tips-icon {
            margin-right: 6px;
            font-size: 14px;
        }
        
        .tips-toggle {
            cursor: pointer;
            padding: 4px;
            border-radius: 2px;
            transition: background-color 0.2s;
        }
        
        .tips-toggle:hover {
            background-color: #e9ecef;
        }
        
        .toggle-icon {
            font-size: 12px;
            color: #6c757d;
        }
        
        .tips-content {
            border: 1px solid #e9ecef;
            border-top: none;
            border-radius: 0 0 4px 4px;
            padding: 12px;
            background: #fff;
        }
        
        .tip-item {
            margin-bottom: 12px;
            padding: 12px;
            border-radius: 4px;
            border-left: 4px solid;
        }
        
        .tip-item:last-child {
            margin-bottom: 0;
        }
        
        .tip-error {
            background-color: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        
        .tip-warning {
            background-color: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        
        .tip-info {
            background-color: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }
        
        .tip-title {
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .tip-description {
            font-size: 13px;
            line-height: 1.5;
        }
        
        .tip-actions {
            margin-top: 8px;
        }
        
        .tip-action-link {
            color: #007bff;
            text-decoration: none;
            font-size: 12px;
            margin-right: 12px;
        }
        
        .tip-action-link:hover {
            text-decoration: underline;
        }
        
        .api-info {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 16px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 13px;
        }
        
        .method {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 2px;
            color: white;
            font-size: 11px;
            margin-right: 8px;
        }
        
        .method.POST { background: #49cc90; }
        .method.GET { background: #61affe; }
        .method.PUT { background: #fdbb17; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 API技术提示组件预览</h1>
        
        <div class="demo-section">
            <h2>POST接口（非幂等）- 创建订单</h2>
            <div class="api-info">
                <span class="method POST">POST</span>
                <span>/api/v1/orders</span>
                <div style="margin-top: 4px; color: #666;">需要签名认证</div>
            </div>
            
            <div class="api-tech-tips">
                <div class="tips-header">
                    <h4 class="tips-title">
                        <span class="tips-icon">⚠️</span>
                        技术提示
                    </h4>
                    <div class="tips-toggle">
                        <span class="toggle-icon">▼</span>
                    </div>
                </div>
                
                <div class="tips-content">
                    <div class="tip-item tip-error">
                        <div class="tip-title">⚠️ 非幂等接口异常处理</div>
                        <div class="tip-description">此接口为非幂等操作，请求失败时请检查业务状态后再决定是否重试，避免重复处理导致业务异常。建议使用唯一业务单号进行幂等控制。</div>
                    </div>
                    
                    <div class="tip-item tip-warning">
                        <div class="tip-title">🔄 重试机制建议</div>
                        <div class="tip-description">建议实现指数退避重试机制，最大重试3次，间隔时间：1s、2s、4s。对于网络超时等临时性错误可以重试，对于参数错误等永久性错误不应重试。</div>
                    </div>
                    
                    <div class="tip-item tip-warning">
                        <div class="tip-title">🔐 签名安全</div>
                        <div class="tip-description">签名密钥请妥善保管，建议定期轮换。生产环境禁止在前端暴露密钥，应在服务端进行签名计算。</div>
                        <div class="tip-actions">
                            <a href="#" class="tip-action-link">查看签名指南</a>
                        </div>
                    </div>
                    
                    <div class="tip-item tip-info">
                        <div class="tip-title">⏱️ 超时处理</div>
                        <div class="tip-description">建议设置合理的超时时间（建议30-60秒），并实现超时后的降级处理。超时不代表请求失败，请通过查询接口确认最终状态。</div>
                    </div>
                    
                    <div class="tip-item tip-info">
                        <div class="tip-title">✅ 参数校验</div>
                        <div class="tip-description">请在客户端进行参数预校验，减少无效请求，提升用户体验。同时服务端也会进行完整的参数校验。</div>
                    </div>
                    
                    <div class="tip-item tip-info">
                        <div class="tip-title">🚦 限流频控</div>
                        <div class="tip-description">接口存在频率限制，建议合理控制调用频率，避免触发限流机制。建议实现请求队列和限流重试逻辑。</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>POST接口（支付相关）- 发起支付</h2>
            <div class="api-info">
                <span class="method POST">POST</span>
                <span>/api/v1/payment/pay</span>
                <div style="margin-top: 4px; color: #666;">需要签名认证 + OAUTH2授权</div>
            </div>
            
            <div class="api-tech-tips">
                <div class="tips-header">
                    <h4 class="tips-title">
                        <span class="tips-icon">⚠️</span>
                        技术提示
                    </h4>
                    <div class="tips-toggle">
                        <span class="toggle-icon">▼</span>
                    </div>
                </div>
                
                <div class="tips-content">
                    <div class="tip-item tip-error">
                        <div class="tip-title">⚠️ 非幂等接口异常处理</div>
                        <div class="tip-description">此接口为非幂等操作，请求失败时请检查业务状态后再决定是否重试，避免重复处理导致业务异常。建议使用唯一业务单号进行幂等控制。</div>
                    </div>
                    
                    <div class="tip-item tip-error">
                        <div class="tip-title">🔒 并发控制</div>
                        <div class="tip-description">涉及资金操作，建议在客户端实现防重复提交机制，避免并发请求导致重复扣款等问题。可使用分布式锁或唯一约束实现。</div>
                    </div>
                    
                    <div class="tip-item tip-warning">
                        <div class="tip-title">🔐 签名安全</div>
                        <div class="tip-description">签名密钥请妥善保管，建议定期轮换。生产环境禁止在前端暴露密钥，应在服务端进行签名计算。</div>
                        <div class="tip-actions">
                            <a href="#" class="tip-action-link">查看签名指南</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>GET接口（幂等）- 查询订单</h2>
            <div class="api-info">
                <span class="method GET">GET</span>
                <span>/api/v1/orders/{orderId}</span>
                <div style="margin-top: 4px; color: #666;">支持幂等</div>
            </div>
            
            <div class="api-tech-tips">
                <div class="tips-header">
                    <h4 class="tips-title">
                        <span class="tips-icon">⚠️</span>
                        技术提示
                    </h4>
                    <div class="tips-toggle">
                        <span class="toggle-icon">▼</span>
                    </div>
                </div>
                
                <div class="tips-content">
                    <div class="tip-item tip-warning">
                        <div class="tip-title">🔄 重试机制建议</div>
                        <div class="tip-description">建议实现指数退避重试机制，最大重试3次，间隔时间：1s、2s、4s。对于网络超时等临时性错误可以重试，对于参数错误等永久性错误不应重试。</div>
                    </div>
                    
                    <div class="tip-item tip-info">
                        <div class="tip-title">⏱️ 超时处理</div>
                        <div class="tip-description">建议设置合理的超时时间（建议30-60秒），并实现超时后的降级处理。超时不代表请求失败，请通过查询接口确认最终状态。</div>
                    </div>
                    
                    <div class="tip-item tip-info">
                        <div class="tip-title">✅ 参数校验</div>
                        <div class="tip-description">请在客户端进行参数预校验，减少无效请求，提升用户体验。同时服务端也会进行完整的参数校验。</div>
                    </div>
                    
                    <div class="tip-item tip-info">
                        <div class="tip-title">🚦 限流频控</div>
                        <div class="tip-description">接口存在频率限制，建议合理控制调用频率，避免触发限流机制。建议实现请求队列和限流重试逻辑。</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 简单的折叠/展开功能
        document.querySelectorAll('.tips-toggle').forEach(toggle => {
            toggle.addEventListener('click', function() {
                const content = this.closest('.api-tech-tips').querySelector('.tips-content');
                const icon = this.querySelector('.toggle-icon');
                
                if (content.style.display === 'none') {
                    content.style.display = 'block';
                    icon.textContent = '▼';
                } else {
                    content.style.display = 'none';
                    icon.textContent = '▶';
                }
            });
        });
    </script>
</body>
</html>
