var isIE = function() {
  return window.navigator.userAgent.indexOf("MSIE") >= 1;
};
// 修复IE10及以下版本不支持dataset属性的问题，兼容transfer-dom.js中使用了dataset的问题
if (isIE() && window.HTMLElement) {
  if (
    Object.getOwnPropertyNames(HTMLElement.prototype).indexOf("dataset") === -1
  ) {
    Object.defineProperty(HTMLElement.prototype, "dataset", {
      get: function() {
        var attributes = this.attributes;
        var name = [],
          value = [];
        var obj = {};
        for (var i = 0; i < attributes.length; i++) {
          if (attributes[i].nodeName.slice(0, 5) == "data-") {
            name.push(attributes[i].nodeName.slice(5));
            value.push(attributes[i].nodeValue);
          }
        }
        for (var j = 0; j < name.length; j++) {
          obj[name[j]] = value[j];
        }
        return obj;
      }
    });
  }
  if (!("classList" in document.documentElement)) {
    Object.defineProperty(HTMLElement.prototype, "classList", {
      get: function() {
        var self = this;

        function update(fn) {
          return function(value) {
            var classes = self.className.split(/\s+/g);

            var index = classes.indexOf(value);

            fn(classes, index, value);

            self.className = classes.join(" ");
          };
        }

        return {
          add: update(function(classes, index, value) {
            if (!~index) classes.push(value);
          }),

          remove: update(function(classes, index) {
            if (~index) classes.splice(index, 1);
          }),

          toggle: update(function(classes, index, value) {
            if (~index) {
              classes.splice(index, 1);
            } else {
              classes.push(value);
            }
          }),

          contains: function(value) {
            return !!~self.className.split(/\s+/g).indexOf(value);
          },

          item: function(i) {
            return self.className.split(/\s+/g)[i] || null;
          }
        };
      }
    });
  }
}
(function() {
  var lastTime = 0;
  var vendors = ["webkit", "moz"];
  for (var x = 0; x < vendors.length && !window.requestAnimationFrame; ++x) {
    window.requestAnimationFrame = window[vendors[x] + "RequestAnimationFrame"];
    window.cancelAnimationFrame =
      window[vendors[x] + "CancelAnimationFrame"] ||
      window[vendors[x] + "CancelRequestAnimationFrame"];
  }
  if (!window.requestAnimationFrame)
    window.requestAnimationFrame = function(callback) {
      var currTime = new Date().getTime();
      var timeToCall = Math.max(0, 16 - (currTime - lastTime));
      var id = window.setTimeout(function() {
        callback(currTime + timeToCall);
      }, timeToCall);
      lastTime = currTime + timeToCall;
      return id;
    };
  if (!window.cancelAnimationFrame)
    window.cancelAnimationFrame = function(id) {
      clearTimeout(id);
    };
})();
