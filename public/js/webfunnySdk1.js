!function(t){"function"==typeof define&&define.amd?define(t):t()}((function(){"use strict";var t="required",e="length",n="type",o="range",r={REQUIRED:"$field$,输入值不可为空",LENGTH:"$field$,输入超长，最大长度为$rule$",TYPE:"$field$,输入的类型不正确，应为$rule$类型",RANGE:"$field$,输入不在范围内，应在$range1$~$range2$之内",LACK:"上送字段缺失，缺少字段$field$"};var i,s,c,u=(i=function(t){function e(n){return"function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?(t.exports=e=function(t){return typeof t},t.exports.default=t.exports,t.exports.__esModule=!0):(t.exports=e=function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.default=t.exports,t.exports.__esModule=!0),e(n)}t.exports=e,t.exports.default=t.exports,t.exports.__esModule=!0},i(s={exports:{}},s.exports),s.exports);(c=u)&&c.__esModule&&Object.prototype.hasOwnProperty.call(c,"default")&&c.default;var a={isString:function(t){return"[object String]"===Object.prototype.toString.apply(t)},isNumber:function(t){return"[object Number]"===Object.prototype.toString.apply(t)},isArray:function(t){return"[object Array]"===Object.prototype.toString.apply(t)},isObject:function(t){return"[object Object]"===Object.prototype.toString.apply(t)},isDate:function(t){return"[object Date]"===Object.prototype.toString.apply(t)},isBoolean:function(t){return"[object Boolean]"===Object.prototype.toString.apply(t)}};function l(t,e){var n=!1,o=function(t,e){var n={success:!1,field:""};for(var o in e){if(!Object.prototype.hasOwnProperty.call(t,o))return n.success=!1,n.field=o,n;n.success=!0}return n}(t,e),i=o.success,s=o.field;if(!(n=i)){var c=r.LACK.replace(/\$field\$/g,s);return console.warn(c),n}for(var u in e){if(!(n=h(t[u],u,e[u])))return n}return n}function h(i,s,c){var u=!1;for(var l in c){var h=c[l];if(l===t&&h){if(!(u=void 0!==i)){var f=r.REQUIRED.replace(/\$field\$/g,s).replace(/\$rule\$/g,"");return console.warn(f),u}}else if(l===e&&h){if(!(u=i.toString().length<=h)){var p=r.LENGTH.replace(/\$field\$/g,s).replace(/\$rule\$/g,h);return console.warn(p),u}}else if(l===n&&h){if(!(u=(0,a["is"+h])(i))){var d=r.TYPE.replace(/\$field\$/g,s).replace(/\$rule\$/g,h);return console.warn(d),u}}else if(l===o&&h){var g=i>=h[0]&&i<h[1];if(!g){var b=r.RANGE.replace(/\$field\$/g,s).replace(/\$range1\$/g,h[0]).replace(/\$range2\$/g,h[1]);return console.warn(b),g}}}return u}var f="//probe.yeepay.com/tracker/upEvent";function p(){var t=f,e=window.location.protocol;return e=e.replace(/file/g,"http"),t.indexOf("http")>-1?t:e+t}function d(t){var e=p();try{var n=window.XMLHttpRequest?new XMLHttpRequest:new window.ActiveXObject("Microsoft.XMLHTTP");n.open("POST",e,!0),n.setRequestHeader("Content-Type","application/x-www-form-urlencoded; charset=UTF-8"),n.send("data="+JSON.stringify(t))}catch(e){!function(t){var e=p(),n=e+(e.indexOf("?")<0?"?":"&")+t;new Image(1,1).src=n}(t)}}function g(t){t&&d(t)}!function(){if("object"==typeof window)if("IntersectionObserver"in window&&"IntersectionObserverEntry"in window&&"intersectionRatio"in window.IntersectionObserverEntry.prototype)"isIntersecting"in window.IntersectionObserverEntry.prototype||Object.defineProperty(window.IntersectionObserverEntry.prototype,"isIntersecting",{get:function(){return this.intersectionRatio>0}});else{var t=function(t){for(var e=window.document,n=r(e);n;)n=r(e=n.ownerDocument);return e}(),e=[],n=null,o=null;s.prototype.THROTTLE_TIMEOUT=100,s.prototype.POLL_INTERVAL=null,s.prototype.USE_MUTATION_OBSERVER=!0,s._setupCrossOriginUpdater=function(){return n||(n=function(t,n){o=t&&n?h(t,n):{top:0,bottom:0,left:0,right:0,width:0,height:0},e.forEach((function(t){t._checkForIntersections()}))}),n},s._resetCrossOriginUpdater=function(){n=null,o=null},s.prototype.observe=function(t){if(!this._observationTargets.some((function(e){return e.element==t}))){if(!t||1!=t.nodeType)throw new Error("target must be an Element");this._registerInstance(),this._observationTargets.push({element:t,entry:null}),this._monitorIntersections(t.ownerDocument),this._checkForIntersections()}},s.prototype.unobserve=function(t){this._observationTargets=this._observationTargets.filter((function(e){return e.element!=t})),this._unmonitorIntersections(t.ownerDocument),0==this._observationTargets.length&&this._unregisterInstance()},s.prototype.disconnect=function(){this._observationTargets=[],this._unmonitorAllIntersections(),this._unregisterInstance()},s.prototype.takeRecords=function(){var t=this._queuedEntries.slice();return this._queuedEntries=[],t},s.prototype._initThresholds=function(t){var e=t||[0];return Array.isArray(e)||(e=[e]),e.sort().filter((function(t,e,n){if("number"!=typeof t||isNaN(t)||t<0||t>1)throw new Error("threshold must be a number between 0 and 1 inclusively");return t!==n[e-1]}))},s.prototype._parseRootMargin=function(t){var e=(t||"0px").split(/\s+/).map((function(t){var e=/^(-?\d*\.?\d+)(px|%)$/.exec(t);if(!e)throw new Error("rootMargin must be specified in pixels or percent");return{value:parseFloat(e[1]),unit:e[2]}}));return e[1]=e[1]||e[0],e[2]=e[2]||e[0],e[3]=e[3]||e[1],e},s.prototype._monitorIntersections=function(e){var n=e.defaultView;if(n&&-1==this._monitoringDocuments.indexOf(e)){var o=this._checkForIntersections,i=null,s=null;this.POLL_INTERVAL?i=n.setInterval(o,this.POLL_INTERVAL):(c(n,"resize",o,!0),c(e,"scroll",o,!0),this.USE_MUTATION_OBSERVER&&"MutationObserver"in n&&(s=new n.MutationObserver(o)).observe(e,{attributes:!0,childList:!0,characterData:!0,subtree:!0})),this._monitoringDocuments.push(e),this._monitoringUnsubscribes.push((function(){var t=e.defaultView;t&&(i&&t.clearInterval(i),u(t,"resize",o,!0)),u(e,"scroll",o,!0),s&&s.disconnect()}));var a=this.root&&(this.root.ownerDocument||this.root)||t;if(e!=a){var l=r(e);l&&this._monitorIntersections(l.ownerDocument)}}},s.prototype._unmonitorIntersections=function(e){var n=this._monitoringDocuments.indexOf(e);if(-1!=n){var o=this.root&&(this.root.ownerDocument||this.root)||t,i=this._observationTargets.some((function(t){var n=t.element.ownerDocument;if(n==e)return!0;for(;n&&n!=o;){var i=r(n);if((n=i&&i.ownerDocument)==e)return!0}return!1}));if(!i){var s=this._monitoringUnsubscribes[n];if(this._monitoringDocuments.splice(n,1),this._monitoringUnsubscribes.splice(n,1),s(),e!=o){var c=r(e);c&&this._unmonitorIntersections(c.ownerDocument)}}}},s.prototype._unmonitorAllIntersections=function(){var t=this._monitoringUnsubscribes.slice(0);this._monitoringDocuments.length=0,this._monitoringUnsubscribes.length=0;for(var e=0;e<t.length;e++)t[e]()},s.prototype._checkForIntersections=function(){if(this.root||!n||o){var t=this._rootIsInDom(),e=t?this._getRootRect():{top:0,bottom:0,left:0,right:0,width:0,height:0};this._observationTargets.forEach((function(o){var r=o.element,s=a(r),c=this._rootContainsTarget(r),u=o.entry,l=t&&c&&this._computeTargetAndRootIntersection(r,s,e),h=null;this._rootContainsTarget(r)?n&&!this.root||(h=e):h={top:0,bottom:0,left:0,right:0,width:0,height:0};var f=o.entry=new i({time:window.performance&&performance.now&&performance.now(),target:r,boundingClientRect:s,rootBounds:h,intersectionRect:l});u?t&&c?this._hasCrossedThreshold(u,f)&&this._queuedEntries.push(f):u&&u.isIntersecting&&this._queuedEntries.push(f):this._queuedEntries.push(f)}),this),this._queuedEntries.length&&this._callback(this.takeRecords(),this)}},s.prototype._computeTargetAndRootIntersection=function(e,r,i){if("none"!=window.getComputedStyle(e).display){for(var s,c,u,l,f,d,g,b,y=r,v=p(e),m=!1;!m&&v;){var w=null,_=1==v.nodeType?window.getComputedStyle(v):{};if("none"==_.display)return null;if(v==this.root||9==v.nodeType)if(m=!0,v==this.root||v==t)n&&!this.root?!o||0==o.width&&0==o.height?(v=null,w=null,y=null):w=o:w=i;else{var E=p(v),O=E&&a(E),I=E&&this._computeTargetAndRootIntersection(E,O,i);O&&I?(v=E,w=h(O,I)):(v=null,y=null)}else{var T=v.ownerDocument;v!=T.body&&v!=T.documentElement&&"visible"!=_.overflow&&(w=a(v))}if(w&&(s=w,c=y,u=void 0,l=void 0,f=void 0,d=void 0,g=void 0,b=void 0,u=Math.max(s.top,c.top),l=Math.min(s.bottom,c.bottom),f=Math.max(s.left,c.left),d=Math.min(s.right,c.right),b=l-u,y=(g=d-f)>=0&&b>=0&&{top:u,bottom:l,left:f,right:d,width:g,height:b}||null),!y)break;v=v&&p(v)}return y}},s.prototype._getRootRect=function(){var e;if(this.root&&!d(this.root))e=a(this.root);else{var n=d(this.root)?this.root:t,o=n.documentElement,r=n.body;e={top:0,left:0,right:o.clientWidth||r.clientWidth,width:o.clientWidth||r.clientWidth,bottom:o.clientHeight||r.clientHeight,height:o.clientHeight||r.clientHeight}}return this._expandRectByRootMargin(e)},s.prototype._expandRectByRootMargin=function(t){var e=this._rootMarginValues.map((function(e,n){return"px"==e.unit?e.value:e.value*(n%2?t.width:t.height)/100})),n={top:t.top-e[0],right:t.right+e[1],bottom:t.bottom+e[2],left:t.left-e[3]};return n.width=n.right-n.left,n.height=n.bottom-n.top,n},s.prototype._hasCrossedThreshold=function(t,e){var n=t&&t.isIntersecting?t.intersectionRatio||0:-1,o=e.isIntersecting?e.intersectionRatio||0:-1;if(n!==o)for(var r=0;r<this.thresholds.length;r++){var i=this.thresholds[r];if(i==n||i==o||i<n!=i<o)return!0}},s.prototype._rootIsInDom=function(){return!this.root||f(t,this.root)},s.prototype._rootContainsTarget=function(e){var n=this.root&&(this.root.ownerDocument||this.root)||t;return f(n,e)&&(!this.root||n==e.ownerDocument)},s.prototype._registerInstance=function(){e.indexOf(this)<0&&e.push(this)},s.prototype._unregisterInstance=function(){var t=e.indexOf(this);-1!=t&&e.splice(t,1)},window.IntersectionObserver=s,window.IntersectionObserverEntry=i}function r(t){try{return t.defaultView&&t.defaultView.frameElement||null}catch(t){return null}}function i(t){this.time=t.time,this.target=t.target,this.rootBounds=l(t.rootBounds),this.boundingClientRect=l(t.boundingClientRect),this.intersectionRect=l(t.intersectionRect||{top:0,bottom:0,left:0,right:0,width:0,height:0}),this.isIntersecting=!!t.intersectionRect;var e=this.boundingClientRect,n=e.width*e.height,o=this.intersectionRect,r=o.width*o.height;this.intersectionRatio=n?Number((r/n).toFixed(4)):this.isIntersecting?1:0}function s(t,e){var n=e||{};if("function"!=typeof t)throw new Error("callback must be a function");if(n.root&&1!=n.root.nodeType&&9!=n.root.nodeType)throw new Error("root must be a Document or Element");this._checkForIntersections=function(t,e){var n=null;return function(){n||(n=setTimeout((function(){t(),n=null}),e))}}(this._checkForIntersections.bind(this),this.THROTTLE_TIMEOUT),this._callback=t,this._observationTargets=[],this._queuedEntries=[],this._rootMarginValues=this._parseRootMargin(n.rootMargin),this.thresholds=this._initThresholds(n.threshold),this.root=n.root||null,this.rootMargin=this._rootMarginValues.map((function(t){return t.value+t.unit})).join(" "),this._monitoringDocuments=[],this._monitoringUnsubscribes=[]}function c(t,e,n,o){"function"==typeof t.addEventListener?t.addEventListener(e,n,o||!1):"function"==typeof t.attachEvent&&t.attachEvent("on"+e,n)}function u(t,e,n,o){"function"==typeof t.removeEventListener?t.removeEventListener(e,n,o||!1):"function"==typeof t.detatchEvent&&t.detatchEvent("on"+e,n)}function a(t){var e;try{e=t.getBoundingClientRect()}catch(t){}return e?(e.width&&e.height||(e={top:e.top,right:e.right,bottom:e.bottom,left:e.left,width:e.right-e.left,height:e.bottom-e.top}),e):{top:0,bottom:0,left:0,right:0,width:0,height:0}}function l(t){return!t||"x"in t?t:{top:t.top,y:t.top,bottom:t.bottom,left:t.left,x:t.left,right:t.right,width:t.width,height:t.height}}function h(t,e){var n=e.top-t.top,o=e.left-t.left;return{top:n,left:o,height:e.height,width:e.width,bottom:n+e.height,right:o+e.width}}function f(t,e){for(var n=e;n;){if(n==t)return!0;n=p(n)}return!1}function p(e){var n=e.parentNode;return 9==e.nodeType&&e!=t?r(e):(n&&n.assignedSlot&&(n=n.assignedSlot.parentNode),n&&11==n.nodeType&&n.host?n.host:n)}function d(t){return t&&9===t.nodeType}}(),null==Element.prototype.getAttributeNames&&(Element.prototype.getAttributeNames=function(){for(var t=this.attributes,e=t.length,n=new Array(e),o=0;o<e;o++)n[o]=t[o].name;return n});var b=function(t,e){if("function"==typeof t.getAttributeNames&&t.getAttributeNames().indexOf(e)>-1)return!0},y=function(t){return!!Object.prototype.hasOwnProperty.call(t.dataset,"webfunnyExposureEle")},v={},m=[],w="_webfunny-eo",_=function(){var t=document.getElementsByTagName("*");Array.prototype.forEach.call(t,(function(t,e){if(b(t,w)){m.push(t);var n=t.getAttribute(w);n&&(o=t,r=e,Object.prototype.hasOwnProperty.call(o.dataset,"exposureKey".concat(r))||(o.dataset["exposureKey".concat(r)]=""),Object.defineProperty(v,"data-exposure-key".concat(e),{value:JSON.parse(n),configureable:!0,writable:!0,enumerable:!0}))}var o,r})),m=m.reduce((function(t,e){return-1===t.indexOf(e)&&t.push(e),t}),[])},E=function(){_(),m.forEach((function(t){var e;y(t)||(e=t,Object.prototype.hasOwnProperty.call(e.dataset,"webfunnyExposureEle")||(e.dataset.webfunnyExposureEle=""),O(t))}))},O=function(t){var e=0;if(b(t,"_webfunny-eo-ratio")){var n=t.getAttribute("_webfunny-eo-ratio");e=Object.is(n,"0")?0:parseFloat(n)}new IntersectionObserver((function(e,n){e.forEach((function(e){if(e.intersectionRatio>0){var o=Object.keys(t.dataset).find((function(t){if(t.indexOf("exposureKey")>-1)return t}));if(o){var r="data-exposure-key"+o.slice(11),i=v[r];if(Object.prototype.hasOwnProperty.call(i,"pointId")){var s=i.pointId;Object.prototype.hasOwnProperty.call(window.webfunnyEvent,s)?(delete i.pointId,window.webfunnyEvent[s].trackEvent(i)):console.warn("请检查pointId上送的值是否正确")}else console.warn("pointId为必须上送字段，请检查是否正确上送")}else console.warn("曝光需要上送对应数据");n.disconnect()}}))}),{root:null,rootMargin:"0px",threshold:e}).observe(t)},I=function(){E(),new MutationObserver((function(t){for(var e=0,n=t.length;e<n;e++)t[e].addedNodes[0]&&b(t[e].addedNodes[0],w)&&E(),t[e].target.querySelector("[".concat(w,"]"))&&(y(t[e].target.querySelector("[".concat(w,"]")))||E()),t[e].attributeName===w&&t[e].target.attributes[w]&&E()})).observe(document.documentElement,{childList:!0,attributes:!0,subtree:!0})};!function(t){l&&g&&(t.webfunnyEventValidateParams=l,t.webfunnyEventSendRequest=g,t.webfunnyEvent={1:{fields:{userId:{required:true,type:'String',length:100},pageName:{required:true,type:'String',length:100},pagePath:{required:true,type:'String',length:512},terminal:{required:true,type:'String',length:50},},trackEvent(params){if(webfunnyEventValidateParams(params,this.fields)){const request={...params,projectId:'event101',pointId:'1'};webfunnyEventSendRequest(request)}}},}),I()}(window||global)}));
