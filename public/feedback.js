(function() {
  window.onload = function () {
    var btnDom = document.querySelector('div[data-id="custom-feedback-btn"]').querySelector('button')
    btnDom.style.transition = 'none'
    window.addEventListener('mouseup', function (e) {
      var text = window.getSelection().toString()
      if(text && window.custom_title !== text) {
        window.custom_title = text
        window.custom_userId = window.localStorage.getItem('customUserId')
        document.querySelector('div[data-id="custom-feedback-btn"]').style.display = 'block'
        document.querySelector('.configurable-box').style.position = 'static'
        btnDom.style.position = 'absolute'
        btnDom.style.top = e.pageY + 'px'
        btnDom.style.left = e.pageX + 'px'
      } else {
        document.querySelector('div[data-id="custom-feedback-btn"]').style.display = 'none'
      }
    })
  }
})()