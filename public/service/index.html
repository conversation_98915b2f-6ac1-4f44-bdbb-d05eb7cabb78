<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="content-type" content="text/html; charset=UTF-8" /> 
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <title>易宝客服</title>
  <style>
    * {
      padding: 0;
      margin: 0;
      box-sizing: border-box;
    }
    body {
      background: #F5F5F5;
      padding: 8px;
    }
    html, body, .content {
      height: 100%;
    }
    iframe {
      padding: 0;
      margin: 0;
    }
    .left {
      float: left;
      width: 67%;
      height: 100%;
    }
    .right {
      float: right;
      width: 33%;
      height: 100%;
      background-color: #fff;
      padding: 16px 20px 0;
    }
    .right .title {
      height: 24px;
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC,'Microsoft YaHei', SimSun, sans-serif;
      font-weight: 500;
      color: #000000;
      line-height: 24px;
      text-align: center;
      margin-bottom: 8px;
    }
    .right .img {
      width: 128px;
      height: 128px;
      margin: 0 auto;
      margin-bottom: 10px;
      background: url('https://img.yeepay.com/fe-resources/yop-docs/images/service/service.png') no-repeat center;
      background-size: 128px 128px;
    }
    .question {
      margin-top: 8px;
      margin-bottom: 16px;
      line-height: 24px;
      text-align: center;
    }
    .question .line {
      width: 20%;
      height: 1px;
      background: #E8E8E8;
      display: inline-block;
      vertical-align: middle;
    }
    .question-title  {
      width: 40%;
      height: 24px;
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC,'Microsoft YaHei', SimSun, sans-serif;
      font-weight: 500;
      color: #000000;
      display: inline-block;
      vertical-align: middle;
      white-space: nowrap;
    }
    .question-list{
      height: 14px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC,'Microsoft YaHei', SimSun, sans-serif;
      font-weight: 400;
      color: #52BF63;
      line-height: 14px;
      padding-left: 36px;
    }
    a {
      color: #52BF63;
      text-decoration: none;
      list-style: none;
    }
    a:hover {
      color: #6BC87A;
    }
    .question-item {
      margin-bottom: 16px;
    }
    .tool-item {
      display: inline-block;
      padding: 0 12px;
      height: 30px;
      line-height: 30px;
      background: rgba(0,0,0,0.0600);
      border-radius: 3px;
      margin-right: 8px;
      margin-bottom: 8px;
      font-size: 14px;
    }
    .tool-item a {
      width: 100%;
      height: 100%;
      color: rgba(0,0,0,0.6500);
      font-family: PingFangSC-Regular, PingFang SC;
    }
    .tool-item:hover {
      background: rgba(82,191,99,0.0600);
    }
    .tool-item:hover a {
      color: #52BF63;
    }
    .tool-item:nth-child(even) {
      margin-right: 0;
    }
  </style>
</head>
<body>
  <div class="content">
    <div class="left left-loading">
      <iframe width="100%" height="100%" src="https://openai.weixin.qq.com/webapp/biSH5CDT6Bp6nobfEy3vwqyleNXNAa?robotName=%E6%98%93%E5%AE%9D%E5%B0%8F%E6%98%93" frameborder="0"></iframe>
    </div>
    <div class="right">
      <div class="title">Hi~ 易宝小易为你服务</div>
      <div class="img"></div>
      <div class="question">
        <div class="line"></div>
        <div class="question-title">推荐工具</div>
        <div class="line"></div>
      </div>
      <div>
      <div class="tool-list">
        <div class="tool-item">
          <a target="block" href="https://mp.yeepay.com/auth/signin?redirectUrl=https://mp.yeepay.com/yop-developer-center/cas?redirectUrl=https://mp.yeepay.com/mp-developer-center/index.html#/dev-services/access-diagnosis">接入诊断</a>
        </div>
        <div class="tool-item">
          <a target="block" href="https://open.yeepay.com/docs/open/platform-doc/developTools-sm/keyTools-sm">密钥工具</a>
        </div>
        <div class="tool-item">
          <a target="block" href="https://open.yeepay.com/docs/open/platform-doc/developTools-sm/platform-sdk-sm">平台SDK</a>
        </div>
        <div class="tool-item">
          <a target="block" href="https://open.yeepay.com/docs/open/platform-doc/developTools-sm/developer_tools-vscode-sm">一站式开发套件</a>
        </div>
        <div class="tool-item">
          <a target="block" href="https://open.yeepay.com/docs/open/platform-doc/notifys/yop-isv-gateways-sm">结果通知工具</a>
        </div>
      </div>
      <div class="question">
        <div class="line"></div>
        <div class="question-title">常见问题</div>
        <div class="line"></div>
      </div>
      <ul class="question-list">
        <li class="question-item">
          <a target="block" href="https://open.yeepay.com/docs/platform/user-guide
          ">如何快速接入？</a>
        </li>
        <li class="question-item">
          <a target="block" href="https://open.yeepay.com/docs/platform/notify-summary/notify-summary
          ">什么是结果通知？</a>
        </li>
        <li class="question-item">
          <a target="block" href="https://open.yeepay.com/docs/platform/sdk_guide/sdk-guide">如何对接API？</a>
        </li>
        <li class="question-item">
          <a target="block" href="https://open.yeepay.com/docs/platform/sandbox_doc">如何使用沙箱？</a>
        </li>
      </ul>
    </div>
  </div>
</body>
</html>